
import React from 'react';
import TissueExplorer from './TissueExplorer';
import VirtualMicroscopeSim from './VirtualMicroscopeSim';
import Card from '../../common/Card';
import Quiz from '../../common/Quiz';
import { SAMPLE_QUIZ_QUESTIONS } from '../../../constants';

interface TissuesModuleProps {
  id: string;
}

const TissuesModule: React.FC<TissuesModuleProps> = ({ id }) => {
  const tissueQuizQuestions = SAMPLE_QUIZ_QUESTIONS.filter(q => q.id.includes('_tissue'));

  return (
    <section id={id} className="py-12 md:py-16 bg-gradient-to-b from-background/10 to-accent/10">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-primary mb-12 text-center">Module 2: Tissues – Organized Communities</h2>
        
        <Card title="Definition of Tissue" className="mb-12">
          <p className="text-lg text-textlight/90">
            Tissues are groups of similar cells that work together to perform a specific function. The human body is primarily composed of four basic types of tissue, each with unique characteristics and roles critical for overall bodily function and homeostasis.
          </p>
        </Card>

        <TissueExplorer />
        
        <div className="my-12">
          <Card title="Virtual Microscope Simulation">
            <VirtualMicroscopeSim />
          </Card>
        </div>

        <Card title="Animated Video Snippets (Placeholder)" className="my-8">
            <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">Placeholder for animated video snippets illustrating specific tissue functions (e.g., epithelial absorption, muscle contraction).</p>
            </div>
        </Card>

        <Quiz questions={tissueQuizQuestions} moduleId="tissues" />
      </div>
    </section>
  );
};

export default TissuesModule;
