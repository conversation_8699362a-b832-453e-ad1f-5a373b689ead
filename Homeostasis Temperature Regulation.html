<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Homeostasis Simulation: Temperature Regulation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f0f4f8; /* Light blue-grey background */
            color: #333;
            padding: 20px;
            box-sizing: border-box;
        }

        .app-container {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 700px;
            text-align: center;
        }

        h1 {
            color: #2c3e50; /* Darker blue-grey */
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .simulation-layout {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: space-around;
            align-items: flex-start;
        }

        .figure-panel, .info-panel {
            flex: 1;
            min-width: 280px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fdfdfd;
        }

        .figure-container {
            position: relative;
            width: 150px;
            height: 250px;
            margin: 20px auto;
            border: 2px dashed #bdc3c7; /* Light grey dashed border */
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .figure {
            position: relative;
            width: 60px; /* Increased size for better visibility */
            height: 150px;
        }

        .figure-head {
            width: 40px; height: 40px;
            background-color: #d2b48c; /* Tan skin tone */
            border-radius: 50%;
            position: absolute;
            top: 0; left: 10px;
            border: 1px solid #a08462;
        }

        .figure-torso {
            width: 40px; height: 70px;
            background-color: #d2b48c;
            position: absolute;
            top: 35px; left: 10px;
            border-radius: 5px;
            border: 1px solid #a08462;
        }
        
        .figure-arm, .figure-leg {
            background-color: #d2b48c;
            position: absolute;
            border: 1px solid #a08462;
            border-radius: 5px;
        }
        .figure-arm { width: 15px; height: 50px; }
        .figure-leg { width: 18px; height: 60px; }

        .figure-arm-left { top: 45px; left: -5px; transform-origin: top center; transform: rotate(20deg); }
        .figure-arm-right { top: 45px; left: 50px; transform-origin: top center; transform: rotate(-20deg); }
        .figure-leg-left { top: 100px; left: 8px; }
        .figure-leg-right { top: 100px; left: 34px; }

        .figure.shivering {
            animation: shiver 0.1s linear infinite;
        }

        @keyframes shiver {
            0% { transform: translate(1px, 0px); }
            25% { transform: translate(-1px, 0px); }
            50% { transform: translate(0px, 1px); }
            75% { transform: translate(0px, -1px); }
            100% { transform: translate(1px, 0px); }
        }

        .receptor-icon {
            position: absolute;
            top: 10px; left: 10px; /* On torso */
            width: 15px; height: 15px;
            background-color: #f39c12; /* Orange */
            color: white;
            border-radius: 50%;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0.2;
            transition: opacity 0.3s;
        }
        .receptor-icon.active {
            opacity: 1;
            animation: flash 0.7s infinite alternate;
        }
        @keyframes flash {
            from { box-shadow: 0 0 5px 2px #f39c12; }
            to { box-shadow: 0 0 10px 4px #f39c12aa; }
        }

        .sweat-droplet {
            position: absolute;
            font-size: 16px;
            color: #3498db; /* Blue for sweat */
            opacity: 0;
            transition: opacity 0.3s;
        }
        .sweat-droplet.active { opacity: 0.8; }
        #sweat1 { top: -5px; left: 5px; } /* Forehead */
        #sweat2 { top: 40px; left: -8px; } /* Armpit-ish */
        #sweat3 { top: 15px; left: 30px; } /* Chest/Neck */


        .hypothalamus-indicator {
            font-size: 0.9em;
            margin-top: 10px;
            padding: 5px 10px;
            border: 2px solid #ccc;
            border-radius: 5px;
            transition: border-color 0.3s, background-color 0.3s;
            background-color: #ecf0f1; /* Light grey */
        }
        .hypothalamus-indicator.active {
            border-color: #e67e22; /* Darker orange */
            background-color: #fdebd0; /* Light orange */
        }

        .temperature-display {
            font-size: 1.5em;
            margin-bottom: 15px;
            font-weight: bold;
            color: #34495e; /* Dark blue-grey */
        }
        #bodyTempReadout {
            min-width: 50px;
            display: inline-block;
        }

        .thermometer {
            width: 80%;
            max-width: 250px;
            margin: 0 auto 15px auto;
        }
        .thermometer-gauge {
            height: 200px;
            width: 30px;
            background-color: #e0e0e0;
            border: 2px solid #7f8c8d; /* Grey border */
            border-radius: 15px 15px 0 0;
            margin: 0 auto;
            position: relative;
            overflow: hidden; /* To contain the fill */
        }
        .thermometer-fill {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: #2ecc71; /* Green for normal */
            border-radius: 12px 12px 0 0; /* Match parent but slightly smaller */
            transition: height 0.3s ease-out, background-color 0.3s;
        }
        .thermometer-bulb {
            width: 50px;
            height: 50px;
            background-color: #7f8c8d; /* Match border color */
            border-radius: 50%;
            margin: -2px auto 0 auto; /* Overlap slightly with gauge */
            position: relative;
            z-index: 5;
        }
        .thermometer-bulb-fill {
            width: 100%;
            height: 100%;
            background-color: #2ecc71; /* Green for normal */
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .thermometer-normal-range {
            position: absolute;
            left: 0;
            width: 100%;
            background-color: rgba(46, 204, 113, 0.2); /* Transparent green */
            border-top: 1px dashed #16a085; /* Darker green */
            border-bottom: 1px dashed #16a085;
            z-index: 1; /* Below fill potentially, or just part of track */
        }
        .thermometer-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.8em;
            padding: 0 5px; /* Align with gauge edges */
            margin-top: 5px;
            color: #7f8c8d;
        }
         .thermometer-labels span:nth-child(2) { color: #16a085; font-weight: bold; } /* Normal min */
         .thermometer-labels span:nth-child(3) { color: #16a085; font-weight: bold; } /* Normal max */


        .controls {
            margin-top: 25px;
            padding: 15px;
            border-top: 1px solid #e0e0e0;
        }
        .controls label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        #externalTempSlider {
            width: 80%;
            max-width: 300px;
            margin-top: 5px;
        }
        #externalTempReadout {
            font-weight: bold;
            color: #2980b9; /* Blue */
        }

        .status-message {
            margin-top: 15px;
            font-size: 1.1em;
            font-weight: bold;
            padding: 10px;
            border-radius: 5px;
        }
        .status-message.hypothermia { color: #3498db; background-color: #eaf5ff; border: 1px solid #a9cce3; } /* Blue */
        .status-message.heatstroke { color: #e74c3c; background-color: #fdedec; border: 1px solid #f5b7b1; } /* Red */
        .status-message.normal { color: #27ae60; background-color: #e8f8f5; border: 1px solid #a3e4d7; } /* Green */


        @media (max-width: 600px) {
            .simulation-layout {
                flex-direction: column;
                align-items: stretch;
            }
            .figure-panel, .info-panel {
                min-width: unset;
                width: 100%;
            }
            h1 { font-size: 1.5em; }
            .temperature-display { font-size: 1.3em; }
        }

    </style>
</head>
<body>
    <div class="app-container">
        <h1>Homeostasis: Temperature Regulation</h1>
        <p>Adjust the external temperature and observe how the body tries to maintain its internal temperature (around 37.0°C).</p>

        <div class="simulation-layout">
            <div class="figure-panel">
                <h3>Visual Representation</h3>
                <div class="figure-container">
                    <div id="figure" class="figure">
                        <div class="figure-head"></div>
                        <div class="figure-torso">
                            <div id="receptor" class="receptor-icon">R</div>
                        </div>
                        <div class="figure-arm figure-arm-left"></div>
                        <div class="figure-arm figure-arm-right"></div>
                        <div class="figure-leg figure-leg-left"></div>
                        <div class="figure-leg figure-leg-right"></div>
                        
                        <span class="sweat-droplet" id="sweat1">💧</span>
                        <span class="sweat-droplet" id="sweat2">💧</span>
                        <span class="sweat-droplet" id="sweat3">💧</span>
                    </div>
                </div>
                <div id="hypothalamusIndicator" class="hypothalamus-indicator">Thermoregulatory Center (Hypothalamus)</div>
            </div>

            <div class="info-panel">
                <h3>Body State</h3>
                <div class="temperature-display">
                    Body Temp: <span id="bodyTempReadout">37.0</span>°C
                </div>
                <div class="thermometer">
                    <div class="thermometer-gauge">
                        <div id="thermometerFill" class="thermometer-fill"></div>
                        <div id="thermometerNormalRange" class="thermometer-normal-range"></div>
                    </div>
                    <div class="thermometer-bulb"><div id="thermometerBulbFill" class="thermometer-bulb-fill"></div></div>
                    <div class="thermometer-labels">
                        <span id="gaugeMinLabel">34°C</span>
                        <span id="gaugeNormalMinLabel">36.5°C</span>
                        <span id="gaugeNormalMaxLabel">37.5°C</span>
                        <span id="gaugeMaxLabel">41°C</span>
                    </div>
                </div>
                <div id="statusMessage" class="status-message">Maintaining stability...</div>
            </div>
        </div>

        <div class="controls">
            <label for="externalTempSlider">External Temperature: <span id="externalTempReadout">20.0</span>°C</label>
            <input type="range" id="externalTempSlider" min="-10" max="50" value="20" step="0.5">
        </div>
    </div>

    <script>
        // DOM Elements
        const bodyTempReadout = document.getElementById('bodyTempReadout');
        const externalTempSlider = document.getElementById('externalTempSlider');
        const externalTempReadout = document.getElementById('externalTempReadout');
        const figureElement = document.getElementById('figure');
        const receptorIcon = document.getElementById('receptor');
        const hypothalamusIndicator = document.getElementById('hypothalamusIndicator');
        const sweatDroplets = [
            document.getElementById('sweat1'),
            document.getElementById('sweat2'),
            document.getElementById('sweat3')
        ];
        const thermometerFill = document.getElementById('thermometerFill');
        const thermometerBulbFill = document.getElementById('thermometerBulbFill');
        const thermometerNormalRange = document.getElementById('thermometerNormalRange');
        const statusMessage = document.getElementById('statusMessage');

        // Constants for Homeostasis
        const SET_POINT = 37.0;
        const NORMAL_TEMP_MIN = 36.5;
        const NORMAL_TEMP_MAX = 37.5;
        const HYPOTHERMIA_THRESHOLD = 35.0;
        const HEATSTROKE_THRESHOLD = 40.0;

        // Thermometer Gauge Visual Constants
        const GAUGE_TEMP_MIN = 34.0;
        const GAUGE_TEMP_MAX = 41.0;

        // Simulation Parameters
        const UPDATE_INTERVAL = 200; // ms
        const EXTERNAL_INFLUENCE_FACTOR = 0.018; 
        const SWEATING_EFFECTIVENESS = 0.08; 
        const SHIVERING_EFFECTIVENESS = 0.08;
        const RECEPTOR_SENSITIVITY_THRESHOLD = 0.3; // Temp diff from SET_POINT to activate receptors visually
        const SIMULATION_BODY_TEMP_MIN = 30.0; // Absolute min for simulation
        const SIMULATION_BODY_TEMP_MAX = 44.0; // Absolute max for simulation

        // State Variables
        let bodyTemperature = SET_POINT;
        let currentExternalTemp = parseFloat(externalTempSlider.value);
        let isShivering = false;
        let isSweating = false;
        let receptorActive = false;
        let hypothalamusActive = false;
        let homeostasisFailed = null; // null, "Hypothermia", "Heatstroke"

        function updateUI() {
            // Body Temperature Readout
            bodyTempReadout.textContent = bodyTemperature.toFixed(1);

            // Figure appearance
            figureElement.classList.toggle('shivering', isShivering && !homeostasisFailed);
            
            sweatDroplets.forEach(droplet => {
                droplet.classList.toggle('active', isSweating && !homeostasisFailed);
            });

            // Receptor and Hypothalamus indicators
            receptorIcon.classList.toggle('active', receptorActive);
            hypothalamusIndicator.classList.toggle('active', hypothalamusActive);
            if (hypothalamusActive) {
                hypothalamusIndicator.textContent = "Thermoregulatory Center: ACTIVE";
            } else {
                hypothalamusIndicator.textContent = "Thermoregulatory Center: Idle";
            }
            

            // Thermometer Fill
            const tempRangeOnGauge = GAUGE_TEMP_MAX - GAUGE_TEMP_MIN;
            let fillPercent = ((bodyTemperature - GAUGE_TEMP_MIN) / tempRangeOnGauge) * 100;
            fillPercent = Math.max(0, Math.min(100, fillPercent)); // Clamp between 0 and 100%
            thermometerFill.style.height = `${fillPercent}%`;

            // Thermometer Fill Color
            let fillColor = '#2ecc71'; // Green for normal
            if (bodyTemperature < NORMAL_TEMP_MIN) {
                fillColor = '#3498db'; // Blue for cold
            } else if (bodyTemperature > NORMAL_TEMP_MAX) {
                fillColor = '#e74c3c'; // Red for hot
            }
            if (homeostasisFailed === "Hypothermia") fillColor = '#5dade2'; // Lighter blue for severe cold
            if (homeostasisFailed === "Heatstroke") fillColor = '#c0392b'; // Darker red for severe heat
            
            thermometerFill.style.backgroundColor = fillColor;
            thermometerBulbFill.style.backgroundColor = fillColor;


            // Status Message
            statusMessage.classList.remove('hypothermia', 'heatstroke', 'normal');
            if (homeostasisFailed) {
                statusMessage.textContent = `${homeostasisFailed} detected! Homeostasis compromised.`;
                statusMessage.classList.add(homeostasisFailed.toLowerCase());
            } else if (isShivering) {
                statusMessage.textContent = "Body is shivering to generate heat...";
                statusMessage.classList.add('normal'); // Or a specific 'warning' class
            } else if (isSweating) {
                statusMessage.textContent = "Body is sweating to cool down...";
                statusMessage.classList.add('normal');
            } else if (bodyTemperature >= NORMAL_TEMP_MIN && bodyTemperature <= NORMAL_TEMP_MAX) {
                statusMessage.textContent = "Body temperature stable and in normal range.";
                statusMessage.classList.add('normal');
            } else {
                statusMessage.textContent = "Monitoring body temperature...";
                 statusMessage.classList.add('normal'); // Default neutral
            }
        }
        
        function setupThermometerVisuals() {
            const tempRangeOnGauge = GAUGE_TEMP_MAX - GAUGE_TEMP_MIN;
            
            // Normal Range Marker
            const normalRangeHeight = ((NORMAL_TEMP_MAX - NORMAL_TEMP_MIN) / tempRangeOnGauge) * 100;
            const normalRangeBottom = ((NORMAL_TEMP_MIN - GAUGE_TEMP_MIN) / tempRangeOnGauge) * 100;
            thermometerNormalRange.style.height = `${normalRangeHeight}%`;
            thermometerNormalRange.style.bottom = `${normalRangeBottom}%`;

            // Labels
            document.getElementById('gaugeMinLabel').textContent = `${GAUGE_TEMP_MIN.toFixed(1)}°C`;
            document.getElementById('gaugeNormalMinLabel').textContent = `${NORMAL_TEMP_MIN.toFixed(1)}°C`;
            document.getElementById('gaugeNormalMaxLabel').textContent = `${NORMAL_TEMP_MAX.toFixed(1)}°C`;
            document.getElementById('gaugeMaxLabel').textContent = `${GAUGE_TEMP_MAX.toFixed(1)}°C`;
        }


        function updateState() {
            currentExternalTemp = parseFloat(externalTempSlider.value);

            // 1. Apply external temperature influence
            let bodyTempDeltaFromExternal = (currentExternalTemp - bodyTemperature) * EXTERNAL_INFLUENCE_FACTOR;
            bodyTemperature += bodyTempDeltaFromExternal;

            // 2. Homeostatic response (if not already failed)
            let effectorAdjustment = 0;
            if (!homeostasisFailed) {
                if (bodyTemperature > NORMAL_TEMP_MAX) {
                    isSweating = true;
                    isShivering = false;
                } else if (bodyTemperature < NORMAL_TEMP_MIN) {
                    isShivering = true;
                    isSweating = false;
                } else { // Within normal range or returning
                    isSweating = false;
                    isShivering = false;
                }

                if (isSweating) {
                    effectorAdjustment = -SWEATING_EFFECTIVENESS;
                }
                if (isShivering) {
                    effectorAdjustment = SHIVERING_EFFECTIVENESS;
                }
                bodyTemperature += effectorAdjustment;
            } else { // Homeostasis has failed, effectors stop
                isSweating = false;
                isShivering = false;
            }
            
            // 3. Receptor and Hypothalamus activation (visual cues)
            receptorActive = homeostasisFailed || (Math.abs(bodyTemperature - SET_POINT) > RECEPTOR_SENSITIVITY_THRESHOLD);
            hypothalamusActive = homeostasisFailed || isSweating || isShivering || receptorActive;


            // 4. Check for homeostatic failure (if not already failed)
            if (!homeostasisFailed) {
                if (bodyTemperature < HYPOTHERMIA_THRESHOLD) {
                    homeostasisFailed = "Hypothermia";
                    isSweating = false; isShivering = false; // Ensure effectors stop immediately
                } else if (bodyTemperature > HEATSTROKE_THRESHOLD) {
                    homeostasisFailed = "Heatstroke";
                    isSweating = false; isShivering = false; // Ensure effectors stop immediately
                }
            }
            
            // 5. Clamp body temperature to a plausible physiological range for simulation
            bodyTemperature = Math.max(SIMULATION_BODY_TEMP_MIN, Math.min(SIMULATION_BODY_TEMP_MAX, bodyTemperature));

            // 6. Update UI
            updateUI();
        }

        // Event Listeners
        externalTempSlider.addEventListener('input', (event) => {
            externalTempReadout.textContent = parseFloat(event.target.value).toFixed(1);
            // If homeostasis failed, allow a "reset" if user changes temp to something manageable
            // For this simple model, failure is persistent unless explicitly reset.
            // To allow recovery:
            // if (homeostasisFailed && Math.abs(parseFloat(event.target.value) - SET_POINT) < 10) { // Example condition
            //     homeostasisFailed = null; 
            //     bodyTemperature = SET_POINT; // Or let it recover naturally
            // }
        });

        // Initial Setup
        function init() {
            externalTempReadout.textContent = currentExternalTemp.toFixed(1);
            setupThermometerVisuals();
            updateUI(); // Initial UI render based on default state
            setInterval(updateState, UPDATE_INTERVAL);
        }

        init();
    </script>
</body>
</html>
