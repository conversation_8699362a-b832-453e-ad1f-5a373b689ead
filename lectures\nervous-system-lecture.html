<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nervous System - Dynamic Animated Lecture | A&P for BME</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/animations.css">
    <link rel="stylesheet" href="../css/lecture.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation Header -->
    <header class="lecture-header">
        <nav class="lecture-nav">
            <div class="nav-container">
                <div class="nav-logo">
                    <i class="fas fa-heartbeat"></i>
                    <span>A&P for BME</span>
                </div>
                <div class="lecture-title">
                    <h1>Nervous System - Dynamic Lecture</h1>
                </div>
                <div class="nav-actions">
                    <a href="../index.html" class="btn-home">
                        <i class="fas fa-home"></i> Return Home
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Lecture Progress Bar -->
    <div class="lecture-progress">
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        <div class="progress-info">
            <span id="currentSlide">1</span> / <span id="totalSlides">14</span>
        </div>
    </div>

    <!-- Lecture Content -->
    <main class="lecture-main">
        <div class="lecture-container">
            
            <!-- Slide 1: Introduction -->
            <div class="lecture-slide active" data-slide="1">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2 class="slide-title">Nervous System</h2>
                        <p class="slide-subtitle">Control and Communication Network</p>
                    </div>
                    <div class="slide-body">
                        <div class="content-grid">
                            <div class="content-text">
                                <h3>Learning Objectives</h3>
                                <ul class="animated-list">
                                    <li class="fade-in-up" style="animation-delay: 0.2s;">Understand nervous system organization</li>
                                    <li class="fade-in-up" style="animation-delay: 0.4s;">Explore neuron structure and function</li>
                                    <li class="fade-in-up" style="animation-delay: 0.6s;">Analyze synaptic transmission</li>
                                    <li class="fade-in-up" style="animation-delay: 0.8s;">Study neural pathways</li>
                                    <li class="fade-in-up" style="animation-delay: 1.0s;">Examine neural engineering applications</li>
                                </ul>
                            </div>
                            <div class="content-visual">
                                <div class="nervous-system-overview">
                                    <div class="brain-model">
                                        <div class="cerebrum">
                                            <div class="neural-activity"></div>
                                        </div>
                                        <div class="brainstem"></div>
                                        <div class="spinal-cord">
                                            <div class="nerve-signals"></div>
                                        </div>
                                        <div class="peripheral-nerves">
                                            <div class="nerve-branch left"></div>
                                            <div class="nerve-branch right"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2: Nervous System Organization -->
            <div class="lecture-slide" data-slide="2">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2 class="slide-title">Nervous System Organization</h2>
                        <p class="slide-subtitle">Central and Peripheral Divisions</p>
                    </div>
                    <div class="slide-body">
                        <div class="ns-organization">
                            <div class="organization-chart">
                                <div class="main-division">
                                    <h3>Nervous System</h3>
                                    <div class="division-branches">
                                        <div class="division cns">
                                            <h4>Central Nervous System (CNS)</h4>
                                            <div class="cns-components">
                                                <div class="component brain-component">
                                                    <div class="component-icon">🧠</div>
                                                    <h5>Brain</h5>
                                                    <ul>
                                                        <li>Cerebrum</li>
                                                        <li>Cerebellum</li>
                                                        <li>Brainstem</li>
                                                    </ul>
                                                </div>
                                                <div class="component spinal-component">
                                                    <div class="component-icon">🦴</div>
                                                    <h5>Spinal Cord</h5>
                                                    <ul>
                                                        <li>Gray matter</li>
                                                        <li>White matter</li>
                                                        <li>Spinal nerves</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="division pns">
                                            <h4>Peripheral Nervous System (PNS)</h4>
                                            <div class="pns-components">
                                                <div class="component somatic-component">
                                                    <div class="component-icon">💪</div>
                                                    <h5>Somatic</h5>
                                                    <ul>
                                                        <li>Motor neurons</li>
                                                        <li>Sensory neurons</li>
                                                        <li>Voluntary control</li>
                                                    </ul>
                                                </div>
                                                <div class="component autonomic-component">
                                                    <div class="component-icon">❤️</div>
                                                    <h5>Autonomic</h5>
                                                    <ul>
                                                        <li>Sympathetic</li>
                                                        <li>Parasympathetic</li>
                                                        <li>Enteric</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3: Neuron Structure -->
            <div class="lecture-slide" data-slide="3">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2 class="slide-title">Neuron Structure</h2>
                        <p class="slide-subtitle">Specialized Cells for Signal Transmission</p>
                    </div>
                    <div class="slide-body">
                        <div class="neuron-anatomy">
                            <div class="neuron-diagram">
                                <div class="neuron-parts">
                                    <div class="cell-body">
                                        <div class="nucleus"></div>
                                        <div class="organelles">
                                            <div class="mitochondria"></div>
                                            <div class="ribosomes"></div>
                                        </div>
                                        <div class="part-label">Cell Body (Soma)</div>
                                    </div>
                                    
                                    <div class="dendrites">
                                        <div class="dendrite-branch"></div>
                                        <div class="dendrite-branch"></div>
                                        <div class="dendrite-branch"></div>
                                        <div class="part-label">Dendrites</div>
                                    </div>
                                    
                                    <div class="axon">
                                        <div class="axon-hillock"></div>
                                        <div class="axon-shaft">
                                            <div class="myelin-segments">
                                                <div class="myelin-segment"></div>
                                                <div class="node-ranvier"></div>
                                                <div class="myelin-segment"></div>
                                                <div class="node-ranvier"></div>
                                                <div class="myelin-segment"></div>
                                            </div>
                                        </div>
                                        <div class="part-label">Axon</div>
                                    </div>
                                    
                                    <div class="axon-terminals">
                                        <div class="terminal-branch"></div>
                                        <div class="terminal-branch"></div>
                                        <div class="synaptic-buttons">
                                            <div class="synaptic-button"></div>
                                            <div class="synaptic-button"></div>
                                        </div>
                                        <div class="part-label">Axon Terminals</div>
                                    </div>
                                </div>
                                
                                <div class="signal-flow">
                                    <div class="signal-arrow">
                                        <span>Signal Direction</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="neuron-functions">
                                <h3>Neuron Functions</h3>
                                <div class="function-list">
                                    <div class="function-item">
                                        <div class="function-icon">📡</div>
                                        <h4>Reception</h4>
                                        <p>Dendrites receive signals from other neurons</p>
                                    </div>
                                    <div class="function-item">
                                        <div class="function-icon">⚡</div>
                                        <h4>Integration</h4>
                                        <p>Cell body processes incoming signals</p>
                                    </div>
                                    <div class="function-item">
                                        <div class="function-icon">🚀</div>
                                        <h4>Transmission</h4>
                                        <p>Axon conducts signals to target cells</p>
                                    </div>
                                    <div class="function-item">
                                        <div class="function-icon">🔗</div>
                                        <h4>Communication</h4>
                                        <p>Terminals release neurotransmitters</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 4: Synaptic Transmission -->
            <div class="lecture-slide" data-slide="4">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2 class="slide-title">Synaptic Transmission</h2>
                        <p class="slide-subtitle">Chemical Communication Between Neurons</p>
                    </div>
                    <div class="slide-body">
                        <div class="synapse-demo">
                            <div class="synapse-structure">
                                <div class="presynaptic-neuron">
                                    <h4>Presynaptic Neuron</h4>
                                    <div class="axon-terminal">
                                        <div class="synaptic-vesicles">
                                            <div class="vesicle">NT</div>
                                            <div class="vesicle">NT</div>
                                            <div class="vesicle">NT</div>
                                        </div>
                                        <div class="calcium-channels">
                                            <div class="ca-channel">Ca²⁺</div>
                                        </div>
                                        <div class="action-potential-wave"></div>
                                    </div>
                                </div>
                                
                                <div class="synaptic-cleft">
                                    <h4>Synaptic Cleft</h4>
                                    <div class="neurotransmitter-molecules">
                                        <div class="nt-molecule">ACh</div>
                                        <div class="nt-molecule">ACh</div>
                                        <div class="nt-molecule">ACh</div>
                                    </div>
                                    <div class="cleft-width">20-50 nm</div>
                                </div>
                                
                                <div class="postsynaptic-neuron">
                                    <h4>Postsynaptic Neuron</h4>
                                    <div class="postsynaptic-membrane">
                                        <div class="receptors">
                                            <div class="receptor">R</div>
                                            <div class="receptor">R</div>
                                            <div class="receptor">R</div>
                                        </div>
                                        <div class="ion-channels">
                                            <div class="ligand-gated-channel">
                                                <div class="channel-pore"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="transmission-steps">
                                <h3>Transmission Process</h3>
                                <div class="steps-timeline">
                                    <div class="step">
                                        <div class="step-number">1</div>
                                        <h5>Action Potential Arrival</h5>
                                        <p>Depolarization reaches axon terminal</p>
                                    </div>
                                    <div class="step">
                                        <div class="step-number">2</div>
                                        <h5>Calcium Influx</h5>
                                        <p>Voltage-gated Ca²⁺ channels open</p>
                                    </div>
                                    <div class="step">
                                        <div class="step-number">3</div>
                                        <h5>Vesicle Fusion</h5>
                                        <p>Neurotransmitter release by exocytosis</p>
                                    </div>
                                    <div class="step">
                                        <div class="step-number">4</div>
                                        <h5>Receptor Binding</h5>
                                        <p>Neurotransmitter binds to receptors</p>
                                    </div>
                                    <div class="step">
                                        <div class="step-number">5</div>
                                        <h5>Signal Generation</h5>
                                        <p>Postsynaptic potential generated</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 5: Neural Pathways -->
            <div class="lecture-slide" data-slide="5">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2 class="slide-title">Neural Pathways</h2>
                        <p class="slide-subtitle">Information Processing Networks</p>
                    </div>
                    <div class="slide-body">
                        <div class="neural-pathways">
                            <div class="pathway-types">
                                <div class="pathway sensory-pathway">
                                    <h3>Sensory Pathway</h3>
                                    <div class="pathway-diagram">
                                        <div class="pathway-component receptor">
                                            <div class="component-icon">👁️</div>
                                            <span>Sensory Receptor</span>
                                        </div>
                                        <div class="pathway-arrow">→</div>
                                        <div class="pathway-component afferent">
                                            <div class="component-icon">📡</div>
                                            <span>Afferent Neuron</span>
                                        </div>
                                        <div class="pathway-arrow">→</div>
                                        <div class="pathway-component integration">
                                            <div class="component-icon">🧠</div>
                                            <span>CNS Integration</span>
                                        </div>
                                    </div>
                                    <div class="pathway-function">
                                        <p><strong>Function:</strong> Carries sensory information to CNS</p>
                                        <p><strong>Example:</strong> Vision, touch, hearing</p>
                                    </div>
                                </div>
                                
                                <div class="pathway motor-pathway">
                                    <h3>Motor Pathway</h3>
                                    <div class="pathway-diagram">
                                        <div class="pathway-component motor-cortex">
                                            <div class="component-icon">🧠</div>
                                            <span>Motor Cortex</span>
                                        </div>
                                        <div class="pathway-arrow">→</div>
                                        <div class="pathway-component efferent">
                                            <div class="component-icon">⚡</div>
                                            <span>Efferent Neuron</span>
                                        </div>
                                        <div class="pathway-arrow">→</div>
                                        <div class="pathway-component effector">
                                            <div class="component-icon">💪</div>
                                            <span>Effector Organ</span>
                                        </div>
                                    </div>
                                    <div class="pathway-function">
                                        <p><strong>Function:</strong> Carries motor commands from CNS</p>
                                        <p><strong>Example:</strong> Voluntary movement, reflexes</p>
                                    </div>
                                </div>
                                
                                <div class="pathway reflex-pathway">
                                    <h3>Reflex Arc</h3>
                                    <div class="pathway-diagram">
                                        <div class="reflex-components">
                                            <div class="reflex-receptor">Receptor</div>
                                            <div class="reflex-sensory">Sensory Neuron</div>
                                            <div class="reflex-integration">Integration Center</div>
                                            <div class="reflex-motor">Motor Neuron</div>
                                            <div class="reflex-effector">Effector</div>
                                        </div>
                                        <div class="reflex-signal">
                                            <div class="signal-path"></div>
                                        </div>
                                    </div>
                                    <div class="pathway-function">
                                        <p><strong>Function:</strong> Rapid, automatic responses</p>
                                        <p><strong>Example:</strong> Knee-jerk reflex, withdrawal reflex</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </main>

    <!-- Lecture Controls -->
    <div class="lecture-controls">
        <button class="control-btn" id="prevBtn">
            <i class="fas fa-chevron-left"></i> Previous
        </button>
        <div class="slide-indicators" id="slideIndicators">
            <!-- Dynamically generated -->
        </div>
        <button class="control-btn" id="nextBtn">
            Next <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <!-- Lecture Sidebar -->
    <div class="lecture-sidebar" id="lectureSidebar">
        <div class="sidebar-header">
            <h3>Lecture Outline</h3>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        <div class="sidebar-content">
            <ul class="lecture-outline">
                <li class="outline-item active" data-slide="1">
                    <span class="outline-number">1</span>
                    <span class="outline-title">Introduction</span>
                </li>
                <li class="outline-item" data-slide="2">
                    <span class="outline-number">2</span>
                    <span class="outline-title">Organization</span>
                </li>
                <li class="outline-item" data-slide="3">
                    <span class="outline-number">3</span>
                    <span class="outline-title">Neuron Structure</span>
                </li>
                <li class="outline-item" data-slide="4">
                    <span class="outline-number">4</span>
                    <span class="outline-title">Synaptic Transmission</span>
                </li>
                <li class="outline-item" data-slide="5">
                    <span class="outline-number">5</span>
                    <span class="outline-title">Neural Pathways</span>
                </li>
            </ul>
        </div>
    </div>

    <!-- Author Information -->
    <div class="lecture-footer">
        <div class="author-info">
            <p><strong>Dr. Mohammed Yagoub Esmail</strong> | SUST BME @2025</p>
            <p><EMAIL> | +249912867327 | +966538076790</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../js/main.js"></script>
    <script src="../js/lecture.js"></script>
</body>
</html>
