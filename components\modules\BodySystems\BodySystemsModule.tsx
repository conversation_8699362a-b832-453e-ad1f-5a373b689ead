
import React from 'react';
import InteractiveHumanBodyMap from './InteractiveHumanBodyMap';
import Card from '../../common/Card';
import Quiz from '../../common/Quiz';
import { BODY_SYSTEMS_DATA, SAMPLE_QUIZ_QUESTIONS } from '../../../constants';

interface BodySystemsModuleProps {
  id: string;
}

const BodySystemsModule: React.FC<BodySystemsModuleProps> = ({ id }) => {
  // Placeholder for system-specific quiz questions
  const systemQuizQuestions = SAMPLE_QUIZ_QUESTIONS.slice(0,2); 

  return (
    <section id={id} className="py-12 md:py-16 bg-gradient-to-b from-background/10 to-accent/10">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-primary mb-12 text-center">Module 4: Body Systems – Orchestrating Life</h2>
        
        <InteractiveHumanBodyMap systems={BODY_SYSTEMS_DATA} />

        <Card title="Animated Flowcharts/Videos (Placeholder)" className="my-12">
          <p className="text-lg text-textlight/90 mb-4">
            For each system, a short animated video or interactive flowchart would illustrate a key physiological process. Examples:
          </p>
          <ul className="list-disc list-inside text-textlight/80 space-y-1">
            <li>Cardiovascular: Blood circulation path.</li>
            <li>Respiratory: Gas exchange in the lungs.</li>
            <li>Nervous: Nerve impulse pathway.</li>
            <li>Digestive: Process of digestion and absorption.</li>
          </ul>
          <div className="mt-6 aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg flex items-center justify-center">
              <p className="text-gray-500">Placeholder for system process animations.</p>
          </div>
        </Card>
        
        <Quiz questions={systemQuizQuestions} moduleId="body-systems" />
      </div>
    </section>
  );
};

export default BodySystemsModule;
