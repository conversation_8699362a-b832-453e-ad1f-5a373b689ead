
import React from 'react';
import Button from '../common/Button';

interface HeroSectionProps {
  id: string;
  onBeginJourney: () => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ id, onBeginJourney }) => {
  return (
    <section id={id} className="min-h-[calc(100vh-4rem)] flex flex-col items-center justify-center text-center p-6 bg-cover bg-center relative" style={{backgroundImage: "url('https://picsum.photos/seed/hero-bg/1920/1080')"}}>
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm"></div> {/* Overlay for text readability */}
      <div className="relative z-10 animate-fadeInUp max-w-3xl">
        <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
          Anatomy & Physiology for Biomedical Engineers: <span className="text-accent">Unlocking the Human Machine</span>
        </h1>
        <p className="text-xl md:text-2xl text-gray-200 mb-10">
          Explore the fundamental building blocks and intricate systems that govern life, from a biomedical engineering perspective.
        </p>
        <Button onClick={onBeginJourney} variant="secondary" size="lg" className="shadow-xl hover:shadow-2xl transform hover:scale-105">
          Begin Your Journey
        </Button>
        <p className="mt-8 text-sm text-gray-300">
          An interactive learning experience designed to deepen your understanding.
        </p>
      </div>
      {/* Placeholder for "captivating, subtle animation of a human body transitioning from cellular level to organ systems" */}
      {/* Could be a Lottie animation, a GIF, or a CSS keyframe animation here */}
      <div className="absolute bottom-10 w-full flex justify-center">
        <div className="w-8 h-12 border-2 border-white rounded-full p-1">
          <div className="w-1 h-3 bg-white rounded-full animate-bounce_custom_hero"></div>
        </div>
      </div>
      <style>{`
        @keyframes bounce_custom_hero {
          0%, 100% { transform: translateY(-25%); animation-timing-function: cubic-bezier(0.8,0,1,1); }
          50% { transform: translateY(0); animation-timing-function: cubic-bezier(0,0,0.2,1); }
        }
        .animate-bounce_custom_hero { animation: bounce_custom_hero 1.5s infinite; }
      `}</style>
    </section>
  );
};

export default HeroSection;
