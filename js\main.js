// ===== MAIN JAVASCRIPT FOR INTERACTIVE A&P PLATFORM =====

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initNavigation();
    initSidebar();
    initScrollAnimations();
    initModuleFilters();
    initProgressTracking();
    initVideoPlayers();
    initStatCounters();
    initSmoothScrolling();
});

// ===== NAVIGATION =====
function initNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Toggle mobile menu
    if (hamburger) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking on a link
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // Update active nav link on scroll
    window.addEventListener('scroll', updateActiveNavLink);
}

// ===== SIDEBAR NAVIGATION =====
function initSidebar() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebarClose = document.getElementById('sidebarClose');
    const sidebar = document.getElementById('navSidebar');
    const overlay = document.getElementById('sidebarOverlay');

    // Open sidebar
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.add('active');
            overlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        });
    }

    // Close sidebar
    function closeSidebar() {
        sidebar.classList.remove('active');
        overlay.classList.remove('active');
        document.body.style.overflow = '';
    }

    if (sidebarClose) {
        sidebarClose.addEventListener('click', closeSidebar);
    }

    if (overlay) {
        overlay.addEventListener('click', closeSidebar);
    }

    // Close sidebar on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && sidebar.classList.contains('active')) {
            closeSidebar();
        }
    });

    // Close sidebar when clicking on a link
    const sidebarLinks = document.querySelectorAll('.sidebar-links a');
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function() {
            // Add a small delay to allow navigation to start
            setTimeout(closeSidebar, 100);
        });
    });
}

function updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (window.pageYOffset >= sectionTop - 200) {
            current = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
}

// ===== SCROLL ANIMATIONS =====
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('in-view');
            }
        });
    }, observerOptions);

    // Observe all elements with animation classes
    const animatedElements = document.querySelectorAll('.animate-on-scroll, .module-card, .simulation-card, .video-card, .feature-item, .stat-item');
    animatedElements.forEach(el => {
        el.classList.add('animate-on-scroll');
        observer.observe(el);
    });
}

// ===== MODULE FILTERS =====
function initModuleFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const moduleCards = document.querySelectorAll('.module-card');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter modules
            moduleCards.forEach(card => {
                if (filter === 'all' || card.getAttribute('data-category') === filter) {
                    card.style.display = 'block';
                    card.style.animation = 'fadeInUp 0.5s ease-out';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });
}

// ===== PROGRESS TRACKING =====
function initProgressTracking() {
    // Simulate progress tracking (in a real app, this would come from a backend)
    const progressData = {
        'foundation': 25,
        'systems': 60,
        'physiology': 40,
        'movement': 80,
        'structure': 15,
        'terminology': 90,
        'electrophysiology': 0
    };

    // Update progress bars
    Object.keys(progressData).forEach(category => {
        const moduleCard = document.querySelector(`[data-category="${category}"]`);
        if (moduleCard) {
            const progressFill = moduleCard.querySelector('.progress-fill');
            const progressText = moduleCard.querySelector('.progress-text');
            
            if (progressFill && progressText) {
                const progress = progressData[category];
                progressFill.style.width = `${progress}%`;
                progressFill.className = `progress-fill progress-${Math.floor(progress / 10) * 10}`;
                progressText.textContent = `${progress}% Complete`;
            }
        }
    });
}

// ===== VIDEO PLAYERS =====
function initVideoPlayers() {
    const videoCards = document.querySelectorAll('.video-card');

    videoCards.forEach(card => {
        // Add click event to entire card
        card.addEventListener('click', function() {
            const videoUrl = card.dataset.videoUrl;
            const videoTitle = card.querySelector('h3').textContent;
            const videoDescription = card.querySelector('p').textContent;
            const videoCategory = card.querySelector('.video-category').textContent;
            const videoDuration = card.querySelector('.video-duration').textContent;
            const videoViews = card.querySelector('.video-views').textContent;

            if (videoUrl) {
                showVideoModal({
                    title: videoTitle,
                    description: videoDescription,
                    category: videoCategory,
                    duration: videoDuration,
                    views: videoViews,
                    url: videoUrl
                });
            }
        });

        // Add hover effect
        card.addEventListener('mouseenter', function() {
            card.style.transform = 'translateY(-5px) scale(1.02)';
            card.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)';
        });

        card.addEventListener('mouseleave', function() {
            card.style.transform = 'translateY(0) scale(1)';
            card.style.boxShadow = '';
        });
    });
}

function showVideoModal(videoData) {
    // Create video modal
    const modal = document.createElement('div');
    modal.className = 'video-modal';
    modal.innerHTML = `
        <div class="video-modal-content">
            <div class="video-modal-header">
                <h3>${videoData.title}</h3>
                <button class="video-modal-close" aria-label="Close video">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="video-modal-body">
                <iframe class="video-modal-iframe"
                        src="${videoData.url}?autoplay=1&rel=0&modestbranding=1"
                        title="${videoData.title}"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowfullscreen>
                </iframe>
            </div>
            <div class="video-modal-info">
                <p class="video-modal-description">${videoData.description}</p>
                <div class="video-modal-meta">
                    <span class="video-modal-category">${videoData.category}</span>
                    <span class="video-modal-duration">
                        <i class="fas fa-clock"></i> ${videoData.duration}
                    </span>
                    <span class="video-modal-views">
                        <i class="fas fa-eye"></i> ${videoData.views}
                    </span>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';

    // Show modal with animation
    setTimeout(() => {
        modal.classList.add('active');
    }, 10);

    // Close modal functionality
    function closeModal() {
        modal.classList.remove('active');
        setTimeout(() => {
            if (document.body.contains(modal)) {
                document.body.removeChild(modal);
                document.body.style.overflow = '';
            }
        }, 300);
    }

    const closeBtn = modal.querySelector('.video-modal-close');
    closeBtn.addEventListener('click', closeModal);

    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal();
        }
    });

    // Close on escape key
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            closeModal();
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);

    // Focus management for accessibility
    const firstFocusable = modal.querySelector('.video-modal-close');
    if (firstFocusable) {
        firstFocusable.focus();
    }
}

// ===== STAT COUNTERS =====
function initStatCounters() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    const countUp = (element, target) => {
        const increment = target / 100;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current) + '+';
        }, 20);
    };
    
    // Intersection Observer for stat counters
    const statsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = parseInt(entry.target.textContent);
                countUp(entry.target, target);
                statsObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });
    
    statNumbers.forEach(stat => {
        statsObserver.observe(stat);
    });
}

// ===== SMOOTH SCROLLING =====
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // Account for fixed header
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// ===== UTILITY FUNCTIONS =====

// Debounce function for performance optimization
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Throttle function for scroll events
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Add optimized scroll listener
window.addEventListener('scroll', throttle(updateActiveNavLink, 100));

// ===== ACCESSIBILITY ENHANCEMENTS =====

// Keyboard navigation for custom elements
document.addEventListener('keydown', function(e) {
    // Add keyboard support for filter buttons
    if (e.target.classList.contains('filter-btn') && (e.key === 'Enter' || e.key === ' ')) {
        e.preventDefault();
        e.target.click();
    }
    
    // Add keyboard support for video play buttons
    if (e.target.classList.contains('play-button') && (e.key === 'Enter' || e.key === ' ')) {
        e.preventDefault();
        e.target.click();
    }
});

// Focus management for modals
function trapFocus(element) {
    const focusableElements = element.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    element.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            if (e.shiftKey) {
                if (document.activeElement === firstElement) {
                    lastElement.focus();
                    e.preventDefault();
                }
            } else {
                if (document.activeElement === lastElement) {
                    firstElement.focus();
                    e.preventDefault();
                }
            }
        }
        
        if (e.key === 'Escape') {
            const closeBtn = element.querySelector('.close-modal');
            if (closeBtn) closeBtn.click();
        }
    });
}
