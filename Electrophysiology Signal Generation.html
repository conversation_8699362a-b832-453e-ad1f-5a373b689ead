<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Electrophysiology: Ion Transport & Signal Generation</title>
    <style>
        :root {
            --primary-color: #005f73;
            --secondary-color: #0a9396;
            --accent-color: #94d2bd;
            --background-color: #e9d8a6;
            --text-color: #001219;
            --highlight-color: #ee9b00;
            --membrane-color: #8b5a3c;
            --cytoplasm-color: #e8f4f8;
            --extracellular-color: #fff2e6;
            --sodium-color: #ff6b6b;
            --potassium-color: #4ecdc4;
            --calcium-color: #45b7d1;
            --chloride-color: #96ceb4;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: linear-gradient(135deg, var(--background-color) 0%, var(--accent-color) 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            color: var(--text-color);
            font-size: 1.1rem;
            max-width: 800px;
            margin: 0 auto;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            margin-bottom: 30px;
        }

        .simulation-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }

        .simulation-canvas {
            width: 100%;
            height: 600px;
            border: 2px solid var(--primary-color);
            border-radius: 10px;
            background: linear-gradient(to bottom, var(--extracellular-color) 0%, var(--extracellular-color) 40%, var(--membrane-color) 40%, var(--membrane-color) 60%, var(--cytoplasm-color) 60%, var(--cytoplasm-color) 100%);
            position: relative;
            overflow: hidden;
        }

        .membrane-section {
            position: absolute;
            top: 40%;
            left: 0;
            right: 0;
            height: 20%;
            background: var(--membrane-color);
            border-top: 2px solid #654321;
            border-bottom: 2px solid #654321;
        }

        .control-group {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid var(--secondary-color);
        }

        .control-group h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .control-item {
            margin-bottom: 15px;
        }

        .control-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--text-color);
        }

        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--secondary-color);
            cursor: pointer;
        }

        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--secondary-color);
            cursor: pointer;
            border: none;
        }

        .value-display {
            display: inline-block;
            margin-left: 10px;
            font-weight: 600;
            color: var(--primary-color);
            min-width: 60px;
        }

        .button {
            background: var(--secondary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .button:hover {
            background: var(--primary-color);
            transform: translateY(-2px);
        }

        .button.active {
            background: var(--highlight-color);
        }

        .ion-legend {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }

        .ion-item {
            display: flex;
            align-items: center;
            padding: 8px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 6px;
        }

        .ion-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .sodium { background-color: var(--sodium-color); }
        .potassium { background-color: var(--potassium-color); }
        .calcium { background-color: var(--calcium-color); }
        .chloride { background-color: var(--chloride-color); }

        .voltage-display {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 1.2rem;
            text-align: center;
            margin-bottom: 20px;
            border: 2px solid var(--secondary-color);
        }

        .phase-indicator {
            background: var(--primary-color);
            color: white;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .info-panels {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .info-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .info-panel h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .info-panel ul {
            list-style: none;
            padding-left: 0;
        }

        .info-panel li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .info-panel li::before {
            content: "→";
            position: absolute;
            left: 0;
            color: var(--secondary-color);
            font-weight: bold;
        }

        .equation {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            text-align: center;
            margin: 15px 0;
            border-left: 4px solid var(--highlight-color);
        }

        .ion-particle {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .channel {
            position: absolute;
            width: 20px;
            height: 60px;
            background: #333;
            border-radius: 10px;
            top: 35%;
        }

        .pump {
            position: absolute;
            width: 30px;
            height: 80px;
            background: #666;
            border-radius: 15px;
            top: 30%;
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .simulation-canvas {
                height: 500px;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 1.8rem;
            }
            
            .simulation-canvas {
                height: 400px;
            }
            
            .ion-legend {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Electrophysiology: Ion Transport & Signal Generation</h1>
            <p>Explore the fundamental mechanisms of bioelectricity through precise simulation of ion movement across cell membranes, from resting potential to action potential generation and propagation.</p>
        </div>

        <div class="main-content">
            <div class="simulation-panel">
                <h2>Interactive Membrane Simulation</h2>
                <div class="simulation-canvas" id="simulationCanvas">
                    <div class="membrane-section" id="membrane"></div>
                </div>
                
                <div class="ion-legend">
                    <div class="ion-item">
                        <div class="ion-color sodium"></div>
                        <span>Na⁺ (Sodium)</span>
                    </div>
                    <div class="ion-item">
                        <div class="ion-color potassium"></div>
                        <span>K⁺ (Potassium)</span>
                    </div>
                    <div class="ion-item">
                        <div class="ion-color calcium"></div>
                        <span>Ca²⁺ (Calcium)</span>
                    </div>
                    <div class="ion-item">
                        <div class="ion-color chloride"></div>
                        <span>Cl⁻ (Chloride)</span>
                    </div>
                </div>
            </div>

            <div class="control-panel">
                <div class="voltage-display" id="voltageDisplay">
                    Membrane Potential: -70 mV
                </div>
                
                <div class="phase-indicator" id="phaseIndicator">
                    Resting State
                </div>

                <div class="control-group">
                    <h3>Membrane Permeability</h3>
                    <div class="control-item">
                        <label for="naPermeability">Na⁺ Permeability:</label>
                        <input type="range" id="naPermeability" class="slider" min="0" max="100" value="5">
                        <span class="value-display" id="naPermValue">5%</span>
                    </div>
                    <div class="control-item">
                        <label for="kPermeability">K⁺ Permeability:</label>
                        <input type="range" id="kPermeability" class="slider" min="0" max="100" value="25">
                        <span class="value-display" id="kPermValue">25%</span>
                    </div>
                    <div class="control-item">
                        <label for="caPermeability">Ca²⁺ Permeability:</label>
                        <input type="range" id="caPermeability" class="slider" min="0" max="100" value="1">
                        <span class="value-display" id="caPermValue">1%</span>
                    </div>
                </div>

                <div class="control-group">
                    <h3>Ion Concentrations (mM)</h3>
                    <div class="control-item">
                        <label for="naOut">Extracellular Na⁺:</label>
                        <input type="range" id="naOut" class="slider" min="100" max="200" value="145">
                        <span class="value-display" id="naOutValue">145</span>
                    </div>
                    <div class="control-item">
                        <label for="kOut">Extracellular K⁺:</label>
                        <input type="range" id="kOut" class="slider" min="1" max="10" value="5">
                        <span class="value-display" id="kOutValue">5</span>
                    </div>
                </div>

                <div class="control-group">
                    <h3>Simulation Controls</h3>
                    <button class="button" id="restingBtn">Resting Potential</button>
                    <button class="button" id="stimulateBtn">Stimulate</button>
                    <button class="button" id="actionPotentialBtn">Action Potential</button>
                    <button class="button" id="resetBtn">Reset</button>
                </div>

                <div class="control-group">
                    <h3>Na⁺/K⁺ Pump Activity</h3>
                    <div class="control-item">
                        <label for="pumpActivity">Pump Rate:</label>
                        <input type="range" id="pumpActivity" class="slider" min="0" max="100" value="50">
                        <span class="value-display" id="pumpValue">50%</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="info-panels">
            <div class="info-panel">
                <h3>Resting Potential Mechanisms</h3>
                <p>The resting membrane potential (~-70mV) is established by:</p>
                <ul>
                    <li>Na⁺/K⁺ ATPase pump (3 Na⁺ out : 2 K⁺ in)</li>
                    <li>Selective K⁺ permeability through leak channels</li>
                    <li>Impermeant intracellular anions</li>
                    <li>Low Na⁺ permeability at rest</li>
                </ul>
                <div class="equation">
                    E<sub>m</sub> = (P<sub>K</sub>E<sub>K</sub> + P<sub>Na</sub>E<sub>Na</sub> + P<sub>Cl</sub>E<sub>Cl</sub>) / (P<sub>K</sub> + P<sub>Na</sub> + P<sub>Cl</sub>)
                </div>
                <p><strong>Goldman-Hodgkin-Katz Equation</strong> - Describes membrane potential based on relative permeabilities and concentration gradients.</p>
            </div>

            <div class="info-panel">
                <h3>Action Potential Phases</h3>
                <ul>
                    <li><strong>Phase 0 - Depolarization:</strong> Voltage-gated Na⁺ channels open rapidly</li>
                    <li><strong>Phase 1 - Early Repolarization:</strong> Na⁺ channels inactivate, K⁺ channels begin opening</li>
                    <li><strong>Phase 2 - Plateau (cardiac):</strong> Ca²⁺ influx balances K⁺ efflux</li>
                    <li><strong>Phase 3 - Repolarization:</strong> K⁺ efflux dominates, Ca²⁺ channels close</li>
                    <li><strong>Phase 4 - Resting:</strong> Na⁺/K⁺ pump restores ionic gradients</li>
                </ul>
                <div class="equation">
                    Threshold ≈ -55mV (Na⁺ channel activation)
                </div>
            </div>

            <div class="info-panel">
                <h3>Ion Channel Properties</h3>
                <p><strong>Voltage-Gated Sodium Channels:</strong></p>
                <ul>
                    <li>Fast activation (0.1-0.2 ms)</li>
                    <li>Fast inactivation (1-2 ms)</li>
                    <li>Responsible for rapid depolarization</li>
                    <li>Blocked by TTX, local anesthetics</li>
                </ul>
                <p><strong>Voltage-Gated Potassium Channels:</strong></p>
                <ul>
                    <li>Slower activation (1-2 ms)</li>
                    <li>No inactivation (most types)</li>
                    <li>Responsible for repolarization</li>
                    <li>Multiple subtypes (delayed rectifier, A-type, etc.)</li>
                </ul>
            </div>

            <div class="info-panel">
                <h3>Nernst Equilibrium Potentials</h3>
                <div class="equation">
                    E<sub>ion</sub> = (RT/zF) × ln([ion]<sub>out</sub>/[ion]<sub>in</sub>)
                </div>
                <p>At 37°C (310K):</p>
                <ul>
                    <li><strong>E<sub>Na</sub>:</strong> +60mV (145mM out / 15mM in)</li>
                    <li><strong>E<sub>K</sub>:</strong> -90mV (5mM out / 140mM in)</li>
                    <li><strong>E<sub>Ca</sub>:</strong> +120mV (2mM out / 0.0001mM in)</li>
                    <li><strong>E<sub>Cl</sub>:</strong> -70mV (110mM out / 10mM in)</li>
                </ul>
                <p>Where R=8.314 J/mol·K, T=310K, F=96,485 C/mol</p>
            </div>

            <div class="info-panel">
                <h3>Clinical Correlations</h3>
                <p><strong>Hyperkalemia (↑K⁺):</strong></p>
                <ul>
                    <li>Depolarizes resting potential</li>
                    <li>Reduces action potential amplitude</li>
                    <li>Can cause cardiac arrhythmias</li>
                </ul>
                <p><strong>Hyponatremia (↓Na⁺):</strong></p>
                <ul>
                    <li>Reduces driving force for depolarization</li>
                    <li>Impairs action potential generation</li>
                    <li>Neurological symptoms</li>
                </ul>
                <p><strong>Local Anesthetics:</strong></p>
                <ul>
                    <li>Block voltage-gated Na⁺ channels</li>
                    <li>Prevent action potential propagation</li>
                    <li>Use-dependent block</li>
                </ul>
            </div>

            <div class="info-panel">
                <h3>Biomedical Engineering Applications</h3>
                <ul>
                    <li><strong>Pacemakers:</strong> Artificial electrical stimulation</li>
                    <li><strong>Defibrillators:</strong> Synchronized cardioversion</li>
                    <li><strong>Neural Prosthetics:</strong> Electrical interface with nervous system</li>
                    <li><strong>Drug Development:</strong> Ion channel modulators</li>
                    <li><strong>Biosensors:</strong> Ion-selective electrodes</li>
                    <li><strong>Patch Clamp:</strong> Single channel recording techniques</li>
                </ul>
            </div>
        </div>

        <script>
            // Simulation state variables
            let membraneVoltage = -70; // mV
            let currentPhase = 'resting';
            let animationId = null;
            let ions = [];
            let channels = [];
            let pumps = [];
            let isStimulated = false;
            let actionPotentialActive = false;

            // Ion properties
            const ionTypes = {
                sodium: { color: '#ff6b6b', charge: 1, symbol: 'Na⁺' },
                potassium: { color: '#4ecdc4', charge: 1, symbol: 'K⁺' },
                calcium: { color: '#45b7d1', charge: 2, symbol: 'Ca²⁺' },
                chloride: { color: '#96ceb4', charge: -1, symbol: 'Cl⁻' }
            };

            // Concentration gradients (mM)
            let concentrations = {
                sodium: { out: 145, in: 15 },
                potassium: { out: 5, in: 140 },
                calcium: { out: 2, in: 0.0001 },
                chloride: { out: 110, in: 10 }
            };

            // Permeabilities (relative)
            let permeabilities = {
                sodium: 0.05,
                potassium: 0.25,
                calcium: 0.01,
                chloride: 0.1
            };

            // DOM elements
            const canvas = document.getElementById('simulationCanvas');
            const voltageDisplay = document.getElementById('voltageDisplay');
            const phaseIndicator = document.getElementById('phaseIndicator');
            const membrane = document.getElementById('membrane');

            // Control elements
            const naPermSlider = document.getElementById('naPermeability');
            const kPermSlider = document.getElementById('kPermeability');
            const caPermSlider = document.getElementById('caPermeability');
            const naOutSlider = document.getElementById('naOut');
            const kOutSlider = document.getElementById('kOut');
            const pumpSlider = document.getElementById('pumpActivity');

            // Buttons
            const restingBtn = document.getElementById('restingBtn');
            const stimulateBtn = document.getElementById('stimulateBtn');
            const actionPotentialBtn = document.getElementById('actionPotentialBtn');
            const resetBtn = document.getElementById('resetBtn');

            // Initialize simulation
            function initSimulation() {
                createChannelsAndPumps();
                createIons();
                updateDisplay();
                setupEventListeners();
                startAnimation();
            }

            function createChannelsAndPumps() {
                // Create voltage-gated sodium channels
                for (let i = 0; i < 8; i++) {
                    const channel = document.createElement('div');
                    channel.className = 'channel';
                    channel.style.left = (100 + i * 80) + 'px';
                    channel.style.background = '#ff6b6b';
                    channel.dataset.type = 'sodium';
                    channel.dataset.state = 'closed';
                    membrane.appendChild(channel);
                    channels.push(channel);
                }

                // Create voltage-gated potassium channels
                for (let i = 0; i < 6; i++) {
                    const channel = document.createElement('div');
                    channel.className = 'channel';
                    channel.style.left = (150 + i * 100) + 'px';
                    channel.style.background = '#4ecdc4';
                    channel.dataset.type = 'potassium';
                    channel.dataset.state = 'closed';
                    membrane.appendChild(channel);
                    channels.push(channel);
                }

                // Create Na/K pumps
                for (let i = 0; i < 4; i++) {
                    const pump = document.createElement('div');
                    pump.className = 'pump';
                    pump.style.left = (200 + i * 150) + 'px';
                    pump.style.background = '#666';
                    pump.dataset.type = 'nakpump';
                    membrane.appendChild(pump);
                    pumps.push(pump);
                }
            }

            function createIons() {
                ions = [];
                const canvasRect = canvas.getBoundingClientRect();

                // Create sodium ions (more outside)
                for (let i = 0; i < 50; i++) {
                    createIon('sodium', Math.random() < 0.8 ? 'extracellular' : 'intracellular');
                }

                // Create potassium ions (more inside)
                for (let i = 0; i < 50; i++) {
                    createIon('potassium', Math.random() < 0.2 ? 'extracellular' : 'intracellular');
                }

                // Create calcium ions (mostly outside)
                for (let i = 0; i < 20; i++) {
                    createIon('calcium', Math.random() < 0.95 ? 'extracellular' : 'intracellular');
                }

                // Create chloride ions
                for (let i = 0; i < 30; i++) {
                    createIon('chloride', Math.random() < 0.7 ? 'extracellular' : 'intracellular');
                }
            }

            function createIon(type, location) {
                const ion = document.createElement('div');
                ion.className = 'ion-particle';
                ion.style.background = ionTypes[type].color;
                ion.dataset.type = type;
                ion.dataset.charge = ionTypes[type].charge;

                const canvasWidth = canvas.offsetWidth;
                const canvasHeight = canvas.offsetHeight;

                if (location === 'extracellular') {
                    ion.style.left = Math.random() * canvasWidth + 'px';
                    ion.style.top = Math.random() * (canvasHeight * 0.35) + 'px';
                } else {
                    ion.style.left = Math.random() * canvasWidth + 'px';
                    ion.style.top = (canvasHeight * 0.65 + Math.random() * (canvasHeight * 0.35)) + 'px';
                }

                canvas.appendChild(ion);
                ions.push(ion);
            }

            // Calculate membrane potential using Goldman-Hodgkin-Katz equation
            function calculateMembraneVoltage() {
                const R = 8.314; // J/mol·K
                const T = 310; // K (37°C)
                const F = 96485; // C/mol
                const RT_F = (R * T) / F * 1000; // Convert to mV

                // Calculate equilibrium potentials
                const E_Na = RT_F * Math.log(concentrations.sodium.out / concentrations.sodium.in);
                const E_K = RT_F * Math.log(concentrations.potassium.out / concentrations.potassium.in);
                const E_Ca = RT_F * Math.log(concentrations.calcium.out / concentrations.calcium.in) / 2; // z=2 for Ca2+
                const E_Cl = -RT_F * Math.log(concentrations.chloride.out / concentrations.chloride.in); // z=-1 for Cl-

                // Goldman-Hodgkin-Katz equation
                const numerator = (permeabilities.potassium * E_K) +
                                (permeabilities.sodium * E_Na) +
                                (permeabilities.calcium * E_Ca) +
                                (permeabilities.chloride * E_Cl);

                const denominator = permeabilities.potassium + permeabilities.sodium +
                                  permeabilities.calcium + permeabilities.chloride;

                return numerator / denominator;
            }

            // Update display elements
            function updateDisplay() {
                membraneVoltage = calculateMembraneVoltage();
                voltageDisplay.textContent = `Membrane Potential: ${membraneVoltage.toFixed(1)} mV`;

                // Update phase indicator
                if (membraneVoltage > -55) {
                    if (membraneVoltage > 0) {
                        phaseIndicator.textContent = 'Depolarization Phase';
                        phaseIndicator.style.background = '#ff6b6b';
                    } else {
                        phaseIndicator.textContent = 'Threshold Reached';
                        phaseIndicator.style.background = '#ffa500';
                    }
                } else if (membraneVoltage > -80) {
                    phaseIndicator.textContent = 'Resting State';
                    phaseIndicator.style.background = 'var(--primary-color)';
                } else {
                    phaseIndicator.textContent = 'Hyperpolarized';
                    phaseIndicator.style.background = '#4ecdc4';
                }

                // Update slider value displays
                document.getElementById('naPermValue').textContent = naPermSlider.value + '%';
                document.getElementById('kPermValue').textContent = kPermSlider.value + '%';
                document.getElementById('caPermValue').textContent = caPermSlider.value + '%';
                document.getElementById('naOutValue').textContent = naOutSlider.value;
                document.getElementById('kOutValue').textContent = kOutSlider.value;
                document.getElementById('pumpValue').textContent = pumpSlider.value + '%';
            }

            // Animate ion movement
            function animateIons() {
                ions.forEach(ion => {
                    const ionType = ion.dataset.type;
                    const currentTop = parseFloat(ion.style.top);
                    const currentLeft = parseFloat(ion.style.left);
                    const canvasHeight = canvas.offsetHeight;
                    const canvasWidth = canvas.offsetWidth;

                    // Determine if ion is in extracellular or intracellular space
                    const isExtracellular = currentTop < canvasHeight * 0.4;
                    const isIntracellular = currentTop > canvasHeight * 0.6;

                    // Random movement
                    let newLeft = currentLeft + (Math.random() - 0.5) * 2;
                    let newTop = currentTop + (Math.random() - 0.5) * 2;

                    // Ion-specific movement based on electrochemical gradients
                    if (ionType === 'sodium' && permeabilities.sodium > 0.1) {
                        // Sodium wants to move in during depolarization
                        if (isExtracellular && membraneVoltage > -60) {
                            newTop += 0.5;
                        }
                    } else if (ionType === 'potassium' && permeabilities.potassium > 0.1) {
                        // Potassium wants to move out during repolarization
                        if (isIntracellular && membraneVoltage > -80) {
                            newTop -= 0.5;
                        }
                    }

                    // Boundary constraints
                    newLeft = Math.max(5, Math.min(canvasWidth - 15, newLeft));
                    newTop = Math.max(5, Math.min(canvasHeight - 15, newTop));

                    // Membrane barrier (ions can't freely cross)
                    if (currentTop < canvasHeight * 0.4 && newTop > canvasHeight * 0.4) {
                        // Trying to cross from extracellular to membrane
                        if (Math.random() > permeabilities[ionType]) {
                            newTop = canvasHeight * 0.4 - 5; // Bounce back
                        }
                    } else if (currentTop > canvasHeight * 0.6 && newTop < canvasHeight * 0.6) {
                        // Trying to cross from intracellular to membrane
                        if (Math.random() > permeabilities[ionType]) {
                            newTop = canvasHeight * 0.6 + 5; // Bounce back
                        }
                    }

                    ion.style.left = newLeft + 'px';
                    ion.style.top = newTop + 'px';
                });
            }

            // Simulate action potential
            function simulateActionPotential() {
                if (actionPotentialActive) return;

                actionPotentialActive = true;
                let phase = 0;
                let time = 0;

                const actionPotentialTimer = setInterval(() => {
                    time += 10; // 10ms increments

                    switch(phase) {
                        case 0: // Rapid depolarization (0-1ms)
                            permeabilities.sodium = Math.min(1.0, 0.05 + time * 0.01);
                            if (time > 100) {
                                phase = 1;
                                time = 0;
                            }
                            break;

                        case 1: // Early repolarization (1-2ms)
                            permeabilities.sodium = Math.max(0.05, 1.0 - time * 0.01);
                            permeabilities.potassium = Math.min(0.8, 0.25 + time * 0.005);
                            if (time > 100) {
                                phase = 2;
                                time = 0;
                            }
                            break;

                        case 2: // Repolarization (2-5ms)
                            permeabilities.potassium = Math.max(0.25, 0.8 - time * 0.002);
                            if (time > 300) {
                                phase = 3;
                                time = 0;
                            }
                            break;

                        case 3: // Return to resting
                            permeabilities.sodium = 0.05;
                            permeabilities.potassium = 0.25;
                            actionPotentialActive = false;
                            clearInterval(actionPotentialTimer);
                            break;
                    }

                    updateChannelStates();
                    updateDisplay();
                }, 10);
            }

            // Update visual state of channels
            function updateChannelStates() {
                channels.forEach(channel => {
                    const type = channel.dataset.type;
                    if (type === 'sodium') {
                        if (permeabilities.sodium > 0.3) {
                            channel.style.background = '#ff4444';
                            channel.style.boxShadow = '0 0 10px #ff4444';
                            channel.dataset.state = 'open';
                        } else {
                            channel.style.background = '#ff6b6b';
                            channel.style.boxShadow = 'none';
                            channel.dataset.state = 'closed';
                        }
                    } else if (type === 'potassium') {
                        if (permeabilities.potassium > 0.4) {
                            channel.style.background = '#2eb8b0';
                            channel.style.boxShadow = '0 0 10px #2eb8b0';
                            channel.dataset.state = 'open';
                        } else {
                            channel.style.background = '#4ecdc4';
                            channel.style.boxShadow = 'none';
                            channel.dataset.state = 'closed';
                        }
                    }
                });
            }

            // Event listeners
            function setupEventListeners() {
                // Permeability sliders
                naPermSlider.addEventListener('input', () => {
                    permeabilities.sodium = naPermSlider.value / 100;
                    updateDisplay();
                    updateChannelStates();
                });

                kPermSlider.addEventListener('input', () => {
                    permeabilities.potassium = kPermSlider.value / 100;
                    updateDisplay();
                    updateChannelStates();
                });

                caPermSlider.addEventListener('input', () => {
                    permeabilities.calcium = caPermSlider.value / 100;
                    updateDisplay();
                });

                // Concentration sliders
                naOutSlider.addEventListener('input', () => {
                    concentrations.sodium.out = parseFloat(naOutSlider.value);
                    updateDisplay();
                });

                kOutSlider.addEventListener('input', () => {
                    concentrations.potassium.out = parseFloat(kOutSlider.value);
                    updateDisplay();
                });

                // Button events
                restingBtn.addEventListener('click', () => {
                    permeabilities.sodium = 0.05;
                    permeabilities.potassium = 0.25;
                    permeabilities.calcium = 0.01;
                    naPermSlider.value = 5;
                    kPermSlider.value = 25;
                    caPermSlider.value = 1;
                    updateDisplay();
                    updateChannelStates();
                    setActiveButton(restingBtn);
                });

                stimulateBtn.addEventListener('click', () => {
                    permeabilities.sodium = 0.15;
                    naPermSlider.value = 15;
                    updateDisplay();
                    updateChannelStates();
                    setActiveButton(stimulateBtn);
                });

                actionPotentialBtn.addEventListener('click', () => {
                    simulateActionPotential();
                    setActiveButton(actionPotentialBtn);
                });

                resetBtn.addEventListener('click', () => {
                    location.reload();
                });
            }

            function setActiveButton(activeBtn) {
                [restingBtn, stimulateBtn, actionPotentialBtn].forEach(btn => {
                    btn.classList.remove('active');
                });
                activeBtn.classList.add('active');
            }

            // Animation loop
            function startAnimation() {
                function animate() {
                    animateIons();
                    animationId = requestAnimationFrame(animate);
                }
                animate();
            }

            // Initialize when page loads
            window.addEventListener('load', initSimulation);
        </script>
    </div>
</body>
</html>
