<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anatomy & Physiology for Biomedical Engineers</title>
    <link rel="stylesheet" href="css/style.css">
    </head>
<body>
    <header class="sticky-header">
        <nav class="main-nav">
            <ul>
                <li><a href="#hero">Welcome</a></li>
                <li><a href="#cellular-foundation">Cells</a></li>
                <li><a href="#tissues-module">Tissues</a></li>
                <li><a href="#organs-module">Organs</a></li>
                <li><a href="#body-systems-module">Systems</a></li>
                <li><a href="#system-integration">Integration</a></li>
                <li class="nav-glossary"><a href="#" id="glossary-toggle">Glossary</a></li>
            </ul>
        </nav>
    </header>

    <aside id="glossary-sidebar" class="glossary-sidebar">
        <span class="close-btn" id="close-glossary">&times;</span>
        <h2>A&P Terminology Glossary</h2>
        <input type="text" id="glossary-search" placeholder="Search terms...">
        <div id="glossary-content">
            </div>
    </aside>

    <main>
        <section id="hero" class="hero-section">
            <div class="hero-content">
                <h1>Anatomy & Physiology for Biomedical Engineers: Unlocking the Human Machine</h1>
                <h2>Explore the fundamental building blocks and intricate systems that govern life, from a biomedical engineering perspective.</h2>
                <p>Welcome to an immersive learning experience designed to equip future biomedical engineers with a foundational understanding of the human body. Dive deep into the mechanics, terminology, and integrated functions that make us who we are.</p>
                <button id="begin-journey-btn" class="cta-button">Begin Your Journey</button>
            </div>
            <div class="hero-animation-placeholder">
                <img src="assets/images/hero-background.gif" alt="Human body transitioning from cells to systems animation">
            </div>
        </section>

        <section id="cellular-foundation" class="module-section">
            <h2>Module 1: The Cellular Foundation – Life's Smallest Engineers</h2>
            <p>At the core of all life are cells, the fundamental units that perform all vital functions. Understanding their structure, function, and communication is paramount for biomedical engineers.</p>

            <h3>Basic Terminology Glossary</h3>
            <p>Throughout this page, hover over <span class="glossary-term" data-term="homeostasis">highlighted terms</span> for quick definitions.</p>
            <p>Click the "Glossary" in the navigation or sidebar to explore more terms interactively.</p>

            <h3>Cell Description & Diversity</h3>
            <div class="interactive-cell-model">
                <h4>Interactive 3D Cell Model</h4>
                <div id="threejs-cell-model-container" class="model-container">
                    <img src="https://via.placeholder.com/600x400?text=Interactive+3D+Cell+Model" alt="3D Cell Model Placeholder">
                    <div class="model-overlay">
                        <p>Click on parts of the cell to learn more!</p>
                        <ul class="cell-parts-legend">
                            <li data-part="nucleus">Nucleus</li>
                            <li data-part="mitochondria">Mitochondria</li>
                            <li data-part="er">Endoplasmic Reticulum (ER)</li>
                            <li data-part="golgi">Golgi Apparatus</li>
                            </ul>
                    </div>
                </div>
                <div id="cell-part-info" class="pop-up-info hidden">
                    <h3></h3>
                    <p></p>
                    <video controls src="" class="hidden"></video>
                    <button class="close-popup">Close</button>
                </div>
            </div>

            <div class="cell-type-showcase">
                <h4>Cell Type Showcase</h4>
                <div class="cell-types-grid">
                    <div class="cell-card">
                        <img src="assets/images/cell-types/neuron.png" alt="Neuron cell">
                        <h5>Neuron (Nerve Cell)</h5>
                        <p>Specialized for transmitting electrical signals, forming the basis of the nervous system.</p>
                    </div>
                    <div class="cell-card">
                        <img src="assets/images/cell-types/muscle_cell.png" alt="Muscle cell">
                        <h5>Muscle Cell (Myocyte)</h5>
                        <p>Contractile cells responsible for movement, found in skeletal, cardiac, and smooth muscles.</p>
                    </div>
                    <div class="cell-card">
                        <img src="assets/images/cell-types/rbc.png" alt="Red Blood Cell">
                        <h5>Red Blood Cell (Erythrocyte)</h5>
                        <p>Transports oxygen throughout the body, characterized by its biconcave shape and lack of nucleus.</p>
                    </div>
                </div>
            </div>

            <h3>The Cell Membrane: Gatekeeper & Communicator (The Secret Story)</h3>
            <div class="video-container">
                <h4>Animated Video: "The Secret of the Cell Membrane: A Dynamic Barrier."</h4>
                <video controls src="assets/videos/cell_membrane_secret.mp4" poster="https://via.placeholder.com/600x338?text=Cell+Membrane+Video" class="responsive-video">
                    Your browser does not support the video tag.
                </video>
            </div>

            <div class="interactive-simulation">
                <h4>Virtual Simulation: Membrane Transport</h4>
                <p>Manipulate concentration gradients and ATP levels to observe different transport mechanisms in action.</p>
                <div id="membrane-transport-simulation" class="simulation-area">
                    <img src="https://via.placeholder.com/700x400?text=Membrane+Transport+Simulation" alt="Membrane Transport Simulation Placeholder">
                    <div class="simulation-controls">
                        <label for="gradient-slider">Concentration Gradient:</label>
                        <input type="range" id="gradient-slider" min="0" max="100" value="50">
                        <label for="atp-toggle">Add ATP:</label>
                        <input type="checkbox" id="atp-toggle">
                        <button id="run-transport-sim">Run Simulation</button>
                        <p class="simulation-feedback"></p>
                    </div>
                </div>
            </div>

            <div class="interactive-simulation action-potential-section">
                <h4>Virtual Simulation: Generation of Action Potentials – The Secret of Communication</h4>
                <p>Explore the dynamic process of nerve impulse generation. Control parameters to understand their impact.</p>
                <div id="action-potential-simulation" class="simulation-area">
                    <img src="https://via.placeholder.com/800x450?text=Action+Potential+Simulation" alt="Action Potential Simulation Placeholder">
                    <div class="simulation-controls">
                        <label for="threshold-slider">Threshold Stimulus:</label>
                        <input type="range" id="threshold-slider" min="0" max="100" value="50">
                        <label for="ion-channel-density-slider">Ion Channel Density:</label>
                        <input type="range" id="ion-channel-density-slider" min="0" max="100" value="50">
                        <button id="run-ap-sim">Simulate AP</button>
                        <div class="ap-graph-placeholder"></div>
                    </div>
                </div>
                <div class="video-container">
                    <h5>Animated Video: Ion Channels & Na+/K+ Pump</h5>
                    <video controls src="assets/videos/ion_channels_pump.mp4" poster="https://via.placeholder.com/600x338?text=Ion+Channels+Video" class="responsive-video">
                        Your browser does not support the video tag.
                    </video>
                </div>
            </div>
        </section>

        <section id="tissues-module" class="module-section">
            <h2>Module 2: Tissues – Organized Communities</h2>
            <p>Cells rarely work alone; they organize into specialized groups called tissues, each with distinct functions and structures.</p>
            <h3>Definition of Tissue</h3>
            <p>A <span class="glossary-term" data-term="tissue">tissue</span> is a group of similar cells and their extracellular matrix, organized to perform a specific function. The human body is composed of four primary tissue types.</p>

            <div class="interactive-tissue-explorer">
                <h4>Interactive Tissue Explorer</h4>
                <div class="tissue-tabs">
                    <button class="tab-button active" data-tissue="epithelial">Epithelial</button>
                    <button class="tab-button" data-tissue="connective">Connective</button>
                    <button class="tab-button" data-tissue="muscle">Muscle</button>
                    <button class="tab-button" data-tissue="nervous">Nervous</button>
                </div>
                <div id="tissue-info-display" class="tissue-info-display">
                    <div class="tissue-content active" id="epithelial-tissue-content">
                        <div class="tissue-image-container">
                            <img src="assets/images/tissues/epithelial.png" alt="Epithelial Tissue">
                            <div class="image-labels">
                                <span class="label" style="top: 10%; left: 50%;">Apical Surface</span>
                                <span class="label" style="top: 80%; left: 50%;">Basement Membrane</span>
                            </div>
                        </div>
                        <div class="tissue-text">
                            <h5>Epithelial Tissue</h5>
                            <p>Forms coverings and linings of organs and cavities. Functions include protection, secretion, absorption, and filtration.</p>
                            <h6>Examples:</h6>
                            <ul>
                                <li>Skin epidermis</li>
                                <li>Lining of digestive tract</li>
                                <li>Lining of blood vessels</li>
                            </ul>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/epithelial_absorption.mp4" poster="https://via.placeholder.com/300x170?text=Epithelial+Video" class="responsive-video">
                                    Your browser does not support the video tag.
                                </video>
                                <p>Watch how epithelial cells absorb nutrients.</p>
                            </div>
                        </div>
                    </div>
                    <div class="tissue-content" id="connective-tissue-content">
                        <div class="tissue-image-container">
                            <img src="assets/images/tissues/connective.png" alt="Connective Tissue">
                            <div class="image-labels">
                                <span class="label" style="top: 30%; left: 20%;">Collagen Fibers</span>
                                <span class="label" style="top: 70%; left: 60%;">Fibroblast</span>
                            </div>
                        </div>
                        <div class="tissue-text">
                            <h5>Connective Tissue</h5>
                            <p>Supports, connects, and protects other tissues and organs. Diverse types include bone, blood, cartilage, and adipose tissue.</p>
                            <h6>Examples:</h6>
                            <ul>
                                <li>Bones and tendons</li>
                                <li>Blood</li>
                                <li>Adipose (fat) tissue</li>
                            </ul>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/connective_support.mp4" poster="https://via.placeholder.com/300x170?text=Connective+Video" class="hidden">
                                    Your browser does not support the video tag.
                                </video>
                                <p>Illustrating the supportive role of connective tissue. (Video Placeholder)</p>
                            </div>
                        </div>
                    </div>
                    <div class="tissue-content" id="muscle-tissue-content">
                        <div class="tissue-image-container">
                            <img src="assets/images/tissues/muscle.png" alt="Muscle Tissue">
                            <div class="image-labels">
                                <span class="label" style="top: 40%; left: 50%;">Muscle Fiber</span>
                                <span class="label" style="top: 60%; left: 30%;">Striations (Skeletal)</span>
                            </div>
                        </div>
                        <div class="tissue-text">
                            <h5>Muscle Tissue</h5>
                            <p>Specialized for contraction, enabling movement. Three types: skeletal, cardiac, and smooth.</p>
                            <h6>Examples:</h6>
                            <ul>
                                <li>Biceps (Skeletal)</li>
                                <li>Heart wall (Cardiac)</li>
                                <li>Intestine walls (Smooth)</li>
                            </ul>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/muscle_contraction.mp4" poster="https://via.placeholder.com/300x170?text=Muscle+Video" class="responsive-video">
                                    Your browser does not support the video tag.
                                </video>
                                <p>Observe the mechanism of muscle contraction.</p>
                            </div>
                        </div>
                    </div>
                    <div class="tissue-content" id="nervous-tissue-content">
                        <div class="tissue-image-container">
                            <img src="assets/images/tissues/nervous.png" alt="Nervous Tissue">
                            <div class="image-labels">
                                <span class="label" style="top: 20%; left: 30%;">Neuron Cell Body</span>
                                <span class="label" style="top: 70%; left: 70%;">Axon</span>
                            </div>
                        </div>
                        <div class="tissue-text">
                            <h5>Nervous Tissue</h5>
                            <p>Responsible for communication and control, forming the brain, spinal cord, and nerves.</p>
                            <h6>Examples:</h6>
                            <ul>
                                <li>Brain</li>
                                <li>Spinal Cord</li>
                                <li>Peripheral Nerves</li>
                            </ul>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/nerve_impulse.mp4" poster="https://via.placeholder.com/300x170?text=Nerve+Video" class="responsive-video">
                                    Your browser does not support the video tag.
                                </video>
                                <p>See how nerve impulses are transmitted.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="interactive-simulation">
                <h4>Virtual Microscope Simulation: Identify the Tissue!</h4>
                <p>Drag and drop tissue samples onto the virtual slide and identify them. Get instant feedback!</p>
                <div id="virtual-microscope-simulation" class="simulation-area">
                    <div class="microscope-stage">
                        <img src="https://via.placeholder.com/400x300?text=Virtual+Microscope+Stage" alt="Virtual Microscope Stage">
                        <div id="tissue-drop-zone" class="drop-zone">Drop tissue here</div>
                    </div>
                    <div class="tissue-samples-palette">
                        <div class="tissue-sample draggable" data-tissue-type="epithelial">Epithelial Sample</div>
                        <div class="tissue-sample draggable" data-tissue-type="connective">Connective Sample</div>
                        <div class="tissue-sample draggable" data-tissue-type="muscle">Muscle Sample</div>
                        <div class="tissue-sample draggable" data-tissue-type="nervous">Nervous Sample</div>
                    </div>
                    <p id="microscope-feedback" class="simulation-feedback"></p>
                </div>
            </div>
        </section>

        <section id="organs-module" class="module-section">
            <h2>Module 3: Organs – Functional Units</h2>
            <p>Organs are complex structures composed of two or more different tissue types working together to perform specific, vital functions.</p>
            <h3>Definition of Organ</h3>
            <p>An <span class="glossary-term" data-term="organ">organ</span> is a collection of tissues joined in a structural unit to serve a common function. The precise arrangement and interaction of these tissues enable the organ to perform its specialized role.</p>

            <div class="interactive-organ-builder">
                <h4>Interactive Organ Builder/Dissector</h4>
                <p>Select an organ to explore its structure and constituent tissues. Click on layers to reveal their roles.</p>
                <div class="organ-selector">
                    <button class="organ-button active" data-organ="heart">Heart</button>
                    <button class="organ-button" data-organ="stomach">Stomach</button>
                    <button class="organ-button" data-organ="brain">Brain</button>
                    <button class="organ-button" data-organ="kidney">Kidney</button>
                    <button class="organ-button" data-organ="skin">Skin</button>
                </div>
                <div id="organ-display-area" class="organ-display-area">
                    <div id="organ-heart" class="organ-view active">
                        <div id="threejs-heart-model-container" class="model-container">
                            <img src="assets/images/organ-models/heart-dissect.png" alt="Interactive Heart Model Placeholder">
                            <div class="model-overlay">
                                <span class="organ-layer-label" data-layer="epicardium" style="top: 10%; left: 50%;">Epicardium (Connective)</span>
                                <span class="organ-layer-label" data-layer="myocardium" style="top: 40%; left: 50%;">Myocardium (Muscle)</span>
                                <span class="organ-layer-label" data-layer="endocardium" style="top: 70%; left: 50%;">Endocardium (Epithelial)</span>
                            </div>
                        </div>
                        <div class="organ-info-panel">
                            <h5>The Heart</h5>
                            <p>A muscular organ that pumps blood throughout the body. Composed primarily of cardiac muscle tissue, lined by epithelial tissue, and surrounded by connective tissue.</p>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/heart_function.mp4" poster="https://via.placeholder.com/300x170?text=Heart+Function+Video" class="responsive-video">
                                    Your browser does not support the video tag.
                                </video>
                                <p>See how the heart's tissues work together.</p>
                            </div>
                        </div>
                    </div>
                    <div id="organ-stomach" class="organ-view">
                        <div id="threejs-stomach-model-container" class="model-container">
                            <img src="assets/images/organ-models/stomach-dissect.png" alt="Interactive Stomach Model Placeholder">
                            <div class="model-overlay">
                                <span class="organ-layer-label" data-layer="mucosa" style="top: 20%; left: 50%;">Mucosa (Epithelial, Connective)</span>
                                <span class="organ-layer-label" data-layer="muscularis" style="top: 60%; left: 50%;">Muscularis (Muscle)</span>
                            </div>
                        </div>
                        <div class="organ-info-panel">
                            <h5>The Stomach</h5>
                            <p>A digestive organ that mixes food with digestive juices. Features prominent muscle layers for churning and epithelial lining for secretion and protection.</p>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/stomach_digestion.mp4" poster="https://via.placeholder.com/300x170?text=Stomach+Digestion" class="hidden">
                                    Your browser does not support the video tag.
                                </video>
                                <p>Understanding stomach function. (Video Placeholder)</p>
                            </div>
                        </div>
                    </div>
                    <div id="organ-brain" class="organ-view">
                        <div id="threejs-brain-model-container" class="model-container">
                            <img src="assets/images/organ-models/brain-dissect.png" alt="Interactive Brain Model Placeholder">
                            <div class="model-overlay">
                                <span class="organ-layer-label" data-layer="gray-matter" style="top: 30%; left: 50%;">Gray Matter (Nervous)</span>
                                <span class="organ-layer-label" data-layer="white-matter" style="top: 70%; left: 50%;">White Matter (Nervous, Connective)</span>
                            </div>
                        </div>
                        <div class="organ-info-panel">
                            <h5>The Brain</h5>
                            <p>The control center of the nervous system, composed primarily of nervous tissue (neurons and glia) and connective tissue that provides support and protection.</p>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/brain_function.mp4" poster="https://via.placeholder.com/300x170?text=Brain+Function" class="hidden">
                                    Your browser does not support the video tag.
                                </video>
                                <p>Exploring brain activity. (Video Placeholder)</p>
                            </div>
                        </div>
                    </div>
                    <div id="organ-kidney" class="organ-view">
                        <div id="threejs-kidney-model-container" class="model-container">
                            <img src="assets/images/organ-models/kidney-dissect.png" alt="Interactive Kidney Model Placeholder">
                            <div class="model-overlay">
                                <span class="organ-layer-label" data-layer="cortex" style="top: 20%; left: 50%;">Cortex (Epithelial, Connective)</span>
                                <span class="organ-layer-label" data-layer="medulla" style="top: 50%; left: 50%;">Medulla (Epithelial, Connective)</span>
                            </div>
                        </div>
                        <div class="organ-info-panel">
                            <h5>The Kidney</h5>
                            <p>Filters blood and produces urine, vital for maintaining fluid and electrolyte balance. Its functional units (nephrons) are rich in specialized epithelial tissues.</p>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/kidney_function.mp4" poster="https://via.placeholder.com/300x170?text=Kidney+Function" class="hidden">
                                    Your browser does not support the video tag.
                                </video>
                                <p>Understanding kidney filtration. (Video Placeholder)</p>
                            </div>
                        </div>
                    </div>
                    <div id="organ-skin" class="organ-view">
                        <div id="threejs-skin-model-container" class="model-container">
                            <img src="assets/images/organ-models/skin-dissect.png" alt="Interactive Skin Model Placeholder">
                            <div class="model-overlay">
                                <span class="organ-layer-label" data-layer="epidermis" style="top: 10%; left: 50%;">Epidermis (Epithelial)</span>
                                <span class="organ-layer-label" data-layer="dermis" style="top: 40%; left: 50%;">Dermis (Connective)</span>
                                <span class="organ-layer-label" data-layer="hypodermis" style="top: 70%; left: 50%;">Hypodermis (Connective, Adipose)</span>
                            </div>
                        </div>
                        <div class="organ-info-panel">
                            <h5>The Skin</h5>
                            <p>The body's largest organ, providing protection, temperature regulation, and sensation. Composed of epithelial (epidermis) and connective (dermis, hypodermis) tissues.</p>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/skin_function.mp4" poster="https://via.placeholder.com/300x170?text=Skin+Function" class="hidden">
                                    Your browser does not support the video tag.
                                </video>
                                <p>Exploring skin's protective role. (Video Placeholder)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="body-systems-module" class="module-section">
            <h2>Module 4: Body Systems – Orchestrating Life</h2>
            <p>Organs work in harmony, forming complex body systems, each dedicated to vital functions that maintain the body's overall health and stability.</p>

            <div class="interactive-human-body-map">
                <h4>Interactive Human Body Map</h4>
                <p>Click on a body system to reveal its primary organs and functions.</p>
                <div id="threejs-human-body-model-container" class="model-container">
                    <img src="assets/images/body-systems/human-body-map.png" alt="Interactive Human Body Map Placeholder">
                    <div class="system-hotspots">
                        <div class="hotspot" style="top: 35%; left: 55%;" data-system="cardiovascular"></div>
                        <div class="hotspot" style="top: 20%; left: 45%;" data-system="nervous"></div>
                        <div class="hotspot" style="top: 25%; left: 50%;" data-system="respiratory"></div>
                        <div class="hotspot" style="top: 50%; left: 50%;" data-system="digestive"></div>
                        <div class="hotspot" style="top: 60%; left: 50%;" data-system="musculoskeletal"></div>
                        <div class="hotspot" style="top: 40%; left: 60%;" data-system="endocrine"></div>
                        <div class="hotspot" style="top: 60%; left: 40%;" data-system="urinary"></div>
                        <div class="hotspot" style="top: 50%; left: 30%;" data-system="lymphatic"></div>
                        <div class="hotspot" style="top: 80%; left: 50%;" data-system="reproductive"></div>
                    </div>
                </div>
                <div id="system-info-panel" class="system-info-panel">
                    <h3>Select a System to Explore</h3>
                    <div class="system-content-display">
                        <div class="system-content" id="cardiovascular-system-content">
                            <h5>Cardiovascular System</h5>
                            <p><strong>Main Function:</strong> Transports blood, oxygen, nutrients, hormones, and waste products throughout the body.</p>
                            <h6>Primary Organs:</h6>
                            <ul>
                                <li>Heart</li>
                                <li>Blood Vessels (Arteries, Veins, Capillaries)</li>
                                <li>Blood</li>
                            </ul>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/blood_circulation.mp4" poster="https://via.placeholder.com/300x170?text=Circulation+Video" class="responsive-video">
                                    Your browser does not support the video tag.
                                </video>
                                <p>Detailed animation of blood circulation.</p>
                            </div>
                        </div>
                        <div class="system-content" id="nervous-system-content">
                            <h5>Nervous System</h5>
                            <p><strong>Main Function:</strong> Controls and coordinates all bodily functions, receives and interprets sensory information, and initiates responses.</p>
                            <h6>Primary Organs:</h6>
                            <ul>
                                <li>Brain</li>
                                <li>Spinal Cord</li>
                                <li>Nerves</li>
                            </ul>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/nerve_pathway.mp4" poster="https://via.placeholder.com/300x170?text=Nerve+Pathway" class="responsive-video">
                                    Your browser does not support the video tag.
                                </video>
                                <p>Animated pathway of a nerve impulse.</p>
                            </div>
                        </div>
                        <div class="system-content" id="respiratory-system-content">
                            <h5>Respiratory System</h5>
                            <p><strong>Main Function:</strong> Facilitates gas exchange (oxygen intake and carbon dioxide removal) between the body and the external environment.</p>
                            <h6>Primary Organs:</h6>
                            <ul>
                                <li>Lungs</li>
                                <li>Trachea</li>
                                <li>Bronchi</li>
                                <li>Diaphragm</li>
                            </ul>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/gas_exchange.mp4" poster="https://via.placeholder.com/300x170?text=Gas+Exchange" class="responsive-video">
                                    Your browser does not support the video tag.
                                </video>
                                <p>Visualize gas exchange in the lungs.</p>
                            </div>
                        </div>
                        <div class="system-content" id="digestive-system-content">
                            <h5>Digestive System</h5>
                            <p><strong>Main Function:</strong> Breaks down food into nutrients that can be absorbed into the bloodstream and eliminates waste products.</p>
                            <h6>Primary Organs:</h6>
                            <ul>
                                <li>Esophagus</li>
                                <li>Stomach</li>
                                <li>Intestines (Small & Large)</li>
                                <li>Liver, Pancreas, Gallbladder (Accessory Organs)</li>
                            </ul>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/digestion_process.mp4" poster="https://via.placeholder.com/300x170?text=Digestion+Process" class="responsive-video">
                                    Your browser does not support the video tag.
                                </video>
                                <p>Track the journey of food through the digestive tract.</p>
                            </div>
                        </div>
                        <div class="system-content" id="musculoskeletal-system-content">
                            <h5>Musculoskeletal System</h5>
                            <p><strong>Main Function:</strong> Provides support, protection, and allows for movement. Produces blood cells and stores minerals.</p>
                            <h6>Primary Organs:</h6>
                            <ul>
                                <li>Bones</li>
                                <li>Muscles</li>
                                <li>Tendons</li>
                                <li>Ligaments</li>
                                <li>Cartilage</li>
                            </ul>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/musculoskeletal_movement.mp4" poster="https://via.placeholder.com/300x170?text=Musculoskeletal+Movement" class="hidden">
                                    Your browser does not support the video tag.
                                </video>
                                <p>Understanding joint movement. (Video Placeholder)</p>
                            </div>
                        </div>
                        <div class="system-content" id="endocrine-system-content">
                            <h5>Endocrine System</h5>
                            <p><strong>Main Function:</strong> Produces and secretes hormones that regulate growth, metabolism, reproduction, and other body functions.</p>
                            <h6>Primary Organs:</h6>
                            <ul>
                                <li>Pituitary Gland</li>
                                <li>Thyroid Gland</li>
                                <li>Adrenal Glands</li>
                                <li>Pancreas</li>
                                <li>Ovaries/Testes</li>
                            </ul>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/hormone_regulation.mp4" poster="https://via.placeholder.com/300x170?text=Hormone+Regulation" class="hidden">
                                    Your browser does not support the video tag.
                                </video>
                                <p>How hormones regulate the body. (Video Placeholder)</p>
                            </div>
                        </div>
                        <div class="system-content" id="urinary-system-content">
                            <h5>Urinary System</h5>
                            <p><strong>Main Function:</strong> Filters blood to remove waste products and excess water, producing urine.</p>
                            <h6>Primary Organs:</h6>
                            <ul>
                                <li>Kidneys</li>
                                <li>Ureters</li>
                                <li>Urinary Bladder</li>
                                <li>Urethra</li>
                            </ul>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/urine_formation.mp4" poster="https://via.placeholder.com/300x170?text=Urine+Formation" class="hidden">
                                    Your browser does not support the video tag.
                                </video>
                                <p>The process of urine formation. (Video Placeholder)</p>
                            </div>
                        </div>
                        <div class="system-content" id="lymphatic-system-content">
                            <h5>Lymphatic/Immune System</h5>
                            <p><strong>Main Function:</strong> Protects the body from disease by producing white blood cells; also drains excess fluid and transports fats.</p>
                            <h6>Primary Organs:</h6>
                            <ul>
                                <li>Lymph Nodes</li>
                                <li>Spleen</li>
                                <li>Thymus</li>
                                <li>Tonsils</li>
                            </ul>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/immune_response.mp4" poster="https://via.placeholder.com/300x170?text=Immune+Response" class="hidden">
                                    Your browser does not support the video tag.
                                </video>
                                <p>How the immune system defends the body. (Video Placeholder)</p>
                            </div>
                        </div>
                        <div class="system-content" id="reproductive-system-content">
                            <h5>Reproductive System</h5>
                            <p><strong>Main Function:</strong> Produces gametes (sperm and eggs) and hormones, essential for reproduction.</p>
                            <h6>Primary Organs:</h6>
                            <ul>
                                <li>Testes (Males)</li>
                                <li>Ovaries (Females)</li>
                                <li>Uterus (Females)</li>
                            </ul>
                            <div class="video-container small-video">
                                <video controls src="assets/videos/reproductive_process.mp4" poster="https://via.placeholder.com="300x170"?text=Reproductive+Process" class="hidden">
                                    Your browser does not support the video tag.
                                </video>
                                <p>The basics of human reproduction. (Video Placeholder)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="system-integration" class="module-section">
            <h2>Module 5: System Integration – The Symphony of the Body</h2>
            <p>No system works in isolation. The human body is a marvel of integration, with all systems constantly communicating and coordinating to maintain a stable internal environment, a concept known as homeostasis.</p>

            <h3>Concept of Homeostasis</h3>
            <div class="video-container">
                <h4>Animated Explanation: Feedback Loops (Positive and Negative)</h4>
                <video controls src="assets/videos/homeostasis_blood_glucose.mp4" poster="https://via.placeholder.com/600x338?text=Homeostasis+Video" class="responsive-video">
                    Your browser does not support the video tag.
                </video>
                <p>Learn about negative feedback (e.g., blood glucose) and positive feedback (e.g., childbirth).</p>
            </div>

            <h3>Interactive Case Studies/Scenarios</h3>

            <div class="case-study-tabs">
                <button class="case-tab-button active" data-scenario="exercise">Exercise Physiology</button>
                <button class="case-tab-button" data-scenario="stress">Stress Response</button>
                <button class="case-tab-button" data-scenario="diabetes">Diabetes Mellitus</button>
                <button class="case-tab-button" data-scenario="hypertension">Hypertension</button>
            </div>

            <div id="case-study-display" class="case-study-display">
                <div class="case-study-content active" id="exercise-scenario">
                    <h4>Scenario 1: Exercise Physiology - The Integrated Response</h4>
                    <p>Observe how multiple body systems adapt to physical activity. Adjust intensity and see real-time changes.</p>
                    <div id="exercise-simulation" class="simulation-area">
                        <img src="https://via.placeholder.com/700x400?text=Exercise+Physiology+Simulation" alt="Exercise Physiology Simulation Placeholder">
                        <div class="simulation-controls">
                            <label for="exercise-intensity">Exercise Intensity:</label>
                            <input type="range" id="exercise-intensity" min="0" max="100" value="50">
                            <button id="start-exercise-sim">Start Exercise</button>
                            <div class="real-time-metrics">
                                <p>Heart Rate: <span id="hr-display">70</span> bpm</p>
                                <p>Breathing Rate: <span id="br-display">12</span> breaths/min</p>
                                <p>Oxygen Consumption: <span id="o2-display">0.25</span> L/min</p>
                            </div>
                            <p class="simulation-feedback"></p>
                        </div>
                    </div>
                </div>

                <div class="case-study-content" id="stress-scenario">
                    <h4>Scenario 2: Stress Response - Fight or Flight!</h4>
                    <p>An animated diagram showing the interplay between the Nervous and Endocrine systems during a "fight or flight" response.</p>
                    <div class="video-container">
                        <video controls src="assets/videos/stress_response.mp4" poster="https://via.placeholder.com/600x338?text=Stress+Response+Video" class="responsive-video">
                            Your browser does not support the video tag.
                        </video>
                        <p>Understand the physiological cascade triggered by stress.</p>
                    </div>
                </div>

                <div class="case-study-content" id="diabetes-scenario">
                    <h4>Scenario 1: Diabetes Mellitus - A Systemic Challenge</h4>
                    <p>Explore Type 1 and Type 2 diabetes, and how endocrine dysfunction impacts multiple systems.</p>
                    <div class="video-container">
                        <video controls src="assets/videos/diabetes_insulin_signaling.mp4" poster="https://via.placeholder.com/600x338?text=Diabetes+Video" class="responsive-video">
                            Your browser does not support the video tag.
                        </video>
                        <p>Animated diagrams illustrate insulin signaling pathways and their disruption.</p>
                    </div>
                    <div class="interactive-quiz">
                        <h5>Quick Challenge: Diabetes</h5>
                        <p>A patient presents with persistent high blood sugar, increased thirst, and frequent urination. Which body systems are primarily affected by this condition, and what potential biomedical engineering interventions could be considered?</p>
                        <textarea placeholder="Type your answer here..." rows="4"></textarea>
                        <button class="submit-answer">Submit Answer</button>
                        <div class="quiz-feedback"></div>
                    </div>
                </div>

                <div class="case-study-content" id="hypertension-scenario">
                    <h4>Scenario 2: Hypertension - The Silent Threat</h4>
                    <p>See how cardiovascular system dysfunction (e.g., narrowed arteries) affects kidney function and overall systemic health.</p>
                    <div class="video-container">
                        <video controls src="assets/videos/hypertension_impact.mp4" poster="https://via.placeholder.com/600x338?text=Hypertension+Video" class="responsive-video">
                            Your browser does not support the video tag.
                        </video>
                        <p>Understanding the long-term effects of high blood pressure.</p>
                    </div>
                    <div class="interactive-quiz">
                        <h5>Quick Challenge: Hypertension</h5>
                        <p>Chronic hypertension can lead to left ventricular hypertrophy (enlargement of the heart's left ventricle). How does this change affect the heart's pumping efficiency, and what engineering principles might be applied to design a device that monitors or mitigates this condition?</p>
                        <textarea placeholder="Type your answer here..." rows="4"></textarea>
                        <button class="submit-answer">Submit Answer</button>
                        <div class="quiz-feedback"></div>
                    </div>
                </div>
            </div>

            <div class="video-container concluding-video">
                <h3>Concluding Video: The Biomedical Engineer's Role</h3>
                <video controls src="assets/videos/concluding_bme_role.mp4" poster="https://via.placeholder.com/800x450?text=BME+Role+Video" class="responsive-video">
                    Your browser does not support the video tag.
                </video>
                <p>A summary of the incredible complexity of the human body and the critical role of biomedical engineers in understanding, diagnosing, and treating system dysfunctions.</p>
            </div>
        </section>

        <section id="knowledge-check" class="quiz-section">
            <h2>Knowledge Check & Quizzes</h2>
            <p>Test your understanding with these short, optional quizzes for each module.</p>
            <div class="quiz-module">
                <h3>Module 1 Quiz: Cellular Foundations</h3>
                <div class="question">
                    <p>1. Which cellular component is primarily responsible for regulating the passage of substances into and out of the cell?</p>
                    <label><input type="radio" name="q1" value="a"> Nucleus</label>
                    <label><input type="radio" name="q1" value="b"> Mitochondria</label>
                    <label><input type="radio" name="q1" value="c"> Cell Membrane</label>
                    <label><input type="radio" name="q1" value="d"> Endoplasmic Reticulum</label>
                    <button class="check-answer" data-correct="c">Check Answer</button>
                    <p class="quiz-feedback"></p>
                </div>
                <div class="question">
                    <p>2. The rapid influx of which ion causes the depolarization phase of an action potential?</p>
                    <label><input type="radio" name="q2" value="a"> Potassium (K+)</label>
                    <label><input type="radio" name="q2" value="b"> Sodium (Na+)</label>
                    <label><input type="radio" name="q2" value="c"> Calcium (Ca2+)</label>
                    <label><input type="radio" name="q2" value="d"> Chloride (Cl-)</label>
                    <button class="check-answer" data-correct="b">Check Answer</button>
                    <p class="quiz-feedback"></p>
                </div>
            </div>
            <div class="quiz-module">
                <h3>Module 2 Quiz: Tissues</h3>
                <div class="question">
                    <p>1. Which tissue type is characterized by its ability to contract and generate force for movement?</p>
                    <label><input type="radio" name="q3" value="a"> Epithelial tissue</label>
                    <label><input type="radio" name="q3" value="b"> Connective tissue</label>
                    <label><input type="radio" name="q3" value="c"> Muscle tissue</label>
                    <label><input type="radio" name="q3" value="d"> Nervous tissue</label>
                    <button class="check-answer" data-correct="c">Check Answer</button>
                    <p class="quiz-feedback"></p>
                </div>
                <div class="question">
                    <p>2. Bones and blood are examples of which primary tissue type?</p>
                    <label><input type="radio" name="q4" value="a"> Epithelial</label>
                    <label><input type="radio" name="q4" value="b"> Connective</label>
                    <label><input type="radio" name="q4" value="c"> Muscle</label>
                    <label><input type="radio" name="q4" value="d"> Nervous</label>
                    <button class="check-answer" data-correct="b">Check Answer</button>
                    <p class="quiz-feedback"></p>
                </div>
            </div>
            </section>
    </main>

    <footer>
        <p>&copy; 2024 Anatomy & Physiology for Biomedical Engineers. All rights reserved.</p>
    </footer>

    <script src="js/script.js"></script>
    </body>
</html>
2. css/style.css
CSS

/* General Body and Typography */
:root {
    --primary-color: #0056b3; /* Dark Blue */
    --secondary-color: #007bff; /* Bright Blue */
    --accent-color: #28a745; /* Green for CTA/Highlights */
    --text-color: #333;
    --background-light: #f8f9fa; /* Light Gray */
    --background-dark: #e9ecef; /* Slightly darker gray */
    --border-color: #ced4da;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --hover-light: rgba(0, 123, 255, 0.1);
    --animation-duration: 0.3s;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    color: var(--text-color);
    background-color: var(--background-light);
    scroll-behavior: smooth;
}

h1, h2, h3, h4, h5, h6 {
    color: var(--primary-color);
    margin-bottom: 0.5em;
    font-weight: 600;
}

h1 { font-size: 2.8em; }
h2 { font-size: 2.2em; border-bottom: 2px solid var(--border-color); padding-bottom: 0.3em; margin-top: 1.5em;}
h3 { font-size: 1.8em; }
h4 { font-size: 1.5em; }
h5 { font-size: 1.2em; }

p {
    margin-bottom: 1em;
}

a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color var(--animation-duration) ease;
}

a:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

/* Header & Navigation */
.sticky-header {
    position: sticky;
    top: 0;
    width: 100%;
    background-color: #fff;
    box-shadow: 0 2px 5px var(--shadow-color);
    z-index: 1000;
    padding: 0.8em 0;
}

.main-nav ul {
    display: flex;
    justify-content: center;
    gap: 25px;
}

.main-nav li a {
    padding: 10px 15px;
    color: var(--primary-color);
    font-weight: bold;
    border-radius: 5px;
    transition: background-color var(--animation-duration) ease, color var(--animation-duration) ease;
}

.main-nav li a:hover,
.main-nav li a.active {
    background-color: var(--secondary-color);
    color: #fff;
    text-decoration: none;
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 70vh; /* Adjust as needed */
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #fff;
    overflow: hidden;
    background-color: var(--primary-color); /* Fallback */
    background-image: url('../assets/images/hero-background.gif'); /* Captivating animation GIF */
    background-size: cover;
    background-position: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Overlay for readability */
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 900px;
    padding: 20px;
}

.hero-content h1 {
    font-size: 3.5em;
    margin-bottom: 15px;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

.hero-content h2 {
    font-size: 1.8em;
    margin-bottom: 30px;
    color: #eee;
    border-bottom: none;
}

.hero-content p {
    font-size: 1.1em;
    color: #ddd;
}

.cta-button {
    background-color: var(--accent-color);
    color: #fff;
    padding: 15px 30px;
    font-size: 1.2em;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color var(--animation-duration) ease, transform var(--animation-duration) ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.cta-button:hover {
    background-color: #218838;
    transform: translateY(-3px);
}

/* Main Content Modules */
main {
    padding: 20px;
    max-width: 1200px;
    margin: 20px auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 15px var(--shadow-color);
}

.module-section {
    padding: 40px 20px;
    margin-bottom: 30px;
    border-bottom: 1px dashed var(--border-color);
}

.module-section:last-child {
    border-bottom: none;
}

/* Glossary & Term Highlighting */
.glossary-term {
    color: var(--secondary-color);
    font-weight: bold;
    cursor: help;
    border-bottom: 1px dashed var(--secondary-color);
    position: relative;
}

.glossary-term:hover::after {
    content: attr(data-definition); /* Dynamically set by JS */
    position: absolute;
    bottom: 120%; /* Position above the text */
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: #fff;
    padding: 8px 12px;
    border-radius: 5px;
    white-space: nowrap;
    font-size: 0.9em;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--animation-duration) ease, visibility var(--animation-duration) ease;
}

.glossary-term:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Glossary Sidebar */
.glossary-sidebar {
    height: 100%;
    width: 0;
    position: fixed;
    z-index: 1001;
    top: 0;
    right: 0;
    background-color: var(--background-dark);
    overflow-x: hidden;
    transition: 0.5s;
    padding-top: 60px;
    box-shadow: -5px 0 15px var(--shadow-color);
    color: var(--text-color);
}

.glossary-sidebar.open {
    width: 350px;
    padding-left: 25px;
    padding-right: 25px;
}

.glossary-sidebar h2 {
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    color: var(--primary-color);
}

.glossary-sidebar .close-btn {
    position: absolute;
    top: 0;
    right: 25px;
    font-size: 36px;
    margin-left: 50px;
    cursor: pointer;
    color: #555;
}

.glossary-sidebar .close-btn:hover {
    color: var(--primary-color);
}

#glossary-search {
    width: calc(100% - 20px);
    padding: 10px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 1em;
}

#glossary-content div {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px dashed var(--border-color);
}

#glossary-content div:last-child {
    border-bottom: none;
}

#glossary-content h4 {
    margin: 0;
    color: var(--secondary-color);
    cursor: pointer;
}

#glossary-content h4:hover {
    text-decoration: underline;
}

#glossary-content p {
    font-size: 0.95em;
    margin-top: 5px;
    color: #555;
    display: none; /* Initially hidden */
}

/* General Interactive Elements & Placeholders */
.model-container, .simulation-area {
    background-color: var(--background-dark);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin: 20px auto;
    padding: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.model-container img, .simulation-area img {
    max-width: 100%;
    height: auto;
    border-radius: 5px;
    opacity: 0.8; /* To signify it's a placeholder */
    transition: opacity var(--animation-duration) ease;
}

.model-container .model-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.4);
    color: white;
    font-size: 1.2em;
    border-radius: 8px;
}

.model-container .model-overlay p {
    margin-bottom: 20px;
}

.model-container .cell-parts-legend,
.model-container .organ-layer-label,
.model-container .system-hotspots .hotspot {
    cursor: pointer;
    background-color: var(--secondary-color);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.9em;
    position: absolute; /* Specific positioning handled by JS or inline style */
    opacity: 0.9;
    transition: opacity var(--animation-duration) ease, transform var(--animation-duration) ease;
}

.model-container .cell-parts-legend li:hover,
.model-container .organ-layer-label:hover,
.model-container .system-hotspots .hotspot:hover {
    opacity: 1;
    transform: scale(1.05);
    background-color: var(--primary-color);
}

.cell-parts-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    padding: 15px;
    background-color: rgba(0,0,0,0.6);
    border-radius: 5px;
}
.cell-parts-legend li {
    padding: 8px 12px;
    background-color: var(--secondary-color);
    border-radius: 4px;
    transition: background-color 0.2s ease;
}
.cell-parts-legend li:hover {
    background-color: var(--primary-color);
}


.pop-up-info {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 5px 15px var(--shadow-color);
    padding: 25px;
    z-index: 1002;
    max-width: 500px;
    text-align: left;
}

.pop-up-info h3 {
    margin-top: 0;
    color: var(--primary-color);
}

.pop-up-info .close-popup {
    float: right;
    background: none;
    border: none;
    font-size: 1.5em;
    cursor: pointer;
    color: #888;
}

.pop-up-info .close-popup:hover {
    color: var(--primary-color);
}

.pop-up-info video {
    width: 100%;
    height: auto;
    margin-top: 15px;
    border-radius: 5px;
}

.hidden {
    display: none !important;
}

.video-container {
    position: relative;
    width: 100%;
    padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
    height: 0;
    overflow: hidden;
    margin: 20px 0;
    background-color: var(--background-dark);
    border-radius: 8px;
}

.video-container.small-video {
    width: 60%; /* Smaller for embedded snippets */
    padding-bottom: 33.75%; /* 16:9 for 60% width */
    margin: 15px auto;
}


.video-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

.responsive-video {
    max-width: 100%;
    height: auto;
}

/* Simulation Controls */
.simulation-controls {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    padding: 15px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 5px;
    width: calc(100% - 40px);
}

.simulation-controls label {
    font-weight: bold;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 5px;
}

.simulation-controls input[type="range"] {
    width: 150px;
    cursor: pointer;
}

.simulation-controls button {
    padding: 10px 20px;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color var(--animation-duration) ease;
}

.simulation-controls button:hover {
    background-color: var(--primary-color);
}

.simulation-feedback {
    margin-top: 10px;
    font-style: italic;
    color: #666;
    min-height: 20px; /* Prevent layout shift */
}

/* Cell Type Showcase */
.cell-type-showcase {
    margin-top: 40px;
}

.cell-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.cell-card {
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 5px var(--shadow-color);
    transition: transform var(--animation-duration) ease;
}

.cell-card:hover {
    transform: translateY(-5px);
}

.cell-card img {
    max-width: 80%;
    height: auto;
    margin-bottom: 10px;
    border-radius: 5px;
}

/* Tissue Explorer */
.tissue-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.tab-button {
    background-color: var(--background-dark);
    color: var(--primary-color);
    padding: 10px 20px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color var(--animation-duration) ease, color var(--animation-duration) ease;
}

.tab-button.active,
.tab-button:hover {
    background-color: var(--secondary-color);
    color: #fff;
}

.tissue-info-display {
    background-color: var(--background-dark);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    min-height: 350px;
    display: flex; /* For internal layout */
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
}

.tissue-content {
    display: none; /* Hide all by default, show active with JS */
    width: 100%;
    flex-wrap: wrap;
}

.tissue-content.active {
    display: flex;
}

.tissue-image-container {
    flex: 1;
    min-width: 300px;
    position: relative;
    text-align: center;
}

.tissue-image-container img {
    max-width: 90%;
    height: auto;
    border-radius: 5px;
    margin-bottom: 10px;
}

.tissue-image-container .image-labels .label {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 0.8em;
    white-space: nowrap;
    transform: translateX(-50%);
}

.tissue-text {
    flex: 2;
    min-width: 400px;
}

/* Virtual Microscope Simulation */
.microscope-stage {
    position: relative;
    width: 450px;
    height: 350px;
    background-color: #eee;
    border: 2px solid #555;
    border-radius: 10px;
    margin: 20px auto;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.microscope-stage img {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: contain;
    opacity: 0.3; /* Background */
}

.drop-zone {
    width: 150px;
    height: 150px;
    border: 2px dashed var(--secondary-color);
    background-color: rgba(0, 123, 255, 0.05);
    display: flex;
    justify-content: center;
    align-items: center;
    font-style: italic;
    color: #888;
    border-radius: 50%;
    position: relative;
    z-index: 10;
    transition: background-color var(--animation-duration) ease, border-color var(--animation-duration) ease;
}

.drop-zone.hover {
    background-color: rgba(0, 123, 255, 0.2);
    border-color: var(--primary-color);
}

.tissue-samples-palette {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

.tissue-sample {
    background-color: var(--primary-color);
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: grab;
    transition: background-color var(--animation-duration) ease, transform var(--animation-duration) ease;
}

.tissue-sample:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
}

.tissue-sample.dragging {
    opacity: 0.7;
    cursor: grabbing;
}

/* Organ Builder/Dissector */
.organ-selector {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    gap: 10px;
    flex-wrap: wrap;
}

.organ-button {
    background-color: var(--background-dark);
    color: var(--primary-color);
    padding: 10px 20px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color var(--animation-duration) ease, color var(--animation-duration) ease;
}

.organ-button.active,
.organ-button:hover {
    background-color: var(--secondary-color);
    color: #fff;
}

.organ-display-area {
    background-color: var(--background-dark);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    min-height: 450px;
    display: flex; /* For internal layout */
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
}

.organ-view {
    display: none; /* Hide all by default, show active with JS */
    width: 100%;
    flex-wrap: wrap;
}

.organ-view.active {
    display: flex;
}

.organ-view .model-container {
    flex: 1;
    min-width: 300px;
    position: relative;
    background-color: #fff; /* Override dark background for organ model */
}

.organ-info-panel {
    flex: 2;
    min-width: 400px;
}

/* Human Body Map */
.interactive-human-body-map .model-container {
    background-color: #f0f0f0; /* Lighter background for the body map */
    min-height: 500px;
    position: relative;
}

.interactive-human-body-map .model-container img {
    max-height: 500px; /* Limit height of placeholder image */
    width: auto;
    object-fit: contain;
    opacity: 1;
}

.system-hotspots .hotspot {
    position: absolute;
    width: 50px; /* Size of clickable area */
    height: 50px;
    border-radius: 50%;
    background-color: rgba(255, 0, 0, 0.4); /* Red transparent for visibility */
    cursor: pointer;
    opacity: 0.7;
    transition: background-color var(--animation-duration) ease, opacity var(--animation-duration) ease, transform var(--animation-duration) ease;
    border: 2px solid rgba(255, 0, 0, 0.8);
    transform: translate(-50%, -50%); /* Center the hotspot */
}

.system-hotspots .hotspot:hover {
    background-color: rgba(255, 0, 0, 0.7);
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
}

.system-info-panel {
    background-color: var(--background-dark);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    min-height: 300px;
}

.system-content-display {
    padding-top: 10px;
}

.system-content {
    display: none;
}

.system-content.active {
    display: block;
}

/* System Integration - Case Studies */
.case-study-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.case-tab-button {
    background-color: var(--background-dark);
    color: var(--primary-color);
    padding: 10px 20px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color var(--animation-duration) ease, color var(--animation-duration) ease;
}

.case-tab-button.active,
.case-tab-button:hover {
    background-color: var(--secondary-color);
    color: #fff;
}

.case-study-display {
    background-color: var(--background-dark);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    min-height: 400px;
}

.case-study-content {
    display: none;
}

.case-study-content.active {
    display: block;
}

.real-time-metrics {
    display: flex;
    gap: 25px;
    margin-top: 15px;
    font-size: 1.1em;
    font-weight: bold;
    color: var(--primary-color);
}

.real-time-metrics span {
    color: var(--accent-color);
}

.interactive-quiz {
    background-color: #f0f8ff; /* Light blue background */
    border: 1px solid #cceeff;
    border-left: 5px solid var(--secondary-color);
    border-radius: 8px;
    padding: 20px;
    margin-top: 30px;
}

.interactive-quiz h5 {
    color: var(--secondary-color);
    margin-bottom: 10px;
}

.interactive-quiz textarea {
    width: calc(100% - 20px);
    padding: 10px;
    margin-top: 10px;
    margin-bottom: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 1em;
    resize: vertical;
}

.interactive-quiz .submit-answer {
    background-color: var(--accent-color);
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color var(--animation-duration) ease;
}

.interactive-quiz .submit-answer:hover {
    background-color: #218838;
}

.interactive-quiz .quiz-feedback {
    margin-top: 10px;
    font-weight: bold;
    color: #e74c3c; /* Red for wrong, green for correct */
}


/* Knowledge Check Quizzes */
.quiz-section {
    padding: 40px 20px;
    margin-top: 30px;
    background-color: var(--background-dark);
    border-radius: 8px;
    box-shadow: 0 0 10px var(--shadow-color);
}

.quiz-module {
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 5px var(--shadow-color);
}

.quiz-module h3 {
    color: var(--primary-color);
    border-bottom: 1px dashed var(--border-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.quiz-module .question {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px dotted #ccc;
}

.quiz-module .question:last-child {
    border-bottom: none;
}

.quiz-module .question p {
    font-weight: bold;
    margin-bottom: 10px;
}

.quiz-module .question label {
    display: block;
    margin-bottom: 8px;
    cursor: pointer;
}

.quiz-module .question label input[type="radio"] {
    margin-right: 8px;
    vertical-align: middle;
}

.quiz-module .question .check-answer {
    background-color: var(--secondary-color);
    color: white;
    padding: 8px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 10px;
    transition: background-color var(--animation-duration) ease;
}

.quiz-module .question .check-answer:hover {
    background-color: var(--primary-color);
}

.quiz-module .quiz-feedback {
    margin-top: 10px;
    font-weight: bold;
    min-height: 20px;
}

.quiz-feedback.correct {
    color: var(--accent-color);
}

.quiz-feedback.incorrect {
    color: #e74c3c;
}

/* Footer */
footer {
    text-align: center;
    padding: 25px;
    margin-top: 40px;
    background-color: var(--primary-color);
    color: white;
    font-size: 0.9em;
    border-radius: 0 0 8px 8px;
}

/* Responsive Design */
@media (max-width: 992px) {
    .main-nav ul {
        flex-wrap: wrap;
        gap: 15px;
    }
    .hero-content h1 {
        font-size: 2.5em;
    }
    .hero-content h2 {
        font-size: 1.4em;
    }
    .module-section {
        padding: 30px 15px;
    }
    .video-container.small-video {
        width: 80%;
        padding-bottom: 45%;
    }
    .tissue-info-display, .organ-display-area {
        flex-direction: column;
        align-items: center;
    }
    .tissue-image-container, .tissue-text,
    .organ-view .model-container, .organ-info-panel {
        min-width: unset;
        width: 100%;
    }
    .interactive-human-body-map .model-container {
        min-height: 400px;
    }
    .system-hotspots .hotspot {
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 768px) {
    .main-nav ul {
        flex-direction: column;
        align-items: center;
    }
    .sticky-header {
        padding: 0.5em 0;
    }
    .main-nav li a {
        padding: 8px 12px;
    }
    .hero-section {
        height: 60vh;
    }
    .hero-content h1 {
        font-size: 2em;
    }
    .hero-content h2 {
        font-size: 1.2em;
    }
    .cta-button {
        padding: 12px 25px;
        font-size: 1em;
    }
    main {
        margin: 10px auto;
        padding: 15px;
    }
    .glossary-sidebar.open {
        width: 100%; /* Full width on smaller screens */
        padding-left: 15px;
        padding-right: 15px;
    }
    .simulation-controls {
        flex-direction: column;
        gap: 10px;
    }
    .microscope-stage {
        width: 90%;
        height: 250px;
    }
    .drop-zone {
        width: 100px;
        height: 100px;
    }
    .tissue-samples-palette {
        flex-wrap: wrap;
    }
    .video-container.small-video {
        width: 95%;
        padding-bottom: 53.4375%; /* 16:9 for 95% width */
    }
    .interactive-human-body-map .model-container {
        min-height: 350px;
    }
}

@media (max-width: 480px) {
    h1 { font-size: 2.2em; }
    h2 { font-size: 1.8em; }
    h3 { font-size: 1.4em; }
    .hero-content h1 {
        font-size: 1.8em;
    }
    .hero-content h2 {
        font-size: 1em;
    }
    .cell-types-grid {
        grid-template-columns: 1fr; /* Stack cards */
    }
}
3. js/script.js
JavaScript

document.addEventListener('DOMContentLoaded', () => {
    // Smooth scrolling for navigation
    document.querySelectorAll('nav a').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            if (targetSection) {
                targetSection.scrollIntoView({ behavior: 'smooth' });
                // Update active class for sticky header
                document.querySelectorAll('nav a').forEach(item => item.classList.remove('active'));
                this.classList.add('active');
            }
        });
    });

    // Initial check for active nav link on load
    const observerOptions = {
        root: null,
        rootMargin: '0px',
        threshold: 0.5 // Adjust as needed
    };

    const sectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                document.querySelectorAll('nav a').forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${entry.target.id}`) {
                        link.classList.add('active');
                    }
                });
            }
        });
    }, observerOptions);

    document.querySelectorAll('section[id]').forEach(section => {
        sectionObserver.observe(section);
    });


    // Begin Journey button functionality
    const beginJourneyBtn = document.getElementById('begin-journey-btn');
    if (beginJourneyBtn) {
        beginJourneyBtn.addEventListener('click', () => {
            const firstModule = document.getElementById('cellular-foundation');
            if (firstModule) {
                firstModule.scrollIntoIntoView({ behavior: 'smooth' });
            }
        });
    }

    // --- Interactive Glossary ---
    const glossaryTerms = {
        "anterior": "Towards the front of the body.",
        "posterior": "Towards the back of the body.",
        "superior": "Towards the head or upper part of a structure.",
        "inferior": "Away from the head or toward the lower part of a structure.",
        "medial": "Towards the midline of the body.",
        "lateral": "Away from the midline of the body.",
        "proximal": "Closer to the origin of the body part or point of attachment of a limb to the body trunk.",
        "distal": "Farther from the origin of a body part or point of attachment of a limb to the body trunk.",
        "homeostasis": "The ability of the body to maintain a stable internal environment despite changes in external conditions.",
        "metabolism": "All chemical reactions that occur within the body, including catabolism (breaking down) and anabolism (building up).",
        "tissue": "A group of similar cells and their extracellular matrix, organized to perform a specific function.",
        "organ": "A collection of tissues joined in a structural unit to serve a common function."
    };

    const glossarySidebar = document.getElementById('glossary-sidebar');
    const glossaryToggle = document.getElementById('glossary-toggle');
    const closeGlossary = document.getElementById('close-glossary');
    const glossarySearch = document.getElementById('glossary-search');
    const glossaryContent = document.getElementById('glossary-content');

    // Populate glossary terms
    function populateGlossary() {
        glossaryContent.innerHTML = '';
        const sortedTerms = Object.keys(glossaryTerms).sort();
        sortedTerms.forEach(term => {
            const termDiv = document.createElement('div');
            termDiv.innerHTML = `<h4>${term.charAt(0).toUpperCase() + term.slice(1)}</h4><p>${glossaryTerms[term]}</p>`;
            glossaryContent.appendChild(termDiv);
        });
    }

    populateGlossary();

    glossaryToggle.addEventListener('click', (e) => {
        e.preventDefault();
        glossarySidebar.classList.add('open');
        document.body.style.overflow = 'hidden'; // Prevent scrolling background
    });

    closeGlossary.addEventListener('click', () => {
        glossarySidebar.classList.remove('open');
        document.body.style.overflow = ''; // Restore scrolling
    });

    glossarySearch.addEventListener('keyup', () => {
        const searchTerm = glossarySearch.value.toLowerCase();
        const terms = glossaryContent.querySelectorAll('div');
        terms.forEach(termDiv => {
            const termName = termDiv.querySelector('h4').textContent.toLowerCase();
            if (termName.includes(searchTerm)) {
                termDiv.style.display = 'block';
            } else {
                termDiv.style.display = 'none';
            }
        });
    });

    // Add hover definitions for glossary terms in main content
    document.querySelectorAll('.glossary-term').forEach(termSpan => {
        const termKey = termSpan.dataset.term;
        if (glossaryTerms[termKey]) {
            termSpan.setAttribute('data-definition', glossaryTerms[termKey]);
        }
    });

    // --- Module 1: Cellular Foundation ---

    // Interactive 3D Cell Model (Placeholder interaction)
    const cellModelContainer = document.getElementById('threejs-cell-model-container');
    const cellPartInfo = document.getElementById('cell-part-info');
    const cellPartInfoTitle = cellPartInfo.querySelector('h3');
    const cellPartInfoDesc = cellPartInfo.querySelector('p');
    const cellPartInfoVideo = cellPartInfo.querySelector('video');
    const closePopupBtn = cellPartInfo.querySelector('.close-popup');

    const cellPartDetails = {
        "nucleus": {
            title: "Nucleus",
            description: "The control center of the cell, containing the cell's genetic material (DNA) and regulating cell growth, metabolism, and reproduction.",
            video: "assets/videos/protein_synthesis.mp4"
        },
        "mitochondria": {
            title: "Mitochondria",
            description: "Often called the 'powerhouses of the cell', mitochondria generate most of the chemical energy needed to power the cell's biochemical reactions, primarily through ATP production.",
            video: "assets/videos/atp_production.mp4"
        },
        "er": {
            title: "Endoplasmic Reticulum (ER)",
            description: "A network of membranes involved in protein and lipid synthesis, and detoxification. Rough ER has ribosomes for protein synthesis, smooth ER for lipid synthesis and detoxification.",
            video: "" // No specific video for ER placeholder
        },
        "golgi": {
            title: "Golgi Apparatus",
            description: "Modifies, sorts, and packages proteins and lipids for secretion or delivery to other organelles.",
            video: "" // No specific video for Golgi placeholder
        }
    };

    // Simulate clicking on a 3D model part
    cellModelContainer.addEventListener('click', (e) => {
        const clickedPart = e.target.closest('.cell-parts-legend li');
        if (clickedPart) {
            const partKey = clickedPart.dataset.part;
            const details = cellPartDetails[partKey];
            if (details) {
                cellPartInfoTitle.textContent = details.title;
                cellPartInfoDesc.textContent = details.description;
                if (details.video) {
                    cellPartInfoVideo.src = details.video;
                    cellPartInfoVideo.classList.remove('hidden');
                    cellPartInfoVideo.load(); // Load video to show poster
                } else {
                    cellPartInfoVideo.classList.add('hidden');
                    cellPartInfoVideo.pause();
                    cellPartInfoVideo.currentTime = 0;
                }
                cellPartInfo.classList.remove('hidden');
            }
        }
    });

    closePopupBtn.addEventListener('click', () => {
        cellPartInfo.classList.add('hidden');
        cellPartInfoVideo.pause();
    });


    // Membrane Transport Simulation (Placeholder interaction)
    const runTransportSimBtn = document.getElementById('run-transport-sim');
    const gradientSlider = document.getElementById('gradient-slider');
    const atpToggle = document.getElementById('atp-toggle');
    const transportFeedback = document.querySelector('#membrane-transport-simulation .simulation-feedback');

    if (runTransportSimBtn) {
        runTransportSimBtn.addEventListener('click', () => {
            const gradientValue = gradientSlider.value;
            const atpActive = atpToggle.checked;
            let message = "Simulating membrane transport: ";

            if (gradientValue > 70 && !atpActive) {
                message += "High concentration gradient, favoring rapid diffusion. Facilitated diffusion also active.";
            } else if (gradientValue < 30 && atpActive) {
                message += "Low concentration gradient, but ATP is present, enabling active transport against the gradient.";
            } else if (gradientValue > 50 && atpActive) {
                message += "Both diffusion and active transport are at play, with active transport overcoming smaller gradients.";
            } else if (!atpActive) {
                message += "Only passive transport (diffusion, facilitated diffusion, osmosis) is occurring based on gradient.";
            } else {
                message += "Transport mechanisms adapting to conditions.";
            }

            transportFeedback.textContent = message;
            // In a real simulation, you'd update a canvas/WebGL model here
        });
    }


    // Action Potential Simulation (Placeholder interaction)
    const runApSimBtn = document.getElementById('run-ap-sim');
    const thresholdSlider = document.getElementById('threshold-slider');
    const ionChannelDensitySlider = document.getElementById('ion-channel-density-slider');
    const apGraphPlaceholder = document.querySelector('.ap-graph-placeholder');

    if (runApSimBtn) {
        runApSimBtn.addEventListener('click', () => {
            const threshold = thresholdSlider.value;
            const ionDensity = ionChannelDensitySlider.value;
            apGraphPlaceholder.innerHTML = `<p>Simulating Action Potential with Threshold: ${threshold}%, Ion Channel Density: ${ionDensity}%.</p>`;
            apGraphPlaceholder.innerHTML += `<p>Graph animation of resting potential, depolarization, repolarization, and hyperpolarization would play here, affected by parameters.</p>`;
            // In a real simulation, you'd use a charting library (e.g., Chart.js) or D3.js to animate the graph
        });
    }

    // --- Module 2: Tissues ---
    const tissueTabButtons = document.querySelectorAll('.tissue-tabs .tab-button');
    const tissueContents = document.querySelectorAll('.tissue-info-display .tissue-content');

    tissueTabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active class from all buttons and content
            tissueTabButtons.forEach(btn => btn.classList.remove('active'));
            tissueContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked button and corresponding content
            button.classList.add('active');
            const targetTissue = button.dataset.tissue;
            document.getElementById(`${targetTissue}-tissue-content`).classList.add('active');
        });
    });

    // Virtual Microscope Simulation (Drag and Drop Placeholder)
    const tissueSamples = document.querySelectorAll('.tissue-sample');
    const tissueDropZone = document.getElementById('tissue-drop-zone');
    const microscopeFeedback = document.getElementById('microscope-feedback');
    let draggedTissueType = null;

    tissueSamples.forEach(sample => {
        sample.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', e.target.dataset.tissueType);
            draggedTissueType = e.target.dataset.tissueType;
            e.target.classList.add('dragging');
        });
        sample.addEventListener('dragend', (e) => {
            e.target.classList.remove('dragging');
        });
    });

    if (tissueDropZone) {
        tissueDropZone.addEventListener('dragover', (e) => {
            e.preventDefault(); // Allow drop
            tissueDropZone.classList.add('hover');
        });

        tissueDropZone.addEventListener('dragleave', () => {
            tissueDropZone.classList.remove('hover');
        });

        tissueDropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            tissueDropZone.classList.remove('hover');
            const droppedTissueType = e.dataTransfer.getData('text/plain');

            // Simulate the correct answer logic for a specific tissue type
            const correctTissueMapping = {
                "epithelial": "epithelial",
                "connective": "connective",
                "muscle": "muscle",
                "nervous": "nervous"
            };

            const randomCorrectTissue = Object.keys(correctTissueMapping)[Math.floor(Math.random() * Object.keys(correctTissueMapping).length)]; // For demo

            if (droppedTissueType === randomCorrectTissue) { // In real, this would be based on the image shown
                microscopeFeedback.textContent = `Correct! You identified ${droppedTissueType} tissue.`;
                microscopeFeedback.style.color = 'var(--accent-color)';
                // In a real simulation, you would change the microscope image to match the dropped tissue and show labels.
            } else {
                microscopeFeedback.textContent = `Incorrect. That looks like ${droppedTissueType} tissue, but this sample is ${randomCorrectTissue}. Try again!`;
                microscopeFeedback.style.color = '#e74c3c';
            }
        });
    }


    // --- Module 3: Organs ---
    const organButtons = document.querySelectorAll('.organ-selector .organ-button');
    const organViews = document.querySelectorAll('.organ-display-area .organ-view');

    organButtons.forEach(button => {
        button.addEventListener('click', () => {
            organButtons.forEach(btn => btn.classList.remove('active'));
            organViews.forEach(view => view.classList.remove('active'));

            button.classList.add('active');
            const targetOrgan = button.dataset.organ;
            document.getElementById(`organ-${targetOrgan}`).classList.add('active');
        });
    });

    // Simulate organ dissection/layer info
    document.querySelectorAll('.organ-layer-label').forEach(label => {
        label.addEventListener('click', (e) => {
            const layer = e.target.dataset.layer;
            alert(`You clicked on the ${layer} layer! In a real 3D model, this would highlight the layer and show detailed information.`);
        });
    });

    // --- Module 4: Body Systems ---
    const systemHotspots = document.querySelectorAll('.system-hotspots .hotspot');
    const systemInfoPanel = document.getElementById('system-info-panel');
    const systemContents = document.querySelectorAll('.system-content-display .system-content');

    systemHotspots.forEach(hotspot => {
        hotspot.addEventListener('click', () => {
            const system = hotspot.dataset.system;

            // Remove active from all system contents
            systemContents.forEach(content => content.classList.remove('active'));

            // Activate the selected system's content
            const targetContent = document.getElementById(`${system}-system-content`);
            if (targetContent) {
                targetContent.classList.add('active');
                systemInfoPanel.querySelector('h3').textContent = 'Selected System:';
            } else {
                systemInfoPanel.querySelector('h3').textContent = 'System Details Not Found';
            }
        });
    });

    // --- Module 5: System Integration ---
    const caseTabButtons = document.querySelectorAll('.case-study-tabs .case-tab-button');
    const caseStudyContents = document.querySelectorAll('.case-study-display .case-study-content');

    caseTabButtons.forEach(button => {
        button.addEventListener('click', () => {
            caseTabButtons.forEach(btn => btn.classList.remove('active'));
            caseStudyContents.forEach(content => content.classList.remove('active'));

            button.classList.add('active');
            const targetScenario = button.dataset.scenario;
            document.getElementById(`${targetScenario}-scenario`).classList.add('active');
        });
    });

    // Exercise Physiology Simulation (Placeholder)
    const exerciseIntensitySlider = document.getElementById('exercise-intensity');
    const startExerciseSimBtn = document.getElementById('start-exercise-sim');
    const hrDisplay = document.getElementById('hr-display');
    const brDisplay = document.getElementById('br-display');
    const o2Display = document.getElementById('o2-display');
    const exerciseFeedback = document.querySelector('#exercise-simulation .simulation-feedback');

    let exerciseInterval = null;

    if (startExerciseSimBtn) {
        startExerciseSimBtn.addEventListener('click', () => {
            if (exerciseInterval) {
                clearInterval(exerciseInterval);
                exerciseInterval = null;
                startExerciseSimBtn.textContent = "Start Exercise";
                exerciseFeedback.textContent = "Exercise simulation stopped.";
                return;
            }

            startExerciseSimBtn.textContent = "Stop Exercise";
            exerciseFeedback.textContent = "Simulating exercise...";

            exerciseInterval = setInterval(() => {
                const intensity = parseInt(exerciseIntensitySlider.value);
                const baseHR = 70;
                const baseBR = 12;
                const baseO2 = 0.25;

                const currentHR = baseHR + (intensity / 100) * 120; // Max HR 190
                const currentBR = baseBR + (intensity / 100) * 30;  // Max BR 42
                const currentO2 = baseO2 + (intensity / 100) * 2;   // Max O2 2.25

                hrDisplay.textContent = Math.round(currentHR);
                brDisplay.textContent = Math.round(currentBR);
                o2Display.textContent = currentO2.toFixed(2);

                if (intensity > 70) {
                    exerciseFeedback.textContent = "High intensity! Cardiovascular and respiratory systems are working hard. Musculoskeletal system is highly active, requiring more ATP.";
                } else if (intensity > 30) {
                    exerciseFeedback.textContent = "Moderate intensity. Systems are adapting efficiently. Nervous system coordinates muscle contractions.";
                } else {
                    exerciseFeedback.textContent = "Low intensity. Body systems are maintaining homeostasis with minimal effort.";
                }

            }, 1000); // Update every second
        });
    }

    // Quiz functionality
    document.querySelectorAll('.quiz-module .check-answer').forEach(button => {
        button.addEventListener('click', () => {
            const questionDiv = button.closest('.question');
            const radios = questionDiv.querySelectorAll('input[type="radio"]');
            let selectedValue = null;
            radios.forEach(radio => {
                if (radio.checked) {
                    selectedValue = radio.value;
                }
            });

            const correctAnswer = button.dataset.correct;
            const feedbackElement = questionDiv.querySelector('.quiz-feedback');

            if (selectedValue === correctAnswer) {
                feedbackElement.textContent = "Correct! Well done.";
                feedbackElement.classList.remove('incorrect');
                feedbackElement.classList.add('correct');
            } else {
                feedbackElement.textContent = `Incorrect. The correct answer was ${correctAnswer.toUpperCase()}.`;
                feedbackElement.classList.remove('correct');
                feedbackElement.classList.add('incorrect');
            }
        });
    });

    document.querySelectorAll('.interactive-quiz .submit-answer').forEach(button => {
        button.addEventListener('click', () => {
            const quizDiv = button.closest('.interactive-quiz');
            const textarea = quizDiv.querySelector('textarea');
            const feedbackElement = quizDiv.querySelector('.quiz-feedback');

            if (textarea.value.trim() === '') {
                feedbackElement.textContent = "Please type your answer before submitting.";
                feedbackElement.style.color = '#e74c3c';
            } else {
                feedbackElement.textContent = "Thank you for your response! In a real scenario, this would be evaluated by an instructor or AI.";
                feedbackElement.style.color = 'var(--accent-color)';
                textarea.value = ''; // Clear for next try
            }
        });
    });


    // --- Three.js Integration (Conceptual) ---
    /*
    This section outlines where Three.js code would go.
    You would need to include Three.js and GLTFLoader scripts in your HTML.

    function load3DModel(containerId, modelPath) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        renderer.setSize(container.clientWidth, container.clientHeight);
        container.appendChild(renderer.domElement);

        const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
        scene.add(ambientLight);
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
        directionalLight.position.set(1, 1, 1);
        scene.add(directionalLight);

        const loader = new THREE.GLTFLoader();
        loader.load(modelPath, (gltf) => {
            const model = gltf.scene;
            scene.add(model);
            // Center the model
            const bbox = new THREE.Box3().setFromObject(model);
            const center = bbox.getCenter(new THREE.Vector3());
            const size = bbox.getSize(new THREE.Vector3());
            const maxDim = Math.max(size.x, size.y, size.z);
            const fov = camera.fov * (Math.PI / 180);
            let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2));
            cameraZ *= 1.5; // Adjust distance
            camera.position.set(center.x, center.y, center.z + cameraZ);
            camera.lookAt(center);

            // Basic rotation for demo
            function animate() {
                requestAnimationFrame(animate);
                model.rotation.y += 0.005;
                renderer.render(scene, camera);
            }
            animate();

            // Example: Add click interaction for cell parts (more complex for general model)
            // You'd typically add raycasting to detect clicks on specific mesh parts
            // container.addEventListener('click', (event) => {
            //     const raycaster = new THREE.Raycaster();
            //     const mouse = new THREE.Vector2();
            //     mouse.x = (event.clientX / container.clientWidth) * 2 - 1;
            //     mouse.y = -(event.clientY / container.clientHeight) * 2 + 1;
            //     raycaster.setFromCamera(mouse, camera);
            //     const intersects = raycaster.intersectObjects(model.children, true);
            //     if (intersects.length > 0) {
            //         console.log('Clicked on:', intersects[0].object.name);
            //         // Trigger your pop-up info here based on intersects[0].object.name
            //     }
            // });

        }, undefined, (error) => {
            console.error('An error occurred loading the 3D model:', error);
            container.innerHTML = '<p>Error loading 3D model. Please ensure the model path is correct.</p>';
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = container.clientWidth / container.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(container.clientWidth, container.clientHeight);
        });
    }

    // Call this if Three.js is loaded
    // load3DModel('threejs-cell-model-container', 'assets/3d-models/generalized_cell.glb');
    // load3DModel('threejs-heart-model-container', 'assets/3d-models/heart.glb');
    // load3DModel('threejs-human-body-model-container', 'assets/3d-models/human_body_systems.glb');
    */
});