<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Human Body Explorer</title>
    <style>
        body {
            font-family: 'Arial Rounded MT Bold', 'Helvetica Rounded', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f8ff; /* AliceBlue */
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            max-width: 1200px;
            padding: 20px;
            box-sizing: border-box;
        }

        h1 {
            color: #0077cc; /* Bright Blue */
            margin-bottom: 20px;
            text-align: center;
        }

        .main-content {
            display: flex;
            flex-direction: row;
            width: 100%;
            gap: 20px;
        }

        .anatomy-display {
            flex: 2;
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 300px; /* Ensure SVG has some space */
            position: relative; /* For potential absolute positioning of tooltips if needed */
        }

        #human-torso-svg {
            width: 100%;
            max-width: 450px;
            height: auto;
            border: 2px solid #ccc;
            border-radius: 10px;
            background-color: #fff; /* White background for SVG area */
        }

        .organ {
            cursor: pointer;
            transition: opacity 0.2s ease-in-out, transform 0.1s ease-in-out;
            stroke: #333; /* Darker stroke for definition */
            stroke-width: 1;
        }

        .organ:hover {
            opacity: 0.7;
            transform: scale(1.02);
        }

        .organ.selected {
            stroke: #000000; /* Black */
            stroke-width: 3;
            opacity: 1;
        }
        
        .organ.highlight-test-correct {
            stroke: #00FF00; /* Green */
            stroke-width: 4;
        }

        .organ.highlight-test-incorrect {
            stroke: #FF0000; /* Red */
            stroke-width: 4;
        }


        .info-panel {
            flex: 1;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            min-width: 280px;
        }

        .info-panel h2 {
            margin-top: 0;
            color: #0077cc;
            font-size: 1.5em;
        }

        #organ-name-display {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 1.2em;
            font-weight: bold;
            box-sizing: border-box;
            background-color: #e9ecef; /* Light grey */
            text-align: center;
        }

        #organ-function-display {
            width: 100%;
            height: 150px;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 1em;
            line-height: 1.5;
            box-sizing: border-box;
            resize: vertical;
            background-color: #f8f9fa; /* Lighter grey */
        }
        
        .controls {
            margin-top: 20px;
            text-align: center;
        }

        #test-mode-button, #reset-view-button {
            padding: 12px 25px;
            font-size: 1em;
            color: white;
            background-color: #0077cc; /* Bright Blue */
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin: 5px;
        }

        #test-mode-button:hover, #reset-view-button:hover {
            background-color: #005fa3; /* Darker Blue */
        }
        
        #test-mode-button.active {
            background-color: #28a745; /* Green */
        }
        #test-mode-button.active:hover {
            background-color: #1e7e34; /* Darker Green */
        }

        #test-feedback, #test-prompt {
            margin-top: 15px;
            font-size: 1.1em;
            font-weight: bold;
            min-height: 2em; /* Reserve space */
        }
        #test-prompt {
            color: #dc3545; /* Red for prompt */
        }
        #test-feedback.correct {
            color: #28a745; /* Green */
        }
        #test-feedback.incorrect {
            color: #dc3545; /* Red */
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            .anatomy-display, .info-panel {
                flex: 1; /* Take full width when stacked */
                width: 100%;
            }
            #human-torso-svg {
                max-width: 100%; /* Allow SVG to fill container width */
            }
             #organ-function-display {
                height: 120px;
            }
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>Explore the Human Body</h1>

        <div class="main-content">
            <div class="anatomy-display">
                <svg id="human-torso-svg" viewBox="0 0 400 600" preserveAspectRatio="xMidYMid meet">
                    <!-- Torso Outline -->
                    <path d="M100,50 Q120,40 200,40 Q280,40 300,50 L320,150 Q330,250 320,350 L300,550 Q200,580 100,550 L80,350 Q70,250 80,150 Z" fill="#ffdAB9" stroke="#a0522d" stroke-width="2"/>

                    <!-- Organs: x, y, width, height or path d -->
                    <!-- Brain (simplified top head area) -->
                    <ellipse id="svg-brain" class="organ" cx="200" cy="70" rx="80" ry="40" fill="#FFC0CB" />

                    <!-- Lungs -->
                    <path id="svg-lung-left" class="organ" d="M160,120 Q100,150 110,250 Q130,280 160,270 L160,120 Z" fill="#ADD8E6" />
                    <path id="svg-lung-right" class="organ" d="M240,120 Q300,150 290,250 Q270,280 240,270 L240,120 Z" fill="#ADD8E6" />
                    
                    <!-- Heart -->
                    <path id="svg-heart" class="organ" d="M200,170 Q170,180 170,220 Q180,250 200,255 Q220,250 230,220 Q230,180 200,170 Z" fill="#FF6347" />

                    <!-- Liver -->
                    <path id="svg-liver" class="organ" d="M200,280 Q280,270 290,350 L180,360 Q150,330 200,280 Z" fill="#8B4513" />
                    
                    <!-- Gallbladder (small, under liver) -->
                    <ellipse id="svg-gallbladder" class="organ" cx="230" cy="335" rx="15" ry="10" fill="#3CB371" />

                    <!-- Stomach -->
                    <path id="svg-stomach" class="organ" d="M120,290 Q130,270 190,290 Q200,350 150,355 Q110,340 120,290 Z" fill="#FFA07A" />
                    
                    <!-- Spleen (behind stomach, to the left) -->
                    <ellipse id="svg-spleen" class="organ" cx="115" cy="330" rx="25" ry="15" fill="#DA70D6" transform="rotate(-20, 115, 330)" />

                    <!-- Pancreas (behind stomach, somewhat central) -->
                    <path id="svg-pancreas" class="organ" d="M140,350 Q200,340 240,360 L230,375 L150,370 Z" fill="#F0E68C" />

                    <!-- Kidneys -->
                    <ellipse id="svg-kidney-left" class="organ" cx="140" cy="400" rx="25" ry="35" fill="#A0522D" />
                    <ellipse id="svg-kidney-right" class="organ" cx="260" cy="400" rx="25" ry="35" fill="#A0522D" />

                    <!-- Small Intestine -->
                    <path id="svg-small-intestine" class="organ" d="M150,380 Q200,370 250,380 Q260,450 200,470 Q140,450 150,380 Z" fill="#FFD700" />
                    
                    <!-- Large Intestine (surrounding small intestine) -->
                    <path id="svg-large-intestine" class="organ" d="M130,370 Q120,480 200,500 Q280,480 270,370 L250,375 Q245,460 200,475 Q155,460 150,375 Z" fill="#D2B48C" />

                    <!-- Bladder -->
                    <ellipse id="svg-bladder" class="organ" cx="200" cy="520" rx="40" ry="25" fill="#FFFFE0" />
                </svg>
            </div>

            <div class="info-panel">
                <h2>Organ Information</h2>
                <input type="text" id="organ-name-display" value="Click an organ" readonly>
                <textarea id="organ-function-display" readonly>Select an organ to see its function.</textarea>
                
                <div class="controls">
                    <button id="test-mode-button">Test Your Knowledge</button>
                    <button id="reset-view-button">Reset View</button>
                    <div id="test-prompt"></div>
                    <div id="test-feedback"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const organDetails = {
            'brain': { name: 'Brain', function: 'Controls thought, memory, emotion, touch, motor skills, vision, breathing, temperature, hunger, and every process that regulates our body.', svgId: 'svg-brain' },
            'lungs': { name: 'Lungs (Pair)', function: 'Allow us to breathe. They bring oxygen into our bodies (inspiration/inhalation) and send carbon dioxide out (expiration/exhalation).', svgIds: ['svg-lung-left', 'svg-lung-right'] },
            'heart': { name: 'Heart', function: 'Pumps blood throughout the body, supplying oxygen and nutrients to the tissues and removing carbon dioxide and other wastes.', svgId: 'svg-heart' },
            'spleen': { name: 'Spleen', function: 'Filters blood, recycles old red blood cells, and stores white blood cells and platelets. Plays a role in the immune system.', svgId: 'svg-spleen' },
            'stomach': { name: 'Stomach', function: 'A J-shaped organ that digests food. It produces enzymes and acids (like hydrochloric acid) to break down food into a liquid mixture called chyme.', svgId: 'svg-stomach' },
            'pancreas': { name: 'Pancreas', function: 'Produces enzymes essential for digestion (breaking down carbohydrates, fats, and proteins) and hormones like insulin and glucagon to regulate blood sugar levels.', svgId: 'svg-pancreas' },
            'liver': { name: 'Liver', function: 'Filters blood from the digestive tract, detoxifies chemicals, metabolizes drugs, produces bile for digestion, and makes proteins important for blood clotting.', svgId: 'svg-liver' },
            'gallbladder': { name: 'Gallbladder', function: 'Stores and concentrates bile produced by the liver. It releases bile into the small intestine to help emulsify and digest fats.', svgId: 'svg-gallbladder' },
            'kidneys': { name: 'Kidneys (Pair)', function: 'Filter waste products and excess water from the blood to produce urine. They also regulate blood pressure, electrolyte balance, and red blood cell production.', svgIds: ['svg-kidney-left', 'svg-kidney-right'] },
            'small-intestine': { name: 'Small Intestine', function: 'The primary site for nutrient absorption from food. It is a long, coiled tube where most digestion is completed with the help of enzymes from the pancreas and bile from the liver.', svgId: 'svg-small-intestine' },
            'large-intestine': { name: 'Large Intestine', function: 'Absorbs water and electrolytes from the remaining indigestible food matter and transmits the useless waste material (feces) from the body.', svgId: 'svg-large-intestine' },
            'bladder': { name: 'Bladder', function: 'A hollow, muscular organ that stores urine produced by the kidneys, allowing urination to be infrequent and controlled.', svgId: 'svg-bladder' }
        };

        const organNameDisplay = document.getElementById('organ-name-display');
        const organFunctionDisplay = document.getElementById('organ-function-display');
        const svgOrganElements = document.querySelectorAll('.organ');
        const testModeButton = document.getElementById('test-mode-button');
        const resetViewButton = document.getElementById('reset-view-button');
        const testPromptDisplay = document.getElementById('test-prompt');
        const testFeedbackDisplay = document.getElementById('test-feedback');
        
        let currentSelectedOrganElement = null;
        let isTestMode = false;
        let currentTestOrganKey = null;
        let organKeys = Object.keys(organDetails);

        function getOrganKeyFromSvgId(svgId) {
            for (const key in organDetails) {
                if (organDetails[key].svgId === svgId) return key;
                if (organDetails[key].svgIds && organDetails[key].svgIds.includes(svgId)) return key;
            }
            return null;
        }
        
        function clearOrganHighlights() {
            svgOrganElements.forEach(el => {
                el.classList.remove('selected', 'highlight-test-correct', 'highlight-test-incorrect');
            });
            currentSelectedOrganElement = null;
        }

        function highlightOrgan(element, highlightClass = 'selected') {
            clearOrganHighlights();
            if (element) {
                element.classList.add(highlightClass);
                // If the organ has multiple parts, highlight them all
                const organKey = getOrganKeyFromSvgId(element.id);
                if (organKey && organDetails[organKey].svgIds) {
                    organDetails[organKey].svgIds.forEach(id => {
                        document.getElementById(id)?.classList.add(highlightClass);
                    });
                }
                currentSelectedOrganElement = element;
            }
        }
        
        function resetDisplays() {
            organNameDisplay.value = "Click an organ";
            organFunctionDisplay.value = "Select an organ to see its function.";
            testPromptDisplay.textContent = "";
            testFeedbackDisplay.textContent = "";
            testFeedbackDisplay.className = '';
            clearOrganHighlights();
        }

        function updateDisplays(organKey) {
            if (organDetails[organKey]) {
                organNameDisplay.value = organDetails[organKey].name;
                organFunctionDisplay.value = organDetails[organKey].function;
            }
        }

        function pickRandomOrganForTest() {
            // Filter out already correctly guessed organs if implementing a streak/completion game
            // For now, just pick any random organ
            const randomIndex = Math.floor(Math.random() * organKeys.length);
            currentTestOrganKey = organKeys[randomIndex];
            testPromptDisplay.textContent = `Find the: ${organDetails[currentTestOrganKey].name}`;
            testFeedbackDisplay.textContent = "";
            testFeedbackDisplay.className = '';
        }
        
        function handleOrganClick(event) {
            const clickedElement = event.target.closest('.organ');
            if (!clickedElement) return;

            const clickedOrganSvgId = clickedElement.id;
            const organKey = getOrganKeyFromSvgId(clickedOrganSvgId);

            if (!organKey) return;

            if (isTestMode) {
                clearOrganHighlights(); // Clear previous test highlights
                let feedbackClass = 'incorrect';
                if (organKey === currentTestOrganKey) {
                    testFeedbackDisplay.textContent = "Correct!";
                    testFeedbackDisplay.className = 'correct';
                    feedbackClass = 'highlight-test-correct';
                    // Highlight the correct organ parts
                    if (organDetails[organKey].svgId) {
                        document.getElementById(organDetails[organKey].svgId)?.classList.add(feedbackClass);
                    } else if (organDetails[organKey].svgIds) {
                        organDetails[organKey].svgIds.forEach(id => document.getElementById(id)?.classList.add(feedbackClass));
                    }
                    setTimeout(() => { // Wait a bit before picking next
                         pickRandomOrganForTest();
                    }, 1500);
                } else {
                    testFeedbackDisplay.textContent = `Incorrect. You clicked the ${organDetails[organKey].name}. Try again!`;
                    testFeedbackDisplay.className = 'incorrect';
                    // Highlight the incorrectly clicked organ parts
                    if (organDetails[organKey].svgId) {
                        document.getElementById(organDetails[organKey].svgId)?.classList.add('highlight-test-incorrect');
                    } else if (organDetails[organKey].svgIds) {
                        organDetails[organKey].svgIds.forEach(id => document.getElementById(id)?.classList.add('highlight-test-incorrect'));
                    }
                }
            } else {
                updateDisplays(organKey);
                highlightOrgan(clickedElement);
            }
        }

        svgOrganElements.forEach(organEl => {
            organEl.addEventListener('click', handleOrganClick);
        });

        testModeButton.addEventListener('click', () => {
            isTestMode = !isTestMode;
            resetDisplays();
            if (isTestMode) {
                testModeButton.textContent = "Exit Test Mode";
                testModeButton.classList.add('active');
                organNameDisplay.value = "Test Mode Active";
                organFunctionDisplay.value = "Click the organ prompted above.";
                pickRandomOrganForTest();
            } else {
                testModeButton.textContent = "Test Your Knowledge";
                testModeButton.classList.remove('active');
                // Displays reset by resetDisplays()
            }
        });

        resetViewButton.addEventListener('click', () => {
            if (isTestMode) {
                // In test mode, reset could mean pick a new organ or just clear feedback
                pickRandomOrganForTest(); // Let's make it pick a new organ
            } else {
                resetDisplays();
            }
        });

        // Initial state
        resetDisplays();

    </script>
</body>
</html>
