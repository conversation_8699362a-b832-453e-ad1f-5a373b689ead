
import React, { useState, useEffect } from 'react';
import Sidebar from './components/layout/Sidebar';
import MainContent from './components/layout/MainContent';
import HeroSection from './components/modules/HeroSection';
import CellularFoundationModule from './components/modules/CellularFoundation/CellularFoundationModule';
import TissuesModule from './components/modules/Tissues/TissuesModule';
import OrgansModule from './components/modules/Organs/OrgansModule';
import BodySystemsModule from './components/modules/BodySystems/BodySystemsModule';
import SystemIntegrationModule from './components/modules/SystemIntegration/SystemIntegrationModule';
import { MODULES } from './constants';
import type { Module } from './types';
import { MenuIcon, CloseIcon } from './components/icons/MenuIcon'; // Assuming CloseIcon is also in MenuIcon or similar file

const App: React.FC = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [activeModule, setActiveModule] = useState<string>(MODULES[0].id);

  useEffect(() => {
    const handleScroll = () => {
      let currentModule = MODULES[0].id;
      for (const module of MODULES) {
        const element = document.getElementById(module.id);
        if (element && element.getBoundingClientRect().top < window.innerHeight / 2) {
          currentModule = module.id;
        } else {
          break; 
        }
      }
      setActiveModule(currentModule);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToModule = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsSidebarOpen(false); // Close sidebar on navigation
  };

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-background to-accent/30">
      <Sidebar 
        modules={MODULES} 
        activeModule={activeModule} 
        onNavigate={scrollToModule} 
        isOpen={isSidebarOpen}
        setIsOpen={setIsSidebarOpen}
      />
      <div className="flex-1 flex flex-col transition-all duration-300">
        <button 
          className="lg:hidden p-4 text-primary fixed top-4 right-4 z-50 bg-white/70 backdrop-blur-sm rounded-full shadow-lg"
          onClick={() => setIsSidebarOpen(!isSidebarOpen)}
        >
          {isSidebarOpen ? <CloseIcon className="w-6 h-6" /> : <MenuIcon className="w-6 h-6" />}
        </button>
        
        <MainContent>
          <HeroSection id="hero" onBeginJourney={() => scrollToModule(MODULES[0].id)} />
          <CellularFoundationModule id={MODULES.find(m => m.title.includes("Cellular"))?.id || 'cellular-foundation'} />
          <TissuesModule id={MODULES.find(m => m.title.includes("Tissues"))?.id || 'tissues'} />
          <OrgansModule id={MODULES.find(m => m.title.includes("Organs"))?.id || 'organs'} />
          <BodySystemsModule id={MODULES.find(m => m.title.includes("Body Systems"))?.id || 'body-systems'} />
          <SystemIntegrationModule id={MODULES.find(m => m.title.includes("System Integration"))?.id || 'system-integration'} />
        </MainContent>
      </div>
    </div>
  );
};

export default App;
