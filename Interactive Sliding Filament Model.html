<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Sliding Filament Model</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .app-container {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 900px;
        }

        h1, h2 {
            color: #0056b3;
            text-align: center;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .visualization-container {
            width: 100%;
            max-width: 800px;
            border: 1px solid #ccc;
            background-color: #eef7ff;
            overflow: hidden; /* Ensures SVG fits nicely */
        }

        #sarcomere-svg {
            display: block;
            width: 100%;
            height: auto; /* For responsive SVG */
        }

        /* SVG Element Styles */
        .z-line { stroke: #333; stroke-width: 6; }
        .actin-filament { stroke: #007bff; stroke-width: 4; }
        .myosin-filament { stroke: #dc3545; stroke-width: 10; }
        .myosin-head { fill: #dc3545; stroke: #a02733; stroke-width: 1; transition: transform 0.5s ease-in-out; }
        .tropomyosin { stroke: #17a2b8; stroke-width: 2; opacity: 1; transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out; }
        .troponin { fill: #17a2b8; r: 4; }
        .actin-binding-site { fill: #007bff; r: 3; opacity: 0.3; /* Initially somewhat hidden by tropomyosin */}
        .calcium-ion { fill: #ffc107; r: 3; opacity: 0; transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out; }
        .atp-molecule { fill: #28a745; opacity: 0; transition: opacity 0.5s ease-in-out; font-size: 10px; }

        .info-panel {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            width: 100%;
            box-sizing: border-box;
        }
        #step-title { margin-top: 0; color: #c82333; }

        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .controls button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s;
        }
        .controls button:hover { background-color: #0056b3; }
        .controls button:disabled { background-color: #ccc; cursor: not-allowed; }

        .key-elements {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .key-elements ul { list-style: none; padding: 0; }
        .key-elements li { margin-bottom: 5px; }

        .quiz-container {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .quiz-diagram-placeholder {
            width: 100%;
            max-width: 600px; /* Smaller than main viz */
            margin: 0 auto 15px auto;
            border: 1px solid #ccc;
            background-color: #fff;
        }
        #quiz-sarcomere-svg {
            display: block;
            width: 100%;
            height: auto;
        }
        .quiz-questions div { margin-bottom: 10px; }
        .quiz-questions label { display: inline-block; width: 150px; }
        .quiz-questions input { padding: 5px; border: 1px solid #ccc; border-radius: 3px; }
        #check-answers-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s;
            display: block;
            margin: 10px auto 0 auto;
        }
        #check-answers-btn:hover { background-color: #1e7e34; }
        #quiz-feedback { margin-top: 10px; font-weight: bold; text-align: center; }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .main-content { flex-direction: column; }
            .visualization-container, .info-panel { width: 100%; }
            .controls { flex-wrap: wrap; }
            .controls button { margin-bottom: 5px; }
            .quiz-questions label { width: auto; display: block; margin-bottom: 3px;}
            .quiz-questions input { width: calc(100% - 12px); }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <h1>Interactive Sliding Filament Model</h1>

        <div class="main-content">
            <div class="visualization-container">
                <svg id="sarcomere-svg" viewBox="0 0 800 300" preserveAspectRatio="xMidYMid meet">
                    <!-- SVG elements will be populated by JavaScript -->
                </svg>
            </div>

            <div class="info-panel">
                <h2 id="step-title"></h2>
                <p id="step-description"></p>
            </div>
        </div>

        <div class="controls">
            <button id="prev-step-btn">Previous Step</button>
            <button id="next-step-btn">Next Step</button>
            <button id="reset-btn">Reset Simulation</button>
        </div>

        <div class="key-elements">
            <h2>Key Sarcomere Elements</h2>
            <ul>
                <li><strong>Z-lines:</strong> Boundaries of the sarcomere. Actin filaments are attached here.</li>
                <li><strong>Actin (Thin Filaments):</strong> Composed mainly of actin protein, also contains tropomyosin and troponin. Has binding sites for myosin heads.</li>
                <li><strong>Myosin (Thick Filaments):</strong> Composed mainly of myosin protein. Myosin heads extend from these filaments.</li>
                <li><strong>Tropomyosin:</strong> A strand-like protein that wraps around actin, covering the myosin-binding sites in a relaxed muscle.</li>
                <li><strong>Troponin:</strong> A complex of three proteins attached to tropomyosin. Calcium binding to troponin initiates the uncovering of myosin-binding sites.</li>
                <li><strong>Calcium Ions (Ca++):</strong> The "trigger" for muscle contraction. Released from the sarcoplasmic reticulum.</li>
                <li><strong>ATP (Adenosine Triphosphate):</strong> The energy currency. Required for myosin head detachment from actin and for re-energizing the myosin head.</li>
            </ul>
        </div>

        <div class="quiz-container">
            <h2>Test Your Knowledge: Label the Sarcomere</h2>
            <div class="quiz-diagram-placeholder">
                <svg id="quiz-sarcomere-svg" viewBox="0 0 600 250" preserveAspectRatio="xMidYMid meet">
                    <!-- Static sarcomere for quiz -->
                    <line class="z-line" x1="30" y1="30" x2="30" y2="220" />
                    <text x="35" y="25" font-size="12" fill="black">1</text>

                    <line class="actin-filament" x1="30" y1="80" x2="250" y2="80" />
                    <text x="130" y="75" font-size="12" fill="black">2</text>

                    <rect class="myosin-filament" x="150" y="120" width="300" height="10" fill="#dc3545" />
                    <text x="290" y="115" font-size="12" fill="black">3</text>

                    <path d="M 200 120 L 210 110 L 220 120" fill="none" stroke="#dc3545" stroke-width="3" /> <!-- Myosin Head example -->
                    <text x="225" y="115" font-size="12" fill="black">4</text>

                    <line x1="30" y1="85" x2="250" y2="85" stroke="#17a2b8" stroke-width="2"/> <!-- Tropomyosin example -->
                    <circle cx="100" cy="85" r="4" fill="#17a2b8"/> <!-- Troponin example -->
                    <text x="100" y="105" font-size="12" fill="black">5</text>

                     <line class="z-line" x1="570" y1="30" x2="570" y2="220" />
                     <line class="actin-filament" x1="570" y1="80" x2="350" y2="80" />
                     <line class="actin-filament" x1="30" y1="170" x2="250" y2="170" />
                     <line class="actin-filament" x1="570" y1="170" x2="350" y2="170" />

                     <path d="M 200 130 L 210 140 L 220 130" fill="none" stroke="#dc3545" stroke-width="3" />
                     <path d="M 300 120 L 310 110 L 320 120" fill="none" stroke="#dc3545" stroke-width="3" />
                     <path d="M 300 130 L 310 140 L 320 130" fill="none" stroke="#dc3545" stroke-width="3" />
                     <path d="M 400 120 L 410 110 L 420 120" fill="none" stroke="#dc3545" stroke-width="3" />
                     <path d="M 400 130 L 410 140 L 420 130" fill="none" stroke="#dc3545" stroke-width="3" />
                </svg>
            </div>
            <div class="quiz-questions">
                <div><label for="label-1">Label 1:</label> <input type="text" id="label-1" data-answer="Z-line"></div>
                <div><label for="label-2">Label 2:</label> <input type="text" id="label-2" data-answer="Actin filament"></div>
                <div><label for="label-3">Label 3:</label> <input type="text" id="label-3" data-answer="Myosin filament"></div>
                <div><label for="label-4">Label 4:</label> <input type="text" id="label-4" data-answer="Myosin head"></div>
                <div><label for="label-5">Label 5:</label> <input type="text" id="label-5" data-answer="Tropomyosin/Troponin"></div>
            </div>
            <button id="check-answers-btn">Check Answers</button>
            <p id="quiz-feedback"></p>
        </div>
    </div>

    <script>
        const svgNS = "http://www.w3.org/2000/svg";
        const svg = document.getElementById('sarcomere-svg');

        // DOM Elements
        const stepTitleEl = document.getElementById('step-title');
        const stepDescriptionEl = document.getElementById('step-description');
        const nextStepBtn = document.getElementById('next-step-btn');
        const prevStepBtn = document.getElementById('prev-step-btn');
        const resetBtn = document.getElementById('reset-btn');

        // Simulation parameters
        const Z_LINE_LEFT_X_INITIAL = 50;
        const Z_LINE_RIGHT_X_INITIAL = 750;
        const ACTIN_Y_TOP = 100;
        const ACTIN_Y_BOTTOM = 200;
        const MYOSIN_Y = 150;
        const MYOSIN_HEAD_SIZE = { width: 15, height: 10 };
        const NUM_MYOSIN_HEADS_PER_SIDE = 3; // Per side of myosin, interacting with one actin

        let zLineLeft, zLineRight;
        let actinTop, actinBottom;
        let tropomyosinTop, tropomyosinBottom;
        let troponinTop = [], troponinBottom = [];
        let actinBindingSitesTop = [], actinBindingSitesBottom = [];
        let myosinFilament;
        let myosinHeadsTop = [], myosinHeadsBottom = [];
        let calciumIons = [];
        let atpMoleculeTop = [], atpMoleculeBottom = [];

        let currentStep = 0;
        const totalSteps = 6; // 0: Rest, 1: Ca Release, 2: Binding Site Exp, 3: Attachment, 4: Power Stroke, 5: Detachment

        const stepData = [
            { title: "Step 0: Muscle at Rest", description: "The sarcomere is in a relaxed state. Z-lines are at their maximum distance for this cycle. Myosin-binding sites on actin are covered by tropomyosin. Myosin heads are in their high-energy (cocked) position, ready for action." },
            { title: "Step 1: Calcium Release", description: "An action potential triggers the release of Calcium ions (Ca++) from the sarcoplasmic reticulum into the sarcoplasm. Ca++ ions begin to diffuse towards the thin filaments." },
            { title: "Step 2: Binding Site Exposure", description: "Ca++ ions bind to troponin. This binding causes a conformational change in the troponin-tropomyosin complex, moving tropomyosin away from the myosin-binding sites on actin, exposing them." },
            { title: "Step 3: Myosin Head Attachment (Cross-bridge)", description: "Energized myosin heads (with ADP + Pi bound from previous ATP hydrolysis) attach to the exposed binding sites on actin, forming cross-bridges." },
            { title: "Step 4: Power Stroke", description: "The myosin head pivots, pulling the actin filament towards the center of the sarcomere (M-line). This is the power stroke. ADP and Pi are released from the myosin head. The sarcomere shortens." },
            { title: "Step 5: Detachment", description: "A new ATP molecule binds to the myosin head. This binding causes the myosin head to detach from the actin filament. The head is now ready to be re-energized for another cycle (if Ca++ is still present)." }
        ];

        function createSVGElement(tag, attributes) {
            const el = document.createElementNS(svgNS, tag);
            for (const key in attributes) {
                el.setAttribute(key, attributes[key]);
            }
            return el;
        }

        function drawSarcomere() {
            svg.innerHTML = ''; // Clear previous drawing

            // Z-Lines
            zLineLeft = createSVGElement('line', { id: 'z-line-left', class: 'z-line', x1: Z_LINE_LEFT_X_INITIAL, y1: 50, x2: Z_LINE_LEFT_X_INITIAL, y2: 250 });
            zLineRight = createSVGElement('line', { id: 'z-line-right', class: 'z-line', x1: Z_LINE_RIGHT_X_INITIAL, y1: 50, x2: Z_LINE_RIGHT_X_INITIAL, y2: 250 });
            svg.appendChild(zLineLeft);
            svg.appendChild(zLineRight);

            // Actin Filaments
            actinTop = createSVGElement('line', { id: 'actin-top', class: 'actin-filament', x1: Z_LINE_LEFT_X_INITIAL, y1: ACTIN_Y_TOP, x2: Z_LINE_LEFT_X_INITIAL + 300, y2: ACTIN_Y_TOP });
            actinBottom = createSVGElement('line', { id: 'actin-bottom', class: 'actin-filament', x1: Z_LINE_LEFT_X_INITIAL, y1: ACTIN_Y_BOTTOM, x2: Z_LINE_LEFT_X_INITIAL + 300, y2: ACTIN_Y_BOTTOM });
            // Also actin from right Z-line
            const actinTopRight = createSVGElement('line', { class: 'actin-filament', x1: Z_LINE_RIGHT_X_INITIAL, y1: ACTIN_Y_TOP, x2: Z_LINE_RIGHT_X_INITIAL - 300, y2: ACTIN_Y_TOP });
            const actinBottomRight = createSVGElement('line', { class: 'actin-filament', x1: Z_LINE_RIGHT_X_INITIAL, y1: ACTIN_Y_BOTTOM, x2: Z_LINE_RIGHT_X_INITIAL - 300, y2: ACTIN_Y_BOTTOM });
            svg.appendChild(actinTop);
            svg.appendChild(actinBottom);
            svg.appendChild(actinTopRight);
            svg.appendChild(actinBottomRight);
            
            // Myosin Filament
            myosinFilament = createSVGElement('rect', { id: 'myosin-filament', class: 'myosin-filament', x: 300, y: MYOSIN_Y - 5, width: 200, height: 10 });
            svg.appendChild(myosinFilament);

            // Tropomyosin and Troponin (simplified, on left actin for interaction)
            tropomyosinTop = createSVGElement('line', { id: 'tropomyosin-top', class: 'tropomyosin', x1: Z_LINE_LEFT_X_INITIAL, y1: ACTIN_Y_TOP + 3, x2: Z_LINE_LEFT_X_INITIAL + 300, y2: ACTIN_Y_TOP + 3 });
            tropomyosinBottom = createSVGElement('line', { id: 'tropomyosin-bottom', class: 'tropomyosin', x1: Z_LINE_LEFT_X_INITIAL, y1: ACTIN_Y_BOTTOM - 3, x2: Z_LINE_LEFT_X_INITIAL + 300, y2: ACTIN_Y_BOTTOM - 3 });
            svg.appendChild(tropomyosinTop);
            svg.appendChild(tropomyosinBottom);

            for (let i = 0; i < 5; i++) { // 5 binding sites / troponin complexes
                const siteX = Z_LINE_LEFT_X_INITIAL + 150 + i * 30;
                actinBindingSitesTop[i] = createSVGElement('circle', { class: 'actin-binding-site', cx: siteX, cy: ACTIN_Y_TOP, r:3 });
                actinBindingSitesBottom[i] = createSVGElement('circle', { class: 'actin-binding-site', cx: siteX, cy: ACTIN_Y_BOTTOM, r:3 });
                svg.appendChild(actinBindingSitesTop[i]);
                svg.appendChild(actinBindingSitesBottom[i]);

                troponinTop[i] = createSVGElement('circle', { class: 'troponin', cx: siteX, cy: ACTIN_Y_TOP + 3, r:4 });
                troponinBottom[i] = createSVGElement('circle', { class: 'troponin', cx: siteX, cy: ACTIN_Y_BOTTOM - 3, r:4 });
                svg.appendChild(troponinTop[i]);
                svg.appendChild(troponinBottom[i]);
            }

            // Myosin Heads (positioned along myosin filament, pointing to left actin)
            myosinHeadsTop = []; myosinHeadsBottom = [];
            for (let i = 0; i < NUM_MYOSIN_HEADS_PER_SIDE; i++) {
                const headX = 300 + (i * 30) + 10; // Myosin starts at 300, heads point left initially
                
                myosinHeadsTop[i] = createSVGElement('rect', {
                    class: 'myosin-head',
                    x: headX, y: MYOSIN_Y - MYOSIN_HEAD_SIZE.height - 5,
                    width: MYOSIN_HEAD_SIZE.width, height: MYOSIN_HEAD_SIZE.height,
                    transform: `rotate(-45 ${headX + MYOSIN_HEAD_SIZE.width/2} ${MYOSIN_Y - 5})` // Cocked position
                });
                myosinHeadsBottom[i] = createSVGElement('rect', {
                    class: 'myosin-head',
                    x: headX, y: MYOSIN_Y + 5,
                    width: MYOSIN_HEAD_SIZE.width, height: MYOSIN_HEAD_SIZE.height,
                    transform: `rotate(45 ${headX + MYOSIN_HEAD_SIZE.width/2} ${MYOSIN_Y + 5})` // Cocked position
                });
                svg.appendChild(myosinHeadsTop[i]);
                svg.appendChild(myosinHeadsBottom[i]);

                // ATP molecules (hidden initially)
                atpMoleculeTop[i] = createSVGElement('text', {
                    class: 'atp-molecule', x: headX, y: MYOSIN_Y - MYOSIN_HEAD_SIZE.height - 10,
                    'font-size': "10px", fill: "#28a745", opacity: 0
                });
                atpMoleculeTop[i].textContent = "ATP";
                svg.appendChild(atpMoleculeTop[i]);

                atpMoleculeBottom[i] = createSVGElement('text', {
                    class: 'atp-molecule', x: headX, y: MYOSIN_Y + MYOSIN_HEAD_SIZE.height + 15,
                    'font-size': "10px", fill: "#28a745", opacity: 0
                });
                atpMoleculeBottom[i].textContent = "ATP";
                svg.appendChild(atpMoleculeBottom[i]);
            }

            // Calcium Ions (hidden initially)
            calciumIons = [];
            for (let i = 0; i < 10; i++) {
                const caY = (i % 2 === 0) ? ACTIN_Y_TOP + 15 : ACTIN_Y_BOTTOM - 15;
                const caX = Z_LINE_LEFT_X_INITIAL + 100 + Math.random() * 150;
                const ca = createSVGElement('circle', { class: 'calcium-ion', cx: caX, cy: caY, r:3 });
                svg.appendChild(ca);
                calciumIons.push(ca);
            }
            updateVisualsForStep();
        }

        function updateVisualsForStep() {
            stepTitleEl.textContent = stepData[currentStep].title;
            stepDescriptionEl.textContent = stepData[currentStep].description;

            // Reset all dynamic visual states before applying current step's state
            // Z-Lines default positions
            zLineLeft.setAttribute('x1', Z_LINE_LEFT_X_INITIAL);
            zLineLeft.setAttribute('x2', Z_LINE_LEFT_X_INITIAL);
            zLineRight.setAttribute('x1', Z_LINE_RIGHT_X_INITIAL);
            zLineRight.setAttribute('x2', Z_LINE_RIGHT_X_INITIAL);
            
            // Actin filaments linked to Z-lines default
            const actinLength = 300;
            actinTop.setAttribute('x1', Z_LINE_LEFT_X_INITIAL);
            actinTop.setAttribute('x2', Z_LINE_LEFT_X_INITIAL + actinLength);
            actinBottom.setAttribute('x1', Z_LINE_LEFT_X_INITIAL);
            actinBottom.setAttribute('x2', Z_LINE_LEFT_X_INITIAL + actinLength);
            
            document.querySelectorAll('#sarcomere-svg .actin-filament')[2].setAttribute('x1', Z_LINE_RIGHT_X_INITIAL);
            document.querySelectorAll('#sarcomere-svg .actin-filament')[2].setAttribute('x2', Z_LINE_RIGHT_X_INITIAL - actinLength);
            document.querySelectorAll('#sarcomere-svg .actin-filament')[3].setAttribute('x1', Z_LINE_RIGHT_X_INITIAL);
            document.querySelectorAll('#sarcomere-svg .actin-filament')[3].setAttribute('x2', Z_LINE_RIGHT_X_INITIAL - actinLength);


            // Tropomyosin default (covering)
            tropomyosinTop.style.transform = 'translateY(0px)';
            tropomyosinBottom.style.transform = 'translateY(0px)';
            tropomyosinTop.style.opacity = '1';
            tropomyosinBottom.style.opacity = '1';
            troponinTop.forEach(t => t.style.transform = 'scale(1)');
            troponinBottom.forEach(t => t.style.transform = 'scale(1)');
            actinBindingSitesTop.forEach(s => s.style.opacity = '0.3');
            actinBindingSitesBottom.forEach(s => s.style.opacity = '0.3');

            // Myosin heads default (cocked, detached)
            myosinHeadsTop.forEach((head, i) => {
                const headX = 300 + (i * 30) + 10;
                head.setAttribute('transform', `rotate(-45 ${headX + MYOSIN_HEAD_SIZE.width/2} ${MYOSIN_Y - 5})`);
                atpMoleculeTop[i].style.opacity = 0;
            });
            myosinHeadsBottom.forEach((head, i) => {
                const headX = 300 + (i * 30) + 10;
                head.setAttribute('transform', `rotate(45 ${headX + MYOSIN_HEAD_SIZE.width/2} ${MYOSIN_Y + 5})`);
                atpMoleculeBottom[i].style.opacity = 0;
            });

            // Calcium ions default (hidden)
            calciumIons.forEach(ca => ca.style.opacity = '0');

            // Apply step-specific visuals
            if (currentStep >= 1) { // Calcium Release onwards
                calciumIons.forEach(ca => {
                    ca.style.opacity = '1';
                    // Animate Ca++ moving towards troponin (simplified: just appear near)
                    const targetTroponinIndex = Math.floor(Math.random() * troponinTop.length);
                    const targetY = ca.getAttribute('cy') < MYOSIN_Y ? ACTIN_Y_TOP + 3 : ACTIN_Y_BOTTOM - 3;
                    const targetX = troponinTop[targetTroponinIndex].getAttribute('cx');
                    ca.style.transform = `translate(${targetX - ca.getAttribute('cx')}px, ${targetY - ca.getAttribute('cy')}px)`;
                });
            }

            if (currentStep >= 2) { // Binding Site Exposure onwards
                tropomyosinTop.style.transform = 'translateY(-5px)'; // Move tropomyosin
                tropomyosinBottom.style.transform = 'translateY(5px)';
                // tropomyosinTop.style.opacity = '0.5'; // Make it appear shifted
                // tropomyosinBottom.style.opacity = '0.5';
                troponinTop.forEach(t => t.style.transform = 'scale(1.2)'); // Troponin changes shape
                troponinBottom.forEach(t => t.style.transform = 'scale(1.2)');
                actinBindingSitesTop.forEach(s => s.style.opacity = '1'); // Expose sites
                actinBindingSitesBottom.forEach(s => s.style.opacity = '1');
            }

            if (currentStep >= 3) { // Myosin Head Attachment onwards
                myosinHeadsTop.forEach((head, i) => {
                    const headX = 300 + (i * 30) + 10;
                    const bindingSiteX = parseFloat(actinBindingSitesTop[i+1].getAttribute('cx')); // Attach to a nearby site
                    const bindingSiteY = ACTIN_Y_TOP;
                    // Calculate rotation and translation to attach
                    // This is a simplified attachment visual; true angle depends on site
                    head.setAttribute('transform', `translate(${bindingSiteX - headX - MYOSIN_HEAD_SIZE.width/2} ${bindingSiteY - (MYOSIN_Y - MYOSIN_HEAD_SIZE.height - 5)}) rotate(0 ${headX + MYOSIN_HEAD_SIZE.width/2} ${MYOSIN_Y - 5})`);
                });
                 myosinHeadsBottom.forEach((head, i) => {
                    const headX = 300 + (i * 30) + 10;
                    const bindingSiteX = parseFloat(actinBindingSitesBottom[i+1].getAttribute('cx'));
                    const bindingSiteY = ACTIN_Y_BOTTOM;
                    head.setAttribute('transform', `translate(${bindingSiteX - headX - MYOSIN_HEAD_SIZE.width/2} ${bindingSiteY - (MYOSIN_Y + 5)}) rotate(0 ${headX + MYOSIN_HEAD_SIZE.width/2} ${MYOSIN_Y + 5})`);
                });
            }

            if (currentStep >= 4) { // Power Stroke onwards
                const contractionAmount = 40; // How much Z-lines move
                zLineLeft.setAttribute('x1', Z_LINE_LEFT_X_INITIAL + contractionAmount);
                zLineLeft.setAttribute('x2', Z_LINE_LEFT_X_INITIAL + contractionAmount);
                zLineRight.setAttribute('x1', Z_LINE_RIGHT_X_INITIAL - contractionAmount);
                zLineRight.setAttribute('x2', Z_LINE_RIGHT_X_INITIAL - contractionAmount);

                actinTop.setAttribute('x1', Z_LINE_LEFT_X_INITIAL + contractionAmount);
                actinTop.setAttribute('x2', Z_LINE_LEFT_X_INITIAL + actinLength + contractionAmount);
                actinBottom.setAttribute('x1', Z_LINE_LEFT_X_INITIAL + contractionAmount);
                actinBottom.setAttribute('x2', Z_LINE_LEFT_X_INITIAL + actinLength + contractionAmount);
                
                document.querySelectorAll('#sarcomere-svg .actin-filament')[2].setAttribute('x1', Z_LINE_RIGHT_X_INITIAL - contractionAmount);
                document.querySelectorAll('#sarcomere-svg .actin-filament')[2].setAttribute('x2', Z_LINE_RIGHT_X_INITIAL - actinLength - contractionAmount);
                document.querySelectorAll('#sarcomere-svg .actin-filament')[3].setAttribute('x1', Z_LINE_RIGHT_X_INITIAL - contractionAmount);
                document.querySelectorAll('#sarcomere-svg .actin-filament')[3].setAttribute('x2', Z_LINE_RIGHT_X_INITIAL - actinLength - contractionAmount);


                // Move tropomyosin, troponin and binding sites with actin
                tropomyosinTop.setAttribute('x1', Z_LINE_LEFT_X_INITIAL + contractionAmount);
                tropomyosinTop.setAttribute('x2', Z_LINE_LEFT_X_INITIAL + actinLength + contractionAmount);
                tropomyosinBottom.setAttribute('x1', Z_LINE_LEFT_X_INITIAL + contractionAmount);
                tropomyosinBottom.setAttribute('x2', Z_LINE_LEFT_X_INITIAL + actinLength + contractionAmount);
                for (let i = 0; i < 5; i++) {
                    const siteX = Z_LINE_LEFT_X_INITIAL + 150 + i * 30 + contractionAmount;
                    actinBindingSitesTop[i].setAttribute('cx', siteX);
                    actinBindingSitesBottom[i].setAttribute('cx', siteX);
                    troponinTop[i].setAttribute('cx', siteX);
                    troponinBottom[i].setAttribute('cx', siteX);
                }


                myosinHeadsTop.forEach((head, i) => {
                    const headX = 300 + (i * 30) + 10;
                    const bindingSiteX = parseFloat(actinBindingSitesTop[i+1].getAttribute('cx'));
                    const bindingSiteY = ACTIN_Y_TOP;
                    // Power stroke: rotate head further
                    head.setAttribute('transform', `translate(${bindingSiteX - headX - MYOSIN_HEAD_SIZE.width/2} ${bindingSiteY - (MYOSIN_Y - MYOSIN_HEAD_SIZE.height - 5)}) rotate(30 ${headX + MYOSIN_HEAD_SIZE.width/2} ${MYOSIN_Y - 5})`);
                });
                myosinHeadsBottom.forEach((head, i) => {
                    const headX = 300 + (i * 30) + 10;
                     const bindingSiteX = parseFloat(actinBindingSitesBottom[i+1].getAttribute('cx'));
                    const bindingSiteY = ACTIN_Y_BOTTOM;
                    head.setAttribute('transform', `translate(${bindingSiteX - headX - MYOSIN_HEAD_SIZE.width/2} ${bindingSiteY - (MYOSIN_Y + 5)}) rotate(-30 ${headX + MYOSIN_HEAD_SIZE.width/2} ${MYOSIN_Y + 5})`);
                });
            }

            if (currentStep >= 5) { // Detachment
                // Myosin heads detach (return to cocked position but away from actin)
                // ATP appears
                myosinHeadsTop.forEach((head, i) => {
                    const headX = 300 + (i * 30) + 10;
                    head.setAttribute('transform', `rotate(-45 ${headX + MYOSIN_HEAD_SIZE.width/2} ${MYOSIN_Y - 5}) translate(0, -5)`); // Slightly away
                    atpMoleculeTop[i].style.opacity = 1;
                });
                myosinHeadsBottom.forEach((head, i) => {
                    const headX = 300 + (i * 30) + 10;
                    head.setAttribute('transform', `rotate(45 ${headX + MYOSIN_HEAD_SIZE.width/2} ${MYOSIN_Y + 5}) translate(0, 5)`); // Slightly away
                    atpMoleculeBottom[i].style.opacity = 1;
                });
            }

            // Button states
            prevStepBtn.disabled = currentStep === 0;
            nextStepBtn.disabled = currentStep === totalSteps - 1;
        }

        nextStepBtn.addEventListener('click', () => {
            if (currentStep < totalSteps - 1) {
                currentStep++;
                updateVisualsForStep();
            }
        });

        prevStepBtn.addEventListener('click', () => {
            if (currentStep > 0) {
                currentStep--;
                updateVisualsForStep();
            }
        });

        resetBtn.addEventListener('click', () => {
            currentStep = 0;
            drawSarcomere(); // Redraws and calls updateVisualsForStep implicitly via currentStep=0
        });
        
        // Quiz Logic
        const checkAnswersBtn = document.getElementById('check-answers-btn');
        const quizFeedbackEl = document.getElementById('quiz-feedback');
        const quizInputs = [
            document.getElementById('label-1'),
            document.getElementById('label-2'),
            document.getElementById('label-3'),
            document.getElementById('label-4'),
            document.getElementById('label-5'),
        ];

        checkAnswersBtn.addEventListener('click', () => {
            let correctCount = 0;
            let allFieldsAttempted = true;
            quizInputs.forEach(input => {
                const userAnswer = input.value.trim().toLowerCase();
                const correctAnswer = input.dataset.answer.toLowerCase();
                if (userAnswer === '') {
                    allFieldsAttempted = false;
                }
                if (userAnswer === correctAnswer) {
                    correctCount++;
                    input.style.borderColor = 'green';
                } else if (userAnswer !== '') {
                    input.style.borderColor = 'red';
                } else {
                     input.style.borderColor = '#ccc'; // Reset if empty
                }
            });

            if (!allFieldsAttempted) {
                 quizFeedbackEl.textContent = "Please attempt to answer all labels.";
                 quizFeedbackEl.style.color = "orange";
                 return;
            }

            if (correctCount === quizInputs.length) {
                quizFeedbackEl.textContent = "Excellent! All answers are correct.";
                quizFeedbackEl.style.color = 'green';
            } else {
                quizFeedbackEl.textContent = `You got ${correctCount} out of ${quizInputs.length} correct. Review the key elements and try again!`;
                quizFeedbackEl.style.color = 'red';
            }
        });

        // Initial setup
        drawSarcomere();
    </script>
</body>
</html>
