<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anatomical Directional Terms Explorer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .app-title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .app-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            width: 100%;
            max-width: 1200px;
        }

        .image-section {
            flex: 1 1 400px; /* Flex basis 400px, can grow and shrink */
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        #skeleton-container {
            position: relative;
            max-width: 320px; /* Native width of the chosen image for easier percentage calculations */
            width: 100%;
            margin: 0 auto;
            border: 1px solid #ccc;
            background-color: #fff;
        }

        #skeleton-image {
            display: block;
            width: 100%;
            height: auto;
        }

        .overlay {
            position: absolute;
            display: none; /* Hidden by default */
            pointer-events: none; /* Allow clicks to pass through if needed */
            mix-blend-mode: multiply; /* Helps see overlaps better */
        }

        /* Specific overlay styles */
        #anterior-highlight { top:0; left:0; width:100%; height:100%; background: rgba(70, 130, 180, 0.35); /* SteelBlue */ }
        #posterior-highlight { top:0; left:0; width:100%; height:100%; background: rgba(60, 179, 113, 0.35); /* MediumSeaGreen */ }
        #superior-highlight { top:0; left:0; width:100%; height:40%; background: rgba(255, 215, 0, 0.35); /* Gold */ }
        #inferior-highlight { bottom:0; left:0; width:100%; height:60%; background: rgba(218, 112, 214, 0.35); /* Orchid */ }
        #medial-line { top:0; left: calc(50% - 2px); width:4px; height:100%; background: rgba(255, 0, 0, 0.7); mix-blend-mode: normal; /* Red line should be solid */ }
        
        #lateral-left-highlight { top: 18%; left: 0%; width: 30%; height: 70%; background: rgba(255, 165, 0, 0.35); /* Orange */ }
        #lateral-right-highlight { top: 18%; right: 0%; width: 30%; height: 70%; background: rgba(255, 165, 0, 0.35); /* Orange */ }
        
        /* Proximal limb parts */
        #proximal-left-arm { top: 18%; left: 20%; width: 20%; height: 22%; background: rgba(0, 0, 139, 0.4); /* DarkBlue */ }
        #proximal-right-arm { top: 18%; right: 20%; width: 20%; height: 22%; background: rgba(0, 0, 139, 0.4); }
        #proximal-left-leg { top: 48%; left: 28%; width: 18%; height: 25%; background: rgba(0, 0, 139, 0.4); }
        #proximal-right-leg { top: 48%; right: 28%; width: 18%; height: 25%; background: rgba(0, 0, 139, 0.4); }

        /* Distal limb parts */
        #distal-left-arm { top: 40%; left: 10%; width: 20%; height: 25%; background: rgba(0, 191, 255, 0.4); /* DeepSkyBlue */ }
        #distal-right-arm { top: 40%; right: 10%; width: 20%; height: 25%; background: rgba(0, 191, 255, 0.4); }
        #distal-left-leg { top: 73%; left: 25%; width: 20%; height: 27%; background: rgba(0, 191, 255, 0.4); }
        #distal-right-leg { top: 73%; right: 25%; width: 20%; height: 27%; background: rgba(0, 191, 255, 0.4); }


        .controls-section {
            flex: 1 1 300px; /* Flex basis 300px, can grow and shrink */
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .controls-section h2, .controls-section h3 {
            color: #34495e;
            margin-top: 0;
        }

        #terms-list {
            list-style-type: none;
            padding: 0;
            margin-bottom: 20px;
        }

        #terms-list li {
            margin-bottom: 10px;
        }

        #terms-list label {
            cursor: pointer;
            display: flex;
            align-items: center;
            font-size: 1.1em;
        }
        #terms-list input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        #definition-box-container {
            margin-top: 20px;
        }

        #definition-box {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            min-height: 100px;
            border: 1px solid #ced4da;
            font-size: 0.95em;
        }
        #definition-box p {
            margin: 0 0 10px 0;
        }
        #definition-box p:last-child {
            margin-bottom: 0;
        }


        #reset-button {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 1em;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-top: 20px;
        }

        #reset-button:hover {
            background-color: #c0392b;
        }

        .info-text {
            font-size: 0.9em;
            color: #555;
            margin-bottom: 15px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }
            .image-section, .controls-section {
                flex-basis: auto; /* Allow them to take full width when stacked */
            }
            #skeleton-container {
                max-width: 280px; /* Slightly smaller on mobile for better fit */
            }
        }
    </style>
</head>
<body>

    <h1 class="app-title">Interactive Anatomical Terms</h1>

    <div class="app-container">
        <div class="image-section">
            <p class="info-text">Select terms from the list to see their definitions and visual representations on the skeleton. The skeleton is shown in the <strong>anatomical position</strong> (body erect, facing forward, arms at sides, palms forward).</p>
            <div id="skeleton-container">
                <img id="skeleton-image" src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Human_skeleton_front_en.svg/320px-Human_skeleton_front_en.svg.png" alt="Skeleton in anatomical position">
                
                <!-- Visual Cues Overlays -->
                <div id="anterior-highlight" class="overlay"></div>
                <div id="posterior-highlight" class="overlay"></div>
                <div id="superior-highlight" class="overlay"></div>
                <div id="inferior-highlight" class="overlay"></div>
                <div id="medial-line" class="overlay"></div>
                <div id="lateral-left-highlight" class="overlay"></div>
                <div id="lateral-right-highlight" class="overlay"></div>
                <div id="proximal-left-arm" class="overlay"></div>
                <div id="proximal-right-arm" class="overlay"></div>
                <div id="proximal-left-leg" class="overlay"></div>
                <div id="proximal-right-leg" class="overlay"></div>
                <div id="distal-left-arm" class="overlay"></div>
                <div id="distal-right-arm" class="overlay"></div>
                <div id="distal-left-leg" class="overlay"></div>
                <div id="distal-right-leg" class="overlay"></div>
            </div>
        </div>

        <div class="controls-section">
            <h2>Directional Terms</h2>
            <ul id="terms-list">
                <!-- Terms will be populated by JavaScript -->
            </ul>

            <div id="definition-box-container">
                <h3>Definition(s):</h3>
                <div id="definition-box">Select a term to see its definition.</div>
            </div>

            <button id="reset-button">Reset All</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const termsData = [
                {
                    id: 'anterior',
                    displayName: 'Anterior / Ventral',
                    definition: '<strong>Anterior (or Ventral):</strong> Pertaining to the front of the body. In humans, this is the surface that faces forward in the anatomical position.',
                    cueIds: ['anterior-highlight']
                },
                {
                    id: 'posterior',
                    displayName: 'Posterior / Dorsal',
                    definition: '<strong>Posterior (or Dorsal):</strong> Pertaining to the back of the body. In humans, this is the surface that faces backward in the anatomical position. (Visualized here as a distinct highlight on the front-facing skeleton for illustrative purposes).',
                    cueIds: ['posterior-highlight']
                },
                {
                    id: 'superior',
                    displayName: 'Superior / Cranial',
                    definition: '<strong>Superior (or Cranial):</strong> Towards the head or upper part of a structure; above. (Cranial refers specifically to the head region).',
                    cueIds: ['superior-highlight']
                },
                {
                    id: 'inferior',
                    displayName: 'Inferior / Caudal',
                    definition: '<strong>Inferior (or Caudal):</strong> Away from the head or towards the lower part of a structure; below. (Caudal refers specifically to the tail end, which is the coccyx in humans).',
                    cueIds: ['inferior-highlight']
                },
                {
                    id: 'medial',
                    displayName: 'Medial',
                    definition: '<strong>Medial:</strong> Towards or at the midline of the body; on the inner side of.',
                    cueIds: ['medial-line']
                },
                {
                    id: 'lateral',
                    displayName: 'Lateral',
                    definition: '<strong>Lateral:</strong> Away from the midline of the body; on the outer side of.',
                    cueIds: ['lateral-left-highlight', 'lateral-right-highlight']
                },
                {
                    id: 'proximal',
                    displayName: 'Proximal',
                    definition: '<strong>Proximal:</strong> Closer to the origin of a body part or the point of attachment of a limb to the body trunk. (Primarily used for limbs).',
                    cueIds: ['proximal-left-arm', 'proximal-right-arm', 'proximal-left-leg', 'proximal-right-leg']
                },
                {
                    id: 'distal',
                    displayName: 'Distal',
                    definition: '<strong>Distal:</strong> Farther from the origin of a body part or the point of attachment of a limb to the body trunk. (Primarily used for limbs).',
                    cueIds: ['distal-left-arm', 'distal-right-arm', 'distal-left-leg', 'distal-right-leg']
                }
            ];

            const termsListElement = document.getElementById('terms-list');
            const definitionBoxElement = document.getElementById('definition-box');
            const resetButton = document.getElementById('reset-button');
            const allOverlayElements = document.querySelectorAll('.overlay');

            // Populate terms list
            termsData.forEach(term => {
                const listItem = document.createElement('li');
                const label = document.createElement('label');
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = `term-${term.id}`;
                checkbox.value = term.id;
                checkbox.dataset.termId = term.id;

                label.htmlFor = `term-${term.id}`;
                label.appendChild(checkbox);
                label.appendChild(document.createTextNode(term.displayName));
                
                listItem.appendChild(label);
                termsListElement.appendChild(listItem);
            });

            const termCheckboxes = termsListElement.querySelectorAll('input[type="checkbox"]');

            function updateDisplay() {
                let activeDefinitions = [];
                
                // Hide all overlays initially
                allOverlayElements.forEach(overlay => overlay.style.display = 'none');

                termCheckboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        const termId = checkbox.dataset.termId;
                        const term = termsData.find(t => t.id === termId);
                        if (term) {
                            activeDefinitions.push(term.definition);
                            term.cueIds.forEach(cueId => {
                                const cueElement = document.getElementById(cueId);
                                if (cueElement) {
                                    cueElement.style.display = 'block';
                                }
                            });
                        }
                    }
                });

                if (activeDefinitions.length > 0) {
                    definitionBoxElement.innerHTML = activeDefinitions.map(def => `<p>${def}</p>`).join('');
                } else {
                    definitionBoxElement.innerHTML = 'Select a term to see its definition.';
                }
            }

            termsListElement.addEventListener('change', (event) => {
                if (event.target.type === 'checkbox') {
                    updateDisplay();
                }
            });

            resetButton.addEventListener('click', () => {
                termCheckboxes.forEach(checkbox => checkbox.checked = false);
                updateDisplay();
            });

            // Initial state
            updateDisplay();
        });
    </script>

</body>
</html>
