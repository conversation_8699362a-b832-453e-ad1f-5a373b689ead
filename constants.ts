
import type { Module, GlossaryTerm, CellPart, TissueType, OrganData, BodySystemData, CaseStudyScenario, QuizQuestion } from './types';
import { CubeIcon, BeakerIcon, HeartIcon, PuzzlePieceIcon, UsersIcon, AdjustmentsHorizontalIcon } from './components/icons/ModuleIcons'; // Example icons

export const APP_TITLE = "Anatomy & Physiology for Biomedical Engineers";

export const MODULES: Module[] = [
  { id: 'cellular-foundation', title: 'The Cellular Foundation', icon: BeakerIcon },
  { id: 'tissues', title: 'Tissues – Organized Communities', icon: CubeIcon },
  { id: 'organs', title: 'Organs – Functional Units', icon: HeartIcon },
  { id: 'body-systems', title: 'Body Systems – Orchestrating Life', icon: UsersIcon },
  { id: 'system-integration', title: 'System Integration – The Symphony', icon: PuzzlePieceIcon },
  { id: 'interactive-tools', title: 'Interactive Tools', icon: AdjustmentsHorizontalIcon} // Generic for quizzes or settings
];

export const GLOSSARY_TERMS: GlossaryTerm[] = [
  { term: 'Homeostasis', definition: 'The tendency toward a relatively stable equilibrium between interdependent elements, especially as maintained by physiological processes.' },
  { term: 'Anterior (Ventral)', definition: 'Toward the front of the body.' },
  { term: 'Posterior (Dorsal)', definition: 'Toward the back of the body.' },
  { term: 'Superior (Cranial)', definition: 'Toward the head end or upper part of a structure or the body; above.' },
  { term: 'Inferior (Caudal)', definition: 'Away from the head end or toward the lower part of a structure or the body; below.' },
  { term: 'Medial', definition: 'Toward or at the midline of the body; on the inner side of.' },
  { term: 'Lateral', definition: 'Away from the midline of the body; on the outer side of.' },
  { term: 'Proximal', definition: 'Closer to the origin of the body part or the point of attachment of a limb to the body trunk.' },
  { term: 'Distal', definition: 'Farther from the origin of a body part or the point of attachment of a limb to the body trunk.' },
];

export const CELL_PARTS: CellPart[] = [
  { name: 'Nucleus', description: 'Controls cell activities and contains genetic material.', imageUrl: 'https://picsum.photos/seed/nucleus/200/200' },
  { name: 'Mitochondria', description: 'Generates most of the cell\'s supply of ATP, used as a source of chemical energy.', imageUrl: 'https://picsum.photos/seed/mitochondria/200/200' },
  { name: 'Cell Membrane', description: 'Regulates the passage of substances into and out of the cell.', imageUrl: 'https://picsum.photos/seed/cellmembrane/200/200' },
];

export const TISSUE_TYPES: TissueType[] = [
  { name: 'Epithelial Tissue', description: 'Covers body surfaces, lines body cavities, and forms glands.', structure: 'Sheets of tightly packed cells.', generalFunction: 'Protection, secretion, absorption, excretion, filtration, diffusion, and sensory reception.', examples: ['Skin surface (epidermis)', 'Lining of GI tract organs'], imageUrl: 'https://picsum.photos/seed/epithelial/300/200' },
  { name: 'Connective Tissue', description: 'Supports, connects, or separates different types of tissues and organs.', structure: 'Cells scattered within an extracellular matrix.', generalFunction: 'Binding and support, protection, insulation, transportation (blood).', examples: ['Bone', 'Tendon', 'Fat and other soft padding tissue'], imageUrl: 'https://picsum.photos/seed/connective/300/200' },
];

export const ORGANS_DATA: OrganData[] = [
    { name: 'Heart', description: 'A muscular organ that pumps blood through the circulatory system.', imageUrl: 'https://picsum.photos/seed/heartorgan/300/300', tissueLayers: [{name: 'Myocardium (Muscle Tissue)', role: 'Contracts to pump blood.'}, {name: 'Endocardium (Epithelial Tissue)', role: 'Lines chambers and valves.'}] },
    { name: 'Brain', description: 'The control center of the nervous system.', imageUrl: 'https://picsum.photos/seed/brainorgan/300/300', tissueLayers: [{name: 'Gray Matter (Nervous Tissue)', role: 'Processing information.'}, {name: 'White Matter (Nervous Tissue)', role: 'Transmitting signals.'}] },
];

export const BODY_SYSTEMS_DATA: BodySystemData[] = [
    { name: 'Cardiovascular System', mainFunction: 'Transports nutrients, oxygen, hormones to cells throughout the body and removes metabolic wastes.', primaryOrgans: ['Heart', 'Blood Vessels', 'Blood'], imageUrl: 'https://picsum.photos/seed/cardiosystem/400/300' },
    { name: 'Nervous System', mainFunction: 'Transmits nerve impulses between parts of the body.', primaryOrgans: ['Brain', 'Spinal Cord', 'Nerves'], imageUrl: 'https://picsum.photos/seed/nervoussystem/400/300' },
];

export const CASE_STUDIES_DATA: CaseStudyScenario[] = [
    { id: 'exercise', type: 'healthy', title: 'Exercise Physiology', description: 'Observe system integration during physical activity.', explanation: 'During exercise, the musculoskeletal system demands more oxygen and nutrients. The cardiovascular system responds by increasing heart rate and stroke volume. The respiratory system increases breathing rate and depth to supply more oxygen and remove CO2. The nervous system coordinates these responses.', interactiveElements: 'Simulation placeholder for heart rate, breathing rate vs. exercise intensity.' },
    { id: 'diabetes', type: 'disease', title: 'Diabetes Mellitus', description: 'Understand impacts of endocrine dysfunction.', explanation: 'Diabetes affects how the body uses glucose. Type 1 involves insulin deficiency, Type 2 involves insulin resistance. This impacts energy metabolism, potentially damaging cardiovascular, nervous, and urinary systems over time.', interactiveElements: 'Diagram placeholder for insulin signaling pathway.' },
];

export const SAMPLE_QUIZ_QUESTIONS: QuizQuestion[] = [
  { id: 'q1_cell', question: 'What is the primary function of mitochondria?', options: ['Store genetic material', 'Produce ATP', 'Synthesize proteins', 'Digest waste'], correctAnswer: 'Produce ATP', explanation: 'Mitochondria are known as the powerhouses of the cell, responsible for generating most of the cell\'s supply of adenosine triphosphate (ATP).' },
  { id: 'q2_tissue', question: 'Which tissue type is responsible for covering body surfaces?', options: ['Connective', 'Muscle', 'Nervous', 'Epithelial'], correctAnswer: 'Epithelial', explanation: 'Epithelial tissue forms linings and coverings throughout the body, including the skin and the lining of internal organs.' },
];

