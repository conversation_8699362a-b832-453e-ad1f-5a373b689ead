
import React from 'react';
import HomeostasisVisualizer from './HomeostasisVisualizer';
import InteractiveCaseStudy from './InteractiveCaseStudy';
import Card from '../../common/Card';
import Quiz from '../../common/Quiz';
import { CASE_STUDIES_DATA, SAMPLE_QUIZ_QUESTIONS } from '../../../constants';
import Accordion from '../../common/Accordion';

interface SystemIntegrationModuleProps {
  id: string;
}

const SystemIntegrationModule: React.FC<SystemIntegrationModuleProps> = ({ id }) => {
  // Placeholder for integration-specific quiz questions
  const integrationQuizQuestions = SAMPLE_QUIZ_QUESTIONS.slice(1,2); 

  const caseStudyAccordionItems = CASE_STUDIES_DATA.map(cs => ({
    title: `${cs.type === 'healthy' ? 'Healthy Scenario:' : 'Disease Scenario:'} ${cs.title}`,
    content: <InteractiveCaseStudy scenario={cs} />
  }));

  return (
    <section id={id} className="py-12 md:py-16 bg-gradient-to-b from-accent/10 to-background/10">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-primary mb-12 text-center">Module 5: System Integration – The Symphony of the Body</h2>
        
        <Card title="Concept of Homeostasis" className="mb-12">
          <HomeostasisVisualizer />
        </Card>

        <h3 className="text-3xl font-semibold text-secondary my-10 text-center">Interactive Case Studies/Scenarios</h3>
        <Accordion items={caseStudyAccordionItems} />
        
        <Card title="Interactive Problem-Solving (Placeholder)" className="my-12">
            <p className="text-lg text-textlight/90">
                This section would present a simple physiological problem (e.g., "A patient presents with chronic fatigue and excessive thirst. Which body systems might be involved? What are potential BME interventions for diagnosis or monitoring?"). Users could select affected systems or suggest interventions.
            </p>
            <div className="mt-4 p-4 bg-gray-100 rounded-lg">
                <p className="font-semibold">Example Problem:</p>
                <p>Patient X has persistent high blood pressure. Identify two primary body systems involved and one potential biomedical device used in management.</p>
                <div className="mt-2 space-x-2">
                    {/* Placeholder for interactive elements */}
                    <span className="inline-block bg-secondary/20 text-secondary px-2 py-1 text-sm rounded">Cardiovascular System</span>
                    <span className="inline-block bg-secondary/20 text-secondary px-2 py-1 text-sm rounded">Urinary System</span>
                    <span className="inline-block bg-accent/50 text-primary px-2 py-1 text-sm rounded">Blood Pressure Monitor</span>
                </div>
            </div>
        </Card>

        <Card title="Concluding Video: The Symphony of the Body (Placeholder)" className="my-8">
            <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">Placeholder for concluding video summarizing body integration and the role of BMEs.</p>
            </div>
        </Card>
        
        <Quiz questions={integrationQuizQuestions} moduleId="system-integration" />
      </div>
    </section>
  );
};

export default SystemIntegrationModule;
