<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Human Organ Explorer</title>
    <style>
        /* Basic Reset */
        body, h1, h2, p, img {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            padding: 15px;
        }

        header {
            text-align: center;
            margin-bottom: 20px;
        }

        header h1 {
            color: #1a2533;
            font-size: 2em;
        }

        .app-container {
            display: flex;
            flex-direction: row;
            background-color: #ffffff;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
            width: 100%;
            max-width: 1100px; /* Max width of the app */
        }

        .torso-container {
            flex: 3; /* Takes more space, e.g., 60% */
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f8f9fa; /* Light background for the SVG area */
            position: relative; 
            min-height: 400px; /* Ensure space for SVG */
        }

        .human-torso-svg {
            width: 100%;
            max-width: 400px; /* Max size of SVG graphic to keep it manageable */
            height: auto;
            stroke-width: 1; /* Default stroke for the SVG container itself, not shapes */
        }

        .organ-shape {
            fill: #ced4da; /* Default organ color - a neutral gray */
            stroke: #495057; /* Darker stroke for definition */
            stroke-width: 1.5px;
            cursor: pointer;
            transition: fill 0.25s ease, transform 0.2s ease, stroke-width 0.2s ease;
        }

        .organ-shape:hover {
            fill: #adb5bd; /* Slightly darker hover color */
            transform: scale(1.03); /* Slight zoom on hover */
        }

        .organ-shape.selected {
            fill: #007bff; /* Bootstrap primary blue for selected */
            stroke: #0056b3; /* Darker blue stroke for selected */
            stroke-width: 2.5px;
            transform: scale(1.05); /* Slightly more zoom for selected */
        }
        
        /* Torso outline for context */
        .torso-outline {
            fill: #e9ecef; /* Very light gray, almost like skin tone */
            stroke: #adb5bd;
            stroke-width: 1px;
        }


        .info-panel {
            flex: 2; /* Takes less space, e.g., 40% */
            padding: 25px;
            background-color: #ffffff;
            border-left: 1px solid #dee2e6;
            display: flex;
            flex-direction: column;
            min-width: 300px; /* Minimum width for the panel on desktop */
        }
        
        .info-panel-content {
            text-align: center;
        }

        .info-panel h2 {
            color: #007bff;
            margin-bottom: 15px;
            font-size: 1.75em;
            font-weight: 600;
        }

        .info-panel p {
            margin-bottom: 20px;
            font-size: 0.95em;
            text-align: left;
            color: #495057;
        }

        .info-panel img {
            max-width: 100%;
            height: auto;
            max-height: 200px; /* Max height for the image */
            object-fit: contain; /* Ensure image fits well */
            border-radius: 8px;
            margin-top: 10px;
            border: 1px solid #ced4da;
            background-color: #f8f9fa; 
        }
        
        .info-panel .placeholder-text {
            color: #6c757d; /* Bootstrap muted color */
            font-style: italic;
            margin-top: 20%; /* Push down if panel is empty */
            font-size: 1em;
        }

        /* Responsive Design */
        @media (max-width: 800px) { /* Adjusted breakpoint */
            .app-container {
                flex-direction: column;
            }

            .info-panel {
                border-left: none;
                border-top: 1px solid #dee2e6;
                min-width: auto; /* Allow it to take full width */
                flex-basis: auto; /* Reset flex-basis for column layout */
            }

            .torso-container {
                min-height: 350px; /* Adjust min height for mobile */
            }
            
            .human-torso-svg {
                max-width: 85%; 
                margin: 0 auto;
            }

            .info-panel h2 {
                font-size: 1.5em;
            }
             .info-panel p {
                font-size: 0.9em;
            }
            .info-panel img {
                max-height: 180px;
            }
        }
         @media (max-width: 480px) {
            header h1 {
                font-size: 1.6em;
            }
            .info-panel h2 {
                font-size: 1.3em;
            }
             .info-panel p {
                font-size: 0.85em;
            }
         }
    </style>
</head>
<body>

    <header>
        <h1>Human Anatomy Explorer</h1>
    </header>

    <div class="app-container">
        <div class="torso-container">
            <!-- SVG Torso and Organs -->
            <!-- ViewBox: min-x, min-y, width, height. Defines the coordinate system. -->
            <!-- Organs are simplified representations. Order of elements dictates layering (last is on top). -->
            <svg class="human-torso-svg" viewBox="0 0 300 500" preserveAspectRatio="xMidYMid meet" xmlns="http://www.w3.org/2000/svg">
                <!-- Simplified Torso Outline -->
                <path class="torso-outline" d="M90,40 Q150,20 210,40 L230,150 Q240,300 210,490 L90,490 Q60,300 70,150 Z" />

                <!-- Brain -->
                <ellipse id="organ-brain" class="organ-shape" cx="150" cy="65" rx="65" ry="40" />

                <!-- Lungs (Left and Right) -->
                <path id="organ-lungs-left" class="organ-shape" d="M138,110 C100,130 75,190 85,270 L135,270 L135,120 Z" />
                <path id="organ-lungs-right" class="organ-shape" d="M162,110 C200,130 225,190 215,270 L165,270 L165,120 Z" />
                
                <!-- Heart (Overlaps lungs slightly) -->
                <path id="organ-heart" class="organ-shape" d="M150,170 Q138,150 120,170 C100,195 130,245 150,255 C170,245 200,195 180,170 Q162,150 150,170 Z" />

                <!-- Liver (Large, upper right abdomen) -->
                <path id="organ-liver" class="organ-shape" d="M90,260 C150,250 210,265 220,290 L200,330 L100,330 Z" />
                
                <!-- Gallbladder (Small, under/behind liver) -->
                <ellipse id="organ-gallbladder" class="organ-shape" cx="175" cy="315" rx="12" ry="18" />

                <!-- Stomach (J-shape, to the left, under liver/lungs) -->
                <path id="organ-stomach" class="organ-shape" d="M95,290 C70,320 90,380 140,380 C170,380 180,350 170,320 Z" />
                
                <!-- Spleen (To the left/behind stomach) -->
                <ellipse id="organ-spleen" class="organ-shape" cx="75" cy="340" rx="22" ry="32" />

                <!-- Pancreas (Behind stomach - simplified oblong shape) -->
                <rect id="organ-pancreas" class="organ-shape" x="105" y="365" width="90" height="22" rx="7" ry="7" />

                <!-- Kidneys (Pair, towards the back) -->
                <ellipse id="organ-kidneys-left" class="organ-shape" cx="100" cy="380" rx="25" ry="40" />
                <ellipse id="organ-kidneys-right" class="organ-shape" cx="200" cy="380" rx="25" ry="40" />

                <!-- Large Intestine (Forms a border) -->
                <path id="organ-large-intestine" class="organ-shape" d="M90,390 Q70,400 80,470 L220,470 Q230,400 210,390 L200,455 L100,455 Z" />

                <!-- Small Intestine (Coiled inside large intestine) -->
                <path id="organ-small-intestine" class="organ-shape" d="M105,400 C95,430 150,450 200,435 C190,400 150,385 105,400 M115,410 Q150,430 185,415 Q150,395 115,410 M130,420 C150,440 170,425 150,410" fill-rule="evenodd"/>
                
                <!-- Bladder (Low in the abdomen) -->
                <path id="organ-bladder" class="organ-shape" d="M120,460 C125,485 175,485 180,460 L150,450 Z" />
            </svg>
        </div>
        <div class="info-panel">
            <div id="info-panel-content" class="info-panel-content">
                <h2 id="organ-name">Select an Organ</h2>
                <p id="organ-description" class="placeholder-text">Click on an organ in the diagram to learn more about its function.</p>
                <img id="organ-image" src="https://via.placeholder.com/250x200/dee2e6/6c757d?Text=Human+Anatomy" alt="Organ image placeholder">
            </div>
        </div>
    </div>

    <script>
        const organDataStore = [
            {
                id: "organ-brain",
                name: "Brain",
                description: "The brain is the command center of the nervous system. It controls thought, memory, emotion, touch, motor skills, vision, breathing, temperature, hunger, and virtually every process that regulates our body.",
                imageUrl: "https://via.placeholder.com/250x200/FFC107/000000?Text=Brain"
            },
            {
                // Data for Lungs (applies to both left and right SVG elements)
                groupId: "organ-lungs", // Shared identifier for Lungs
                ids: ["organ-lungs-left", "organ-lungs-right"],
                name: "Lungs",
                description: "The lungs are a pair of spongy, air-filled organs located on either side of the chest (thorax). Their primary function is to facilitate respiration, bringing oxygen into the bloodstream and removing carbon dioxide.",
                imageUrl: "https://via.placeholder.com/250x200/03A9F4/FFFFFF?Text=Lungs"
            },
            {
                id: "organ-heart",
                name: "Heart",
                description: "The heart is a muscular organ that pumps blood throughout the body via the circulatory system. This vital process supplies oxygen and nutrients to tissues while removing carbon dioxide and other waste products.",
                imageUrl: "https://via.placeholder.com/250x200/E91E63/FFFFFF?Text=Heart"
            },
            {
                id: "organ-liver",
                name: "Liver",
                description: "The liver is a large, vital organ with numerous functions, including detoxifying harmful substances, producing bile to aid in fat digestion, and synthesizing proteins essential for blood clotting.",
                imageUrl: "https://via.placeholder.com/250x200/795548/FFFFFF?Text=Liver"
            },
            {
                id: "organ-gallbladder",
                name: "Gallbladder",
                description: "The gallbladder is a small organ situated beneath the liver. Its main role is to store and concentrate bile produced by the liver, releasing it into the small intestine to help digest fats.",
                imageUrl: "https://via.placeholder.com/250x200/4CAF50/FFFFFF?Text=Gallbladder"
            },
            {
                id: "organ-stomach",
                name: "Stomach",
                description: "The stomach is a muscular, J-shaped organ that plays a key role in digestion. It secretes acid and enzymes that break down food, preparing it for absorption in the small intestine.",
                imageUrl: "https://via.placeholder.com/250x200/FF9800/000000?Text=Stomach"
            },
             {
                id: "organ-spleen",
                name: "Spleen",
                description: "The spleen, located in the upper left abdomen, primarily acts as a blood filter. It removes old or damaged red blood cells, recycles iron, and plays a role in the immune system.",
                imageUrl: "https://via.placeholder.com/250x200/673AB7/FFFFFF?Text=Spleen"
            },
            {
                id: "organ-pancreas",
                name: "Pancreas",
                description: "The pancreas has both exocrine functions, producing enzymes crucial for digestion, and endocrine functions, secreting hormones like insulin and glucagon to regulate blood sugar levels.",
                imageUrl: "https://via.placeholder.com/250x200/FFEB3B/000000?Text=Pancreas"
            },
            {
                // Data for Kidneys (applies to both left and right SVG elements)
                groupId: "organ-kidneys", // Shared identifier for Kidneys
                ids: ["organ-kidneys-left", "organ-kidneys-right"],
                name: "Kidneys",
                description: "The kidneys are a pair of bean-shaped organs that filter waste products from the blood to produce urine. They are crucial for maintaining overall fluid balance, regulating electrolytes, and blood pressure.",
                imageUrl: "https://via.placeholder.com/250x200/9C27B0/FFFFFF?Text=Kidneys"
            },
            {
                id: "organ-small-intestine",
                name: "Small Intestine",
                description: "The small intestine is a long, coiled tube where the majority of digestion and nutrient absorption occurs. It receives chyme from the stomach and further breaks it down with enzymes and bile.",
                imageUrl: "https://via.placeholder.com/250x200/CDDC39/000000?Text=Small+Intestine"
            },
            {
                id: "organ-large-intestine",
                name: "Large Intestine",
                description: "The large intestine (colon) is primarily responsible for absorbing water from indigestible food residue and transmitting the waste material from the body. It also houses beneficial gut bacteria.",
                imageUrl: "https://via.placeholder.com/250x200/607D8B/FFFFFF?Text=Large+Intestine"
            },
            {
                id: "organ-bladder",
                name: "Bladder",
                description: "The bladder is a hollow, muscular, and distensible organ that stores urine produced by the kidneys. When it fills, stretch receptors signal the brain, creating the urge to urinate.",
                imageUrl: "https://via.placeholder.com/250x200/FF5722/FFFFFF?Text=Bladder"
            }
        ];

        const organNameEl = document.getElementById('organ-name');
        const organDescriptionEl = document.getElementById('organ-description');
        const organImageEl = document.getElementById('organ-image');
        
        const svgOrganShapes = document.querySelectorAll('.organ-shape');
        let currentSelectedElements = [];

        function displayOrganInfo(data) {
            organNameEl.textContent = data.name;
            organDescriptionEl.textContent = data.description;
            organDescriptionEl.classList.remove('placeholder-text');
            organImageEl.src = data.imageUrl;
            organImageEl.alt = `Illustration of ${data.name}`;
        }

        function resetPanel() {
            organNameEl.textContent = "Select an Organ";
            organDescriptionEl.textContent = "Click on an organ in the diagram to learn more about its function.";
            organDescriptionEl.classList.add('placeholder-text');
            organImageEl.src = "https://via.placeholder.com/250x200/dee2e6/6c757d?Text=Human+Anatomy";
            organImageEl.alt = "Placeholder for organ image";
        }
        
        resetPanel(); // Initialize panel on load

        svgOrganShapes.forEach(shape => {
            shape.addEventListener('click', function() {
                const clickedId = this.id;
                let organToDisplay = null;
                let elementsToHighlight = [this];

                // Check if clicked organ is part of a group (like Lungs or Kidneys)
                const groupData = organDataStore.find(data => data.groupId && data.ids.includes(clickedId));
                
                if (groupData) {
                    organToDisplay = groupData;
                    elementsToHighlight = [];
                    groupData.ids.forEach(idInGroup => {
                        const el = document.getElementById(idInGroup);
                        if (el) elementsToHighlight.push(el);
                    });
                } else {
                    // Single organ
                    organToDisplay = organDataStore.find(data => data.id === clickedId);
                }

                if (!organToDisplay) return;

                // Deselect previously selected elements
                currentSelectedElements.forEach(el => el.classList.remove('selected'));
                currentSelectedElements = [];

                // Select new element(s)
                elementsToHighlight.forEach(el => {
                    el.classList.add('selected');
                    currentSelectedElements.push(el);
                });
                
                displayOrganInfo(organToDisplay);
            });
        });

    </script>

</body>
</html>
