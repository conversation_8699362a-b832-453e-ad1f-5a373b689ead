
import React, { useState } from 'react';
import type { QuizQuestion } from '../../types';
import Button from './Button';

interface QuizProps {
  questions: QuizQuestion[];
  moduleId: string; // To differentiate quizzes if needed
}

const Quiz: React.FC<QuizProps> = ({ questions }) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [showFeedback, setShowFeedback] = useState(false);
  const [score, setScore] = useState(0);
  const [quizCompleted, setQuizCompleted] = useState(false);

  const currentQuestion = questions[currentQuestionIndex];

  const handleAnswerSelection = (answer: string) => {
    if (showFeedback) return; // Prevent changing answer after submission
    setSelectedAnswer(answer);
  };

  const handleSubmitAnswer = () => {
    if (!selectedAnswer) return;
    setShowFeedback(true);
    if (selectedAnswer === currentQuestion.correctAnswer) {
      setScore(score + 1);
    }
  };

  const handleNextQuestion = () => {
    setSelectedAnswer(null);
    setShowFeedback(false);
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      setQuizCompleted(true);
    }
  };

  const handleRestartQuiz = () => {
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setShowFeedback(false);
    setScore(0);
    setQuizCompleted(false);
  };

  if (quizCompleted) {
    return (
      <div className="p-6 bg-white shadow-lg rounded-lg my-8 text-center">
        <h3 className="text-2xl font-semibold text-primary mb-4">Quiz Completed!</h3>
        <p className="text-lg mb-4">Your score: {score} out of {questions.length}</p>
        <Button onClick={handleRestartQuiz} variant="secondary">Restart Quiz</Button>
      </div>
    );
  }

  if (!currentQuestion) return <p>Loading quiz...</p>;

  return (
    <div className="p-6 bg-white/80 backdrop-blur-md shadow-xl rounded-xl my-8">
      <h3 className="text-xl font-semibold text-primary mb-1">Knowledge Check</h3>
      <p className="text-lg text-textlight mb-4">{currentQuestion.question}</p>
      <div className="space-y-3 mb-6">
        {currentQuestion.options.map((option, index) => (
          <button
            key={index}
            onClick={() => handleAnswerSelection(option)}
            disabled={showFeedback}
            className={`w-full text-left p-3 rounded-lg border-2 transition-all duration-200
              ${selectedAnswer === option ? 'bg-secondary/30 border-secondary' : 'bg-gray-50 hover:bg-gray-100 border-gray-300'}
              ${showFeedback && option === currentQuestion.correctAnswer ? '!bg-green-200 !border-green-500' : ''}
              ${showFeedback && selectedAnswer === option && option !== currentQuestion.correctAnswer ? '!bg-red-200 !border-red-500' : ''}
              ${showFeedback ? 'cursor-not-allowed' : 'cursor-pointer'}
            `}
          >
            {option}
          </button>
        ))}
      </div>
      {showFeedback && (
        <div className={`p-3 rounded-lg mb-4 ${selectedAnswer === currentQuestion.correctAnswer ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
          {selectedAnswer === currentQuestion.correctAnswer ? 'Correct!' : 'Incorrect.'}
          {currentQuestion.explanation && <p className="text-sm mt-1">{currentQuestion.explanation}</p>}
        </div>
      )}
      {!showFeedback && <Button onClick={handleSubmitAnswer} disabled={!selectedAnswer}>Submit Answer</Button>}
      {showFeedback && <Button onClick={handleNextQuestion} variant="secondary">
        {currentQuestionIndex < questions.length - 1 ? 'Next Question' : 'Finish Quiz'}
      </Button>}
    </div>
  );
};

export default Quiz;
