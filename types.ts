
export interface Module {
  id: string;
  title: string;
  icon?: React.ElementType; // For potential icons in sidebar
}

export interface GlossaryTerm {
  term: string;
  definition: string;
  category?: string;
}

export interface CellPart {
  name: string;
  description: string;
  imageUrl?: string; // Placeholder for image
  videoUrl?: string; // Placeholder for micro-video
}

export interface TissueType {
  name: string;
  description: string;
  structure: string;
  generalFunction: string;
  examples: string[];
  imageUrl: string; // Microscopic image
}

export interface OrganData {
  name: string;
  description: string;
  imageUrl: string; // Image of the organ
  tissueLayers: { name: string; role: string }[];
}

export interface BodySystemData {
  name: string;
  mainFunction: string;
  primaryOrgans: string[];
  imageUrl: string; // Image representing the system
  processVideoUrl?: string; // Placeholder for animated flowchart/video
}

export interface CaseStudyScenario {
  id: string;
  title: string;
  description: string;
  type: 'healthy' | 'disease';
  interactiveElements?: React.ReactNode; // For complex simulations
  explanation: string; // For simpler text-based explanations or diagrams
}

export interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: string; // Could be index or value
  explanation?: string;
}

export enum MembraneTransportType {
  DIFFUSION = 'Diffusion',
  FACILITATED_DIFFUSION = 'Facilitated Diffusion',
  ACTIVE_TRANSPORT = 'Active Transport',
  OSMOSIS = 'Osmosis',
  ENDOCYTOSIS = 'Endocytosis',
  EXOCYTOSIS = 'Exocytosis',
}

export enum ActionPotentialPhase {
  RESTING = 'Resting Potential',
  DEPOLARIZATION = 'Depolarization',
  REPOLARIZATION = 'Repolarization',
  HYPERPOLARIZATION = 'Hyperpolarization',
  PROPAGATION = 'Propagation'
}

export interface Hotspot {
  id: string;
  x: number; // Percentage
  y: number; // Percentage
  title: string;
  description: string;
}
