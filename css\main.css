/* ===== CSS VARIABLES ===== */
:root {
    /* Modern Color Palette */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #06b6d4;
    --secondary-dark: #0891b2;
    --secondary-light: #22d3ee;
    --accent-color: #8b5cf6;
    --accent-dark: #7c3aed;
    --accent-light: #a78bfa;
    --background-color: #f8fafc;
    --background-dark: #0f172a;
    --surface-color: #ffffff;
    --surface-dark: #1e293b;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --text-light: #94a3b8;
    --text-white: #ffffff;
    --border-color: #e2e8f0;
    --border-dark: #334155;

    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #06b6d4;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
    --gradient-surface: linear-gradient(135deg, var(--surface-color) 0%, #f1f5f9 100%);
    --gradient-dark: linear-gradient(135deg, var(--background-dark) 0%, var(--surface-dark) 100%);
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--gradient-surface);
    overflow-x: hidden;
    min-height: 100vh;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.section-title {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
    border-radius: 2px;
}

.section-subtitle {
    text-align: center;
    color: var(--medium-gray);
    margin-bottom: 3rem;
    font-size: 1.125rem;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    text-align: center;
    transition: var(--transition-normal);
    cursor: pointer;
    border: none;
    text-decoration: none;
    font-size: 1rem;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-white);
    box-shadow: var(--shadow-md);
    border: none;
}

.btn-primary:hover {
    background: var(--gradient-accent);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--text-white);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--text-white);
    transform: translateY(-2px);
}

/* ===== HEADER & NAVIGATION ===== */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    z-index: 1000;
    box-shadow: var(--shadow-md);
    border-bottom: 1px solid var(--border-color);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-logo i {
    font-size: 1.5rem;
    color: var(--accent-color);
    animation: pulse 2s infinite;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    background: var(--gradient-primary);
    color: var(--text-white);
    box-shadow: var(--shadow-sm);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    transition: var(--transition-fast);
}

/* ===== NAVIGATION ACTIONS ===== */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-sidebar-toggle {
    background: var(--gradient-primary);
    color: var(--text-white);
    border: none;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.nav-sidebar-toggle:hover {
    background: var(--gradient-accent);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* ===== NAVIGATION SIDEBAR ===== */
.nav-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: var(--surface-color);
    box-shadow: var(--shadow-lg);
    z-index: 1001;
    transition: right var(--transition-normal);
    overflow-y: auto;
}

.nav-sidebar.active {
    right: 0;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    background: var(--gradient-primary);
    color: var(--text-white);
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.25rem;
}

.sidebar-close {
    background: transparent;
    color: var(--text-white);
    border: none;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.sidebar-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-content {
    padding: 2rem;
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-section h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.sidebar-links {
    list-style: none;
    padding: 0;
}

.sidebar-links li {
    margin-bottom: 0.5rem;
}

.sidebar-links a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 8px;
    transition: var(--transition-fast);
    font-weight: 500;
}

.sidebar-links a:hover {
    background: var(--gradient-primary);
    color: var(--text-white);
    transform: translateX(5px);
}

.sidebar-links i {
    width: 20px;
    text-align: center;
    color: var(--accent-color);
}

.sidebar-links a:hover i {
    color: var(--text-white);
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* ===== HERO SECTION ===== */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    position: relative;
    padding-top: 80px;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
    opacity: 0.3;
    animation: float 20s ease-in-out infinite;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.hero-text {
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--text-white);
    margin-bottom: 0.5rem;
    line-height: 1.1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1.5rem;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.hero-description {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    line-height: 1.7;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

.animated-heart {
    font-size: 8rem;
    color: var(--accent-light);
    animation: heartbeat 2s infinite;
    filter: drop-shadow(0 0 20px rgba(139, 92, 246, 0.5));
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.element {
    position: absolute;
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.7);
    animation: float 6s ease-in-out infinite;
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.element-1 { top: 10%; left: 10%; animation-delay: 0s; color: var(--accent-light); }
.element-2 { top: 20%; right: 10%; animation-delay: 1.5s; color: var(--secondary-light); }
.element-3 { bottom: 20%; left: 15%; animation-delay: 3s; color: var(--accent-light); }
.element-4 { bottom: 10%; right: 20%; animation-delay: 4.5s; color: var(--secondary-light); }

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    font-size: 1.5rem;
    color: var(--primary-color);
}

/* ===== MODULES SECTION ===== */
.modules-section {
    padding: 5rem 0;
    background: var(--white);
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.module-card {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border: 1px solid rgba(0, 95, 115, 0.1);
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.module-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.module-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.module-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.module-card p {
    margin-bottom: 1.5rem;
}

.module-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.module-link {
    padding: 0.5rem 1rem;
    background: var(--light-gray);
    border-radius: 6px;
    color: var(--primary-color);
    font-size: 0.9rem;
    transition: var(--transition-fast);
}

.module-link:hover {
    background: var(--accent-color);
    color: var(--primary-color);
}

.module-progress {
    margin-top: auto;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--light-gray);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
    border-radius: 4px;
    transition: width var(--transition-normal);
}

.progress-0 { width: 0%; }

.progress-text {
    font-size: 0.875rem;
    color: var(--medium-gray);
}

.module-filter {
    text-align: center;
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    background: var(--light-gray);
    color: var(--medium-gray);
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.9rem;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: var(--white);
}

/* ===== SIMULATIONS SECTION ===== */
.simulations-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, var(--light-gray) 0%, var(--white) 100%);
}

.simulations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.simulation-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.simulation-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.simulation-card.featured {
    grid-column: span 2;
}

.simulation-preview {
    position: relative;
    height: 200px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.simulation-icon {
    font-size: 4rem;
    color: var(--white);
    opacity: 0.8;
}

.play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
}

.play-overlay:hover {
    background: var(--white);
    transform: translate(-50%, -50%) scale(1.1);
}

.play-overlay i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-left: 3px;
}

.simulation-content {
    padding: 1.5rem;
}

.simulation-content h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.simulation-content p {
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.simulation-tags {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.tag {
    padding: 0.25rem 0.75rem;
    background: var(--accent-color);
    color: var(--primary-color);
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.simulation-link {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 6px;
    font-weight: 500;
    transition: var(--transition-fast);
}

.simulation-link:hover {
    background: var(--secondary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* ===== VIDEOS SECTION ===== */
.videos-section {
    padding: 5rem 0;
    background: var(--white);
}

.videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.video-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.video-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-normal);
}

.video-card:hover .video-thumbnail img {
    transform: scale(1.05);
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
}

.play-button:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translate(-50%, -50%) scale(1.1);
}

.play-button i {
    font-size: 1.5rem;
    color: var(--white);
    margin-left: 3px;
}

.video-duration {
    position: absolute;
    bottom: 0.5rem;
    right: 0.5rem;
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
}

.video-info {
    padding: 1.5rem;
}

.video-info h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.video-info p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.video-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.video-category {
    background: var(--accent-color);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}

.video-views {
    color: var(--medium-gray);
}

/* ===== ABOUT SECTION ===== */
.about-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, var(--light-gray) 0%, var(--white) 100%);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    text-align: left;
    margin-bottom: 2rem;
}

.about-text p {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
}

.feature-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--white);
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.feature-item i {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.feature-item h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.125rem;
}

.feature-item p {
    font-size: 0.9rem;
    margin-bottom: 0;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    background: var(--white);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--medium-gray);
    font-size: 0.9rem;
    font-weight: 500;
}

/* ===== FOOTER ===== */
.main-footer {
    background: var(--dark-gray);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: var(--white);
    margin-bottom: 1rem;
    font-size: 1.125rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer-logo i {
    font-size: 1.5rem;
    color: var(--text-highlight);
}

.footer-section p {
    color: #adb5bd;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #adb5bd;
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--white);
}

.footer-section ul li i {
    margin-right: 0.5rem;
    color: var(--text-highlight);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    transition: var(--transition-fast);
}

.social-links a:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #495057;
    padding-top: 1rem;
    text-align: center;
}

.footer-bottom p {
    color: #adb5bd;
    margin: 0;
    font-size: 0.9rem;
}

/* ===== AUTHOR INFORMATION ===== */
.author-info {
    margin: 1.5rem 0;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    border-left: 4px solid var(--accent-color);
}

.author-info h4 {
    color: var(--text-white);
    margin-bottom: 1rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.author-info h4 i {
    color: var(--accent-color);
}

.author-info p {
    color: #e2e8f0;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.author-info strong {
    color: var(--text-white);
    font-weight: 600;
}

.contact-phones {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 0.5rem;
}

.contact-phones p {
    margin-bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.copyright-info {
    text-align: center;
}

.copyright-info p {
    margin-bottom: 0.5rem;
}

.copyright-info p:last-child {
    margin-bottom: 0;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }

    .hero-subtitle {
        font-size: 1.75rem;
    }

    .simulation-card.featured {
        grid-column: span 1;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: rgba(255, 255, 255, 0.95);
        -webkit-backdrop-filter: blur(10px);
        backdrop-filter: blur(10px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: var(--transition-normal);
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.5rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .modules-grid {
        grid-template-columns: 1fr;
    }

    .simulations-grid {
        grid-template-columns: 1fr;
    }

    .videos-grid {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 0.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.25rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
    }

    .module-card {
        padding: 1.5rem;
    }

    .simulation-content {
        padding: 1rem;
    }

    .video-info {
        padding: 1rem;
    }

    .feature-item {
        padding: 1rem;
    }

    .stat-item {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }
}
