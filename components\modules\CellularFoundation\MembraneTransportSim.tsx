
import React, { useState, useEffect, useCallback } from 'react';
import Button from '../../common/Button';
import { MembraneTransportType } from '../../../types';

const MembraneTransportSim: React.FC = () => {
  const [transportType, setTransportType] = useState<MembraneTransportType>(MembraneTransportType.DIFFUSION);
  const [concentrationGradient, setConcentrationGradient] = useState(50); // 0-100
  const [atpAvailable, setAtpAvailable] = useState(true);
  const [particles, setParticles] = useState<{ id: number; inside: boolean; size: number }[]>([]);

  const initializeParticles = useCallback(() => {
    const newParticles = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      // Initial distribution based on gradient for diffusion/osmosis-like scenarios
      inside: Math.random() * 100 < (100 - concentrationGradient), 
      size: Math.random() * 5 + 2, // Random size for visual
    }));
    setParticles(newParticles);
  }, [concentrationGradient]);

  useEffect(() => {
    initializeParticles();
  }, [initializeParticles, transportType]); // Re-init if type changes

  const simulateStep = () => {
    setParticles(prevParticles => 
      prevParticles.map(p => {
        let shouldMove = false;
        const randomFactor = Math.random();

        switch (transportType) {
          case MembraneTransportType.DIFFUSION:
            // Higher chance to move from high to low
            if (p.inside && randomFactor < concentrationGradient / 100) shouldMove = true; // Move out
            if (!p.inside && randomFactor < (100 - concentrationGradient) / 100) shouldMove = true; // Move in
            break;
          case MembraneTransportType.FACILITATED_DIFFUSION:
            // Similar to diffusion but implies channels/carriers
            if (p.inside && randomFactor < concentrationGradient / 150) shouldMove = true; 
            if (!p.inside && randomFactor < (100 - concentrationGradient) / 150) shouldMove = true;
            break;
          case MembraneTransportType.ACTIVE_TRANSPORT:
            if (atpAvailable) {
              // Can move against gradient if ATP is present
              if (p.inside && randomFactor < (100 - concentrationGradient) / 100 * 0.5) shouldMove = true; // Move out (harder if low outside)
              if (!p.inside && randomFactor < concentrationGradient / 100 * 0.5) shouldMove = true; // Move in (harder if high inside)
            }
            break;
          case MembraneTransportType.OSMOSIS: // Simplified: water moves towards higher solute conc. (represented by gradient)
             // If 'gradient' means solute outside is higher, water moves out.
             // This is a very abstract representation.
            if (p.inside && randomFactor < concentrationGradient / 200) shouldMove = true; // Water moves out
            if (!p.inside && randomFactor < (100-concentrationGradient) / 200) shouldMove = true; // Water moves in
            break;
          // Endocytosis/Exocytosis are more complex bulk transport, harder to show simply here
          default:
            break;
        }
        return shouldMove ? { ...p, inside: !p.inside } : p;
      })
    );
  };
  
  // Auto-run simulation step for some visual effect
  useEffect(() => {
    if (transportType !== MembraneTransportType.ENDOCYTOSIS && transportType !== MembraneTransportType.EXOCYTOSIS) {
      const intervalId = setInterval(simulateStep, 500);
      return () => clearInterval(intervalId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [transportType, atpAvailable, particles]); // particles in dep array for re-evaluating simulation

  return (
    <div className="space-y-4 p-4 bg-gray-50 rounded-lg shadow-inner">
      <div className="flex flex-wrap gap-2 mb-4">
        {Object.values(MembraneTransportType).map(type => (
          <Button 
            key={type} 
            onClick={() => setTransportType(type)}
            variant={transportType === type ? 'primary' : 'outline'}
            size="sm"
          >
            {type}
          </Button>
        ))}
      </div>

      <p className="text-sm text-textlight/80">Current Type: <span className="font-semibold text-secondary">{transportType}</span></p>

      <div className="my-4">
        <label htmlFor="concentrationGradient" className="block text-sm font-medium text-gray-700">
          Concentration Gradient (Outside High): {concentrationGradient}%
        </label>
        <input
          type="range"
          id="concentrationGradient"
          min="0"
          max="100"
          value={concentrationGradient}
          onChange={(e) => setConcentrationGradient(parseInt(e.target.value))}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-secondary"
        />
      </div>

      {transportType === MembraneTransportType.ACTIVE_TRANSPORT && (
        <div className="my-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={atpAvailable}
              onChange={(e) => setAtpAvailable(e.target.checked)}
              className="rounded text-secondary focus:ring-secondary"
            />
            <span>ATP Available</span>
          </label>
        </div>
      )}

      <div className="w-full h-48 bg-background border-2 border-secondary rounded-lg flex relative overflow-hidden">
        {/* Membrane */}
        <div className="absolute top-0 bottom-0 left-1/2 w-1 bg-primary/50 transform -translate-x-1/2"></div>
        {/* Particles */}
        {particles.map(p => (
          <div
            key={p.id}
            className="absolute rounded-full bg-texthighlight transition-all duration-500 ease-linear"
            style={{
              width: `${p.size}px`,
              height: `${p.size}px`,
              top: `${Math.random() * 90}%`, // Random Y for visual spread
              left: p.inside ? `${Math.random() * 40 + 5}%` : `${Math.random() * 40 + 55}%`, // Spread within compartment
              opacity: (transportType === MembraneTransportType.ACTIVE_TRANSPORT && !atpAvailable && (p.inside ? concentrationGradient < 50 : concentrationGradient > 50)) ? 0.5 : 1, // Dim if active transport stuck
            }}
          ></div>
        ))}
        <div className="absolute top-2 left-4 text-sm text-primary font-semibold">Inside Cell</div>
        <div className="absolute top-2 right-4 text-sm text-primary font-semibold">Outside Cell</div>
      </div>
      <p className="text-xs text-textlight/60 mt-2">
        This is a simplified visual representation. Actual mechanisms are more complex.
        Endocytosis/Exocytosis are complex and not fully simulated here.
      </p>
    </div>
  );
};

export default MembraneTransportSim;

