<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Study Anatomy and Physiology</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body class="bg-gray-50 text-gray-800 font-sans">
  <header class="bg-blue-900 text-white p-6 shadow-md">
    <h1 class="text-3xl font-bold text-center">Study Anatomy and Physiology for Biomedical Engineering</h1>
    <p class="text-center mt-2">An Interactive Journey from Cells to Systems</p>
  </header>

  <main class="p-6 max-w-6xl mx-auto">
    <!-- Navigation Tabs -->
    <nav class="flex flex-wrap gap-2 mb-6">
      <button class="tab-btn" @click="currentTab='intro'">Terminology</button>
      <button class="tab-btn" @click="currentTab='cells'">Cells</button>
      <button class="tab-btn" @click="currentTab='membrane'">Cell Membrane & Potentials</button>
      <button class="tab-btn" @click="currentTab='tissues'">Tissues</button>
      <button class="tab-btn" @click="currentTab='organs'">Organs</button>
      <button class="tab-btn" @click="currentTab='systems'">Body Systems</button>
      <button class="tab-btn" @click="currentTab='integration'">System Integration</button>
    </nav>

    <div x-data="{ currentTab: 'intro' }">
      <!-- Intro Terminology -->
      <section x-show="currentTab==='intro'">
        <h2 class="text-2xl font-semibold mb-2">Basic Terminology</h2>
        <p>Explore fundamental terms used in Anatomy and Physiology through tooltips and animated definitions.</p>
      </section>

      <!-- Cells -->
      <section x-show="currentTab==='cells'">
        <h2 class="text-2xl font-semibold mb-2">Cell Descriptions & Organelles</h2>
        <p>Interact with 3D models of cells. Click on organelles to learn their functions.</p>
        <div class="mt-4">
          <img src="https://upload.wikimedia.org/wikipedia/commons/1/17/Animal_Cell.svg" alt="Cell Diagram" class="w-full max-w-md mx-auto">
        </div>
      </section>

      <!-- Membrane -->
      <section x-show="currentTab==='membrane'">
        <h2 class="text-2xl font-semibold mb-2">The Cell Membrane: The Gatekeeper</h2>
        <p>Watch the animated story of how the membrane maintains balance and enables action potentials.</p>
        <div class="mt-4">
          <iframe class="w-full h-64" src="https://www.youtube.com/embed/oc2YOZk4WD8" title="Cell Membrane Story" allowfullscreen></iframe>
        </div>
      </section>

      <!-- Tissues -->
      <section x-show="currentTab==='tissues'">
        <h2 class="text-2xl font-semibold mb-2">Tissue Types & Functions</h2>
        <p>Use our microscope simulator to view different tissues.</p>
        <ul class="list-disc ml-6 mt-2">
          <li>Epithelial</li>
          <li>Connective</li>
          <li>Muscle</li>
          <li>Nervous</li>
        </ul>
      </section>

      <!-- Organs -->
      <section x-show="currentTab==='organs'">
        <h2 class="text-2xl font-semibold mb-2">Organs Formation</h2>
        <p>See how tissues organize into functional organs with interactive layering diagrams.</p>
      </section>

      <!-- Systems -->
      <section x-show="currentTab==='systems'">
        <h2 class="text-2xl font-semibold mb-2">Body Systems</h2>
        <p>Explore each system: nervous, cardiovascular, respiratory, etc. Use a clickable map.</p>
      </section>

      <!-- Integration -->
      <section x-show="currentTab==='integration'">
        <h2 class="text-2xl font-semibold mb-2">System Integration</h2>
        <p>Compare healthy and diseased states through virtual simulations. Observe how systems interconnect to maintain homeostasis.</p>
      </section>
    </div>
  </main>

  <footer class="bg-blue-900 text-white text-center p-4 mt-10">
    <p>&copy; 2025 Biomedical Education Hub. All rights reserved.</p>
  </footer>

  <style>
    .tab-btn {
      @apply bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700;
    }
  </style>
</body>
</html>
