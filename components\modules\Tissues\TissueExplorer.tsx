
import React, { useState } from 'react';
import { TISSUE_TYPES } from '../../../constants';
import type { TissueType } from '../../../types';
import Card from '../../common/Card';

const TissueExplorer: React.FC = () => {
  const [selectedTissue, setSelectedTissue] = useState<TissueType | null>(TISSUE_TYPES[0]);

  return (
    <div className="p-6 bg-white/70 backdrop-blur-sm shadow-xl rounded-xl">
      <h3 className="text-3xl font-semibold text-secondary mb-8 text-center">Interactive Tissue Explorer</h3>
      <div className="flex flex-col lg:flex-row gap-8">
        <div className="lg:w-1/3 space-y-3">
          {TISSUE_TYPES.map((tissue) => (
            <button
              key={tissue.name}
              onClick={() => setSelectedTissue(tissue)}
              className={`w-full p-4 text-left rounded-lg transition-all duration-200 border-2
                ${selectedTissue?.name === tissue.name 
                  ? 'bg-secondary text-white border-secondary shadow-lg' 
                  : 'bg-gray-100 hover:bg-accent/20 border-gray-300 text-primary'
                }`}
            >
              {tissue.name}
            </button>
          ))}
        </div>
        <div className="lg:w-2/3">
          {selectedTissue ? (
            <Card className="bg-white">
                <img src={selectedTissue.imageUrl} alt={selectedTissue.name} className="w-full h-64 object-cover rounded-t-lg mb-4" />
                <h4 className="text-2xl font-bold text-primary mb-3">{selectedTissue.name}</h4>
                <p className="text-textlight/80 mb-2"><strong>Description:</strong> {selectedTissue.description}</p>
                <p className="text-textlight/80 mb-2"><strong>Structure:</strong> {selectedTissue.structure}</p>
                <p className="text-textlight/80 mb-2"><strong>General Function:</strong> {selectedTissue.generalFunction}</p>
                <p className="text-textlight/80"><strong>Found In:</strong> {selectedTissue.examples.join(', ')}</p>
                <p className="mt-4 text-sm text-gray-500">Interactive labels on image and animated video snippets would be added here.</p>
            </Card>
          ) : (
            <p className="text-center text-textlight/70 p-8">Select a tissue type to learn more.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default TissueExplorer;
