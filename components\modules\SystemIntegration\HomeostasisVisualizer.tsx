
import React, { useState } from 'react';
import Button from '../../common/Button';

const HomeostasisVisualizer: React.FC = () => {
  const [example, setExample] = useState<'temperature' | 'glucose'>('temperature');
  const [loopType, setLoopType] = useState<'negative' | 'positive'>('negative');

  const temperatureNegativeFeedback = [
    "Stimulus: Body temperature rises (e.g., exercise, hot environment)",
    "Receptors: Temperature sensors in skin and hypothalamus detect change",
    "Control Center: Hypothalamus processes information",
    "Effectors: Sweat glands activate (sweating), blood vessels in skin dilate (vasodilation)",
    "Response: Body temperature decreases, returning to normal range"
  ];

  const glucoseNegativeFeedback = [
    "Stimulus: Blood glucose level rises (e.g., after a meal)",
    "Receptors: Pancreatic beta cells detect high glucose",
    "Control Center: Pancreas",
    "Effectors: Pancreas releases insulin",
    "Response: Insulin promotes glucose uptake by cells and storage as glycogen; blood glucose level falls"
  ];
  
  const positiveFeedbackExample = [ // Example: Blood Clotting (simplified)
    "Stimulus: Break or tear in blood vessel wall (injury)",
    "Receptors: Platelets adhere to site and release chemicals",
    "Control Center & Effectors: Released chemicals attract more platelets",
    "Response: Platelet plug is formed, cycle continues until clot is formed and bleeding stops. (Amplification)"
  ];

  const currentSteps = loopType === 'negative' 
    ? (example === 'temperature' ? temperatureNegativeFeedback : glucoseNegativeFeedback)
    : positiveFeedbackExample;

  return (
    <div className="p-4 bg-gray-50 rounded-lg shadow-inner space-y-6">
      <p className="text-lg text-textlight/90 mb-4">
        Homeostasis is the body's ability to maintain a stable internal environment despite changes in external conditions. This is often achieved through feedback loops.
      </p>
      <div className="flex flex-wrap gap-2 mb-4">
        <Button onClick={() => setLoopType('negative')} variant={loopType === 'negative' ? 'primary' : 'outline'} size="sm">Negative Feedback</Button>
        <Button onClick={() => setLoopType('positive')} variant={loopType === 'positive' ? 'primary' : 'outline'} size="sm">Positive Feedback</Button>
      </div>

      {loopType === 'negative' && (
        <div className="flex flex-wrap gap-2 mb-4">
          <Button onClick={() => setExample('temperature')} variant={example === 'temperature' ? 'secondary' : 'outline'} size="sm">Body Temperature</Button>
          <Button onClick={() => setExample('glucose')} variant={example === 'glucose' ? 'secondary' : 'outline'} size="sm">Blood Glucose</Button>
        </div>
      )}

      <h4 className="text-xl font-semibold text-primary">
        {loopType === 'negative' ? `Negative Feedback: ${example === 'temperature' ? 'Body Temperature Regulation' : 'Blood Glucose Regulation'}` : 'Positive Feedback Example: Blood Clotting'}
      </h4>
      
      <div className="space-y-3">
        {currentSteps.map((step, index) => (
          <div key={index} className="flex items-start space-x-3 p-3 bg-white border border-accent/30 rounded-md shadow-sm">
            <div className="flex-shrink-0 w-6 h-6 bg-secondary text-white rounded-full flex items-center justify-center font-bold text-sm">{index + 1}</div>
            <p className="text-textlight/80">{step}</p>
          </div>
        ))}
      </div>
      <p className="text-sm text-textlight/60 mt-4">
        {loopType === 'negative' 
          ? "Negative feedback loops counteract the initial change, bringing the system back to its set point."
          : "Positive feedback loops amplify the initial change, pushing the system further from its set point. Less common for homeostasis, often part of a larger process that has an endpoint."}
      </p>
      <p className="text-xs text-textlight/60 mt-2">This is a simplified flowchart representation. Animated diagrams would enhance understanding.</p>
    </div>
  );
};

export default HomeostasisVisualizer;
