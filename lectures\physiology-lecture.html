<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Physiology - Dynamic Animated Lecture | A&P for BME</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/animations.css">
    <link rel="stylesheet" href="../css/lecture.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation Header -->
    <header class="lecture-header">
        <nav class="lecture-nav">
            <div class="nav-container">
                <div class="nav-logo">
                    <i class="fas fa-heartbeat"></i>
                    <span>A&P for BME</span>
                </div>
                <div class="lecture-title">
                    <h1>Physiology - Dynamic Lecture</h1>
                </div>
                <div class="nav-actions">
                    <a href="../index.html" class="btn-home">
                        <i class="fas fa-home"></i> Return Home
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Lecture Progress Bar -->
    <div class="lecture-progress">
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        <div class="progress-info">
            <span id="currentSlide">1</span> / <span id="totalSlides">10</span>
        </div>
    </div>

    <!-- Lecture Content -->
    <main class="lecture-main">
        <div class="lecture-container">
            
            <!-- Slide 1: Introduction -->
            <div class="lecture-slide active" data-slide="1">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2 class="slide-title">Human Physiology</h2>
                        <p class="slide-subtitle">Understanding Body Functions</p>
                    </div>
                    <div class="slide-body">
                        <div class="content-grid">
                            <div class="content-text">
                                <h3>Learning Objectives</h3>
                                <ul class="animated-list">
                                    <li class="fade-in-up" style="animation-delay: 0.2s;">Understand homeostasis mechanisms</li>
                                    <li class="fade-in-up" style="animation-delay: 0.4s;">Explore feedback control systems</li>
                                    <li class="fade-in-up" style="animation-delay: 0.6s;">Analyze physiological processes</li>
                                    <li class="fade-in-up" style="animation-delay: 0.8s;">Examine organ system integration</li>
                                    <li class="fade-in-up" style="animation-delay: 1.0s;">Study biomedical applications</li>
                                </ul>
                            </div>
                            <div class="content-visual">
                                <div class="physiology-diagram">
                                    <div class="body-outline">
                                        <div class="organ-system brain" data-system="nervous"></div>
                                        <div class="organ-system heart" data-system="cardiovascular"></div>
                                        <div class="organ-system lungs" data-system="respiratory"></div>
                                        <div class="organ-system kidneys" data-system="renal"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2: Homeostasis -->
            <div class="lecture-slide" data-slide="2">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2 class="slide-title">Homeostasis</h2>
                        <p class="slide-subtitle">Maintaining Internal Balance</p>
                    </div>
                    <div class="slide-body">
                        <div class="homeostasis-container">
                            <div class="homeostasis-center">
                                <div class="balance-scale">
                                    <div class="scale-base"></div>
                                    <div class="scale-arm"></div>
                                    <div class="scale-left">
                                        <span>Disturbance</span>
                                    </div>
                                    <div class="scale-right">
                                        <span>Correction</span>
                                    </div>
                                </div>
                                <h3>Dynamic Equilibrium</h3>
                            </div>
                            <div class="homeostasis-examples">
                                <div class="example-card temperature">
                                    <div class="example-icon">🌡️</div>
                                    <h4>Temperature</h4>
                                    <p>37°C ± 0.5°C</p>
                                </div>
                                <div class="example-card ph">
                                    <div class="example-icon">⚗️</div>
                                    <h4>Blood pH</h4>
                                    <p>7.4 ± 0.05</p>
                                </div>
                                <div class="example-card glucose">
                                    <div class="example-icon">🍯</div>
                                    <h4>Blood Glucose</h4>
                                    <p>90-110 mg/dL</p>
                                </div>
                                <div class="example-card pressure">
                                    <div class="example-icon">💓</div>
                                    <h4>Blood Pressure</h4>
                                    <p>120/80 mmHg</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3: Feedback Systems -->
            <div class="lecture-slide" data-slide="3">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2 class="slide-title">Feedback Control Systems</h2>
                        <p class="slide-subtitle">Negative vs Positive Feedback</p>
                    </div>
                    <div class="slide-body">
                        <div class="feedback-comparison">
                            <div class="feedback-type negative">
                                <h3>Negative Feedback</h3>
                                <div class="feedback-loop">
                                    <div class="feedback-step stimulus">
                                        <div class="step-icon">📊</div>
                                        <span>Stimulus</span>
                                    </div>
                                    <div class="feedback-arrow">→</div>
                                    <div class="feedback-step receptor">
                                        <div class="step-icon">👁️</div>
                                        <span>Receptor</span>
                                    </div>
                                    <div class="feedback-arrow">→</div>
                                    <div class="feedback-step control">
                                        <div class="step-icon">🧠</div>
                                        <span>Control Center</span>
                                    </div>
                                    <div class="feedback-arrow">→</div>
                                    <div class="feedback-step effector">
                                        <div class="step-icon">⚡</div>
                                        <span>Effector</span>
                                    </div>
                                    <div class="feedback-arrow negative-arrow">↺</div>
                                </div>
                                <div class="feedback-example">
                                    <strong>Example:</strong> Body temperature regulation
                                </div>
                            </div>
                            <div class="feedback-type positive">
                                <h3>Positive Feedback</h3>
                                <div class="feedback-loop">
                                    <div class="feedback-step stimulus">
                                        <div class="step-icon">📈</div>
                                        <span>Stimulus</span>
                                    </div>
                                    <div class="feedback-arrow">→</div>
                                    <div class="feedback-step response">
                                        <div class="step-icon">🔄</div>
                                        <span>Response</span>
                                    </div>
                                    <div class="feedback-arrow">→</div>
                                    <div class="feedback-step amplification">
                                        <div class="step-icon">📢</div>
                                        <span>Amplification</span>
                                    </div>
                                    <div class="feedback-arrow positive-arrow">↗️</div>
                                </div>
                                <div class="feedback-example">
                                    <strong>Example:</strong> Blood clotting cascade
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 4: Cardiovascular System -->
            <div class="lecture-slide" data-slide="4">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2 class="slide-title">Cardiovascular Physiology</h2>
                        <p class="slide-subtitle">Heart Function and Circulation</p>
                    </div>
                    <div class="slide-body">
                        <div class="cardiovascular-container">
                            <div class="heart-animation">
                                <div class="heart-chambers">
                                    <div class="chamber ra">RA</div>
                                    <div class="chamber la">LA</div>
                                    <div class="chamber rv">RV</div>
                                    <div class="chamber lv">LV</div>
                                </div>
                                <div class="heart-valves">
                                    <div class="valve tricuspid"></div>
                                    <div class="valve mitral"></div>
                                    <div class="valve pulmonary"></div>
                                    <div class="valve aortic"></div>
                                </div>
                                <div class="blood-flow">
                                    <div class="flow-particle" style="animation-delay: 0s;"></div>
                                    <div class="flow-particle" style="animation-delay: 0.5s;"></div>
                                    <div class="flow-particle" style="animation-delay: 1s;"></div>
                                </div>
                            </div>
                            <div class="cardiac-cycle">
                                <h3>Cardiac Cycle</h3>
                                <div class="cycle-phases">
                                    <div class="phase systole">
                                        <h4>Systole</h4>
                                        <p>Ventricular contraction</p>
                                        <span>~0.3 seconds</span>
                                    </div>
                                    <div class="phase diastole">
                                        <h4>Diastole</h4>
                                        <p>Ventricular relaxation</p>
                                        <span>~0.5 seconds</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 5: Respiratory System -->
            <div class="lecture-slide" data-slide="5">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2 class="slide-title">Respiratory Physiology</h2>
                        <p class="slide-subtitle">Gas Exchange and Breathing</p>
                    </div>
                    <div class="slide-body">
                        <div class="respiratory-container">
                            <div class="breathing-animation">
                                <div class="lungs">
                                    <div class="lung left-lung">
                                        <div class="alveoli"></div>
                                    </div>
                                    <div class="lung right-lung">
                                        <div class="alveoli"></div>
                                    </div>
                                </div>
                                <div class="diaphragm"></div>
                                <div class="air-flow">
                                    <div class="air-particle o2">O₂</div>
                                    <div class="air-particle co2">CO₂</div>
                                </div>
                            </div>
                            <div class="gas-exchange">
                                <h3>Gas Exchange</h3>
                                <div class="exchange-process">
                                    <div class="gas-info oxygen">
                                        <div class="gas-molecule">O₂</div>
                                        <p>Oxygen uptake</p>
                                        <span>21% → 16%</span>
                                    </div>
                                    <div class="exchange-arrow">⇄</div>
                                    <div class="gas-info carbon-dioxide">
                                        <div class="gas-molecule">CO₂</div>
                                        <p>Carbon dioxide release</p>
                                        <span>0.04% → 4%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </main>

    <!-- Lecture Controls -->
    <div class="lecture-controls">
        <button class="control-btn" id="prevBtn">
            <i class="fas fa-chevron-left"></i> Previous
        </button>
        <div class="slide-indicators" id="slideIndicators">
            <!-- Dynamically generated -->
        </div>
        <button class="control-btn" id="nextBtn">
            Next <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <!-- Lecture Sidebar -->
    <div class="lecture-sidebar" id="lectureSidebar">
        <div class="sidebar-header">
            <h3>Lecture Outline</h3>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        <div class="sidebar-content">
            <ul class="lecture-outline">
                <li class="outline-item active" data-slide="1">
                    <span class="outline-number">1</span>
                    <span class="outline-title">Introduction</span>
                </li>
                <li class="outline-item" data-slide="2">
                    <span class="outline-number">2</span>
                    <span class="outline-title">Homeostasis</span>
                </li>
                <li class="outline-item" data-slide="3">
                    <span class="outline-number">3</span>
                    <span class="outline-title">Feedback Systems</span>
                </li>
                <li class="outline-item" data-slide="4">
                    <span class="outline-number">4</span>
                    <span class="outline-title">Cardiovascular</span>
                </li>
                <li class="outline-item" data-slide="5">
                    <span class="outline-number">5</span>
                    <span class="outline-title">Respiratory</span>
                </li>
            </ul>
        </div>
    </div>

    <!-- Author Information -->
    <div class="lecture-footer">
        <div class="author-info">
            <p><strong>Dr. Mohammed Yagoub Esmail</strong> | SUST BME @2025</p>
            <p><EMAIL> | +249912867327 | +966538076790</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../js/main.js"></script>
    <script src="../js/lecture.js"></script>
</body>
</html>
