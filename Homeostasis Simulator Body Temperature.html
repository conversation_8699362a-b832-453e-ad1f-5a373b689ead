<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Homeostasis Simulator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5; /* Light grey background */
            color: #333;
            display: flex;
            justify-content: center;
            align-items: flex-start; /* Align to top to allow scrolling if content overflows */
            min-height: 100vh;
            box-sizing: border-box;
        }
        .container {
            background: #ffffff; /* White container background */
            padding: 20px 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 650px; /* Max width for content */
            text-align: center;
        }
        h1 {
            color: #1a253c; /* Dark blue heading */
            margin-bottom: 15px;
            font-size: 1.8em; /* Responsive font size */
        }
        .intro-text {
            font-size: 0.95em;
            color: #4a5568; /* Medium grey text */
            margin-bottom: 20px;
            text-align: left;
            padding: 10px 15px; /* Padding for readability */
            border-left: 3px solid #3498db; /* Accent border */
            background-color: #f8f9fa; /* Slight background tint for the intro */
            border-radius: 4px;
        }
        .game-goal {
            font-weight: 600; /* Semibold */
            margin-bottom: 25px;
            font-size: 1.15em;
            color: #2c3e50; /* Darker grey for goal text */
        }

        .display-section, .controls-section, .status-section, .timer-section {
            margin-bottom: 25px;
            padding: 18px;
            background-color: #f8f9fa; /* Very light grey for sections */
            border-radius: 8px;
            border: 1px solid #e9ecef; /* Subtle border for sections */
        }
        
        .display-section h2, .timer-section h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #34495e; /* Section title color */
            font-size: 1.3em;
        }

        .variable-info {
            font-size: 1.1em;
            color: #555;
            margin-bottom: 8px;
        }
        .variable-value {
            font-size: 2.8em; /* Large display for the current value */
            font-weight: 700; /* Bold */
            color: #3498db; /* Primary blue for the value */
            margin: 8px 0 12px 0;
            line-height: 1.2; /* Adjust line height for large font */
        }
        .target-range {
            font-size: 1.0em;
            color: #7f8c8d; /* Muted color for target range text */
        }

        .controls-section {
            display: flex;
            justify-content: center;
            gap: 15px; /* Space between buttons */
            flex-wrap: wrap; /* Allow buttons to wrap on smaller screens */
        }
        .controls-section button {
            padding: 12px 22px;
            font-size: 1.0em;
            font-weight: 500; /* Medium weight for button text */
            cursor: pointer;
            border: none;
            border-radius: 6px;
            color: white;
            transition: background-color 0.2s ease, transform 0.1s ease;
            min-width: 150px; /* Minimum width for buttons */
        }
        .controls-section button:active {
            transform: translateY(1px); /* Slight press effect */
        }

        #decreaseBtn { background-color: #e74c3c; } /* Red for decrease/cool down */
        #decreaseBtn:hover { background-color: #c0392b; }
        #increaseBtn { background-color: #2ecc71; } /* Green for increase/warm up */
        #increaseBtn:hover { background-color: #27ae60; }

        .status-section-content { 
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px; /* Space between "Feedback:" text and indicator */
            font-size: 1.1em;
        }
        .status-indicator {
            font-size: 1.1em; /* Adjusted for better balance with label */
            font-weight: bold;
            padding: 8px 15px;
            border-radius: 6px;
            display: inline-block; /* Allows padding and border styling */
            min-width: 160px; /* Consistent width for status message */
            box-sizing: border-box;
        }
        .status-ok {
            color: #27ae60; /* Darker Green for text */
            background-color: #e6f7ee; /* Light Green Background */
            border: 1px solid #a6dcb0; /* Greenish border */
        }
        .status-error {
            color: #c0392b; /* Darker Red for text */
            background-color: #fbeeed; /* Light Red Background */
            border: 1px solid #eab6b3; /* Reddish border */
        }
        .timer-label {
            font-size: 1.1em;
            color: #555;
            margin-bottom: 8px;
        }
        .timer-value {
            font-size: 2.0em; /* Large display for timer */
            font-weight: 700; /* Bold */
            color: #2c3e50; /* Dark grey for timer text */
            margin-bottom: 15px;
            display: block; /* Ensures it takes full width for centering */
        }
        #resetBtn {
            padding: 12px 28px;
            font-size: 1.05em;
            font-weight: 500;
            background-color: #7f8c8d; /* Neutral grey for reset button */
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s ease, transform 0.1s ease;
        }
        #resetBtn:hover {
            background-color: #6c7a7d; /* Darker grey on hover */
        }
        #resetBtn:active {
            transform: translateY(1px); /* Press effect */
        }

        /* Responsive adjustments for smaller screens */
        @media (max-width: 520px) {
            .container {
                padding: 15px 20px; /* Reduced padding on small screens */
            }
            h1 { font-size: 1.6em; }
            .intro-text { font-size: 0.9em; padding-left: 10px; }
            .variable-value { font-size: 2.4em; }
            .timer-value { font-size: 1.8em; }
            
            .controls-section {
                flex-direction: column; /* Stack buttons vertically */
                align-items: stretch; /* Make buttons full width of their container */
            }
            .controls-section button {
                width: 100%; /* Full width buttons */
                margin: 6px 0; /* Adjust margin for column layout */
                min-width: auto; /* Reset min-width for stacked buttons */
            }
            .status-indicator {
                font-size: 1.0em; /* Slightly smaller status text */
                padding: 8px 10px;
                min-width: 140px;
            }
            .status-section-content {
                font-size: 1.0em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Homeostasis Simulator: Body Temperature</h1>
        <p class="intro-text">
            In anatomy and physiology, homeostasis is the ability of the body to maintain an internal set of specific conditions. Many things in the body are maintained within a homeostatic set range, including body temperature, blood pressure, fluid volume, electrolytes, and pH. The nervous and endocrine systems are the main regulatory forces that keep these variables within range. When the body is unable to maintain homeostasis, disease results.
        </p>
        <p class="game-goal">Your goal: Keep the Body Temperature within the target range for as long as possible.</p>

        <div class="display-section">
            <h2>Current Status</h2>
            <div class="variable-info">Physiological Variable: Body Temperature</div>
            <div id="currentValueDisplay" class="variable-value">98.6 °F</div>
            <div id="targetRangeDisplay" class="target-range">Target: 97.0 - 99.0 °F</div>
        </div>

        <div class="controls-section">
            <button id="decreaseBtn" title="Decrease body temperature">Cool Down (-0.1°F)</button>
            <button id="increaseBtn" title="Increase body temperature">Warm Up (+0.1°F)</button>
        </div>

        <div class="status-section">
            <div class="status-section-content">
                <span>Feedback:</span>
                <span id="statusIndicator">Initializing...</span>
            </div>
        </div>

        <div class="timer-section">
            <h3>Game Progress</h3>
            <div class="timer-label">Time in Homeostasis:</div>
            <div id="timerDisplay" class="timer-value">0 seconds</div>
            <button id="resetBtn">Reset Simulation</button>
        </div>
    </div>

    <script>
        // Physiological variable settings
        const UNIT = "°F";
        const TARGET_MIN = 97.0;
        const TARGET_MAX = 99.0;
        const ADJUST_STEP = 0.1;
        const INITIAL_VALUE_MIN = 95.0; // Range for random initial value
        const INITIAL_VALUE_MAX = 102.0; // Range for random initial value
        const DECIMAL_PLACES = 1;

        // DOM Elements
        let currentValueDisplay, targetRangeDisplay, decreaseBtn, increaseBtn, statusIndicator, timerDisplay, resetBtn;

        // Game state variables
        let currentPhysiologicalValue;
        let timeInHomeostasis;
        let homeostasisTimerInterval;
        let isTimerActive;

        // Helper function to get a random float within a range, to a specific number of decimal places
        function getRandomFloat(min, max, decimals) {
            const str = (Math.random() * (max - min) + min).toFixed(decimals);
            return parseFloat(str);
        }

        // Update all UI display elements
        function updateUIDisplay() {
            currentValueDisplay.textContent = `${currentPhysiologicalValue.toFixed(DECIMAL_PLACES)} ${UNIT}`;
            timerDisplay.textContent = `${timeInHomeostasis} second${timeInHomeostasis === 1 ? '' : 's'}`;
        }

        // Check if the current value is within the homeostatic range and update status/timer
        function checkHomeostasis() {
            const isInRange = currentPhysiologicalValue >= TARGET_MIN && currentPhysiologicalValue <= TARGET_MAX;

            if (isInRange) {
                statusIndicator.textContent = '✔ In Range';
                statusIndicator.className = 'status-indicator status-ok';
                if (!isTimerActive) {
                    startTimer();
                }
            } else {
                statusIndicator.textContent = '❌ Out of Range';
                statusIndicator.className = 'status-indicator status-error';
                if (isTimerActive) {
                    stopTimer();
                }
            }
        }

        // Start the homeostasis timer
        function startTimer() {
            if (isTimerActive) return; // Prevent multiple intervals if already active
            isTimerActive = true;
            homeostasisTimerInterval = setInterval(() => {
                timeInHomeostasis++;
                updateUIDisplay(); // Update display, primarily for the timer text
            }, 1000);
        }

        // Stop the homeostasis timer
        function stopTimer() {
            if (!isTimerActive) return; // Do nothing if timer is not active
            isTimerActive = false;
            clearInterval(homeostasisTimerInterval);
        }

        // Handle value increase
        function handleIncrease() {
            currentPhysiologicalValue = parseFloat((currentPhysiologicalValue + ADJUST_STEP).toFixed(DECIMAL_PLACES));
            updateUIDisplay();
            checkHomeostasis();
        }

        // Handle value decrease
        function handleDecrease() {
            currentPhysiologicalValue = parseFloat((currentPhysiologicalValue - ADJUST_STEP).toFixed(DECIMAL_PLACES));
            updateUIDisplay();
            checkHomeostasis();
        }
        
        // Initialize or reset the game
        function initializeGame() {
            stopTimer(); // Ensure any existing timer is stopped before re-initializing

            currentPhysiologicalValue = getRandomFloat(INITIAL_VALUE_MIN, INITIAL_VALUE_MAX, DECIMAL_PLACES);
            timeInHomeostasis = 0;
            isTimerActive = false; // Reset timer active flag explicitly

            // Update static displays like target range
            targetRangeDisplay.textContent = `Target: ${TARGET_MIN.toFixed(DECIMAL_PLACES)} - ${TARGET_MAX.toFixed(DECIMAL_PLACES)} ${UNIT}`;
            
            // Initial UI update and status check
            updateUIDisplay();
            checkHomeostasis(); // This will set initial status and start timer if in range
        }

        // Event listeners setup
        document.addEventListener('DOMContentLoaded', () => {
            // Assign DOM elements to variables
            currentValueDisplay = document.getElementById('currentValueDisplay');
            targetRangeDisplay = document.getElementById('targetRangeDisplay');
            decreaseBtn = document.getElementById('decreaseBtn');
            increaseBtn = document.getElementById('increaseBtn');
            statusIndicator = document.getElementById('statusIndicator');
            timerDisplay = document.getElementById('timerDisplay');
            resetBtn = document.getElementById('resetBtn');

            // Attach event listeners to controls
            decreaseBtn.addEventListener('click', handleDecrease);
            increaseBtn.addEventListener('click', handleIncrease);
            resetBtn.addEventListener('click', initializeGame);

            // Initialize the game state and UI
            initializeGame();
        });
    </script>
</body>
</html>
