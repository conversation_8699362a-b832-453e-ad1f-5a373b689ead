
import React, { useState } from 'react';
import type { BodySystemData } from '../../../types';
import Modal from '../../common/Modal';

interface InteractiveHumanBodyMapProps {
    systems: BodySystemData[];
}

// Basic SVG Human Outline (very simplified)
const HumanOutline: React.FC<{ onClickSystem: (systemName: string, event: React.MouseEvent) => void, highlightedSystem: string | null }> = ({ onClickSystem, highlightedSystem }) => (
    <svg viewBox="0 0 200 400" className="w-full max-w-xs mx-auto h-auto drop-shadow-lg">
        <defs>
            <filter id="glow">
                <feGaussianBlur stdDeviation="2.5" result="coloredBlur"/>
                <feMerge>
                    <feMergeNode in="coloredBlur"/>
                    <feMergeNode in="SourceGraphic"/>
                </feMerge>
            </filter>
        </defs>
        {/* Head - Nervous System */}
        <ellipse 
            cx="100" cy="50" rx="40" ry="40" 
            className={`cursor-pointer transition-all duration-300 ${highlightedSystem === 'Nervous System' ? 'fill-blue-500' : 'fill-gray-300 hover:fill-gray-400'}`} 
            stroke="gray" strokeWidth="2" 
            onClick={(e) => onClickSystem('Nervous System', e)}
            filter={highlightedSystem === 'Nervous System' ? "url(#glow)" : ""}
        />
        {/* Torso - Cardiovascular, Respiratory, Digestive */}
        <rect 
            x="60" y="90" width="80" height="120" rx="10" 
            className={`cursor-pointer transition-all duration-300 ${highlightedSystem && ['Cardiovascular System', 'Respiratory System', 'Digestive System'].includes(highlightedSystem) ? 'fill-red-400' : 'fill-gray-300 hover:fill-gray-400'}`} 
            stroke="gray" strokeWidth="2" 
            onClick={(e) => onClickSystem('Cardiovascular System', e)} // Default to Cardio for torso click
            filter={highlightedSystem && ['Cardiovascular System', 'Respiratory System', 'Digestive System'].includes(highlightedSystem) ? "url(#glow)" : ""}
        />
        {/* Limbs - Musculoskeletal */}
        <rect x="30" y="100" width="20" height="100" className="fill-gray-300 stroke-gray-200" strokeWidth="1"/>
        <rect x="150" y="100" width="20" height="100" className="fill-gray-300 stroke-gray-200" strokeWidth="1"/>
        <rect x="50" y="210" width="30" height="120" className="fill-gray-300 stroke-gray-200" strokeWidth="1"/>
        <rect x="120" y="210" width="30" height="120" className="fill-gray-300 stroke-gray-200" strokeWidth="1"/>
        
        <text x="100" y="55" textAnchor="middle" fontSize="10" fill="black" className="pointer-events-none">Nervous</text>
        <text x="100" y="150" textAnchor="middle" fontSize="10" fill="black" className="pointer-events-none">Core Systems</text>
    </svg>
);


const InteractiveHumanBodyMap: React.FC<InteractiveHumanBodyMapProps> = ({ systems }) => {
    const [selectedSystem, setSelectedSystem] = useState<BodySystemData | null>(null);
    const [modalPosition, setModalPosition] = useState<{top: number, left: number} | null>(null);


    const handleSystemClick = (systemName: string, event: React.MouseEvent) => {
        const system = systems.find(s => s.name === systemName) || systems.find(s => s.name.includes(systemName.split(' ')[0])); // Fallback for general area clicks
        if (system) {
            setSelectedSystem(system);
            // Position modal near click
            const rect = (event.currentTarget as SVGRectElement).getBoundingClientRect();
            setModalPosition({ top: rect.bottom + window.scrollY + 5, left: rect.left + window.scrollX });
        } else {
             setSelectedSystem(systems[0]); // Default if not found
             setModalPosition({top: event.clientY, left: event.clientX });
        }
    };
    
    const closeModal = () => {
        setSelectedSystem(null);
        setModalPosition(null);
    };

    return (
        <div className="p-6 bg-white/70 backdrop-blur-sm shadow-xl rounded-xl text-center">
            <h3 className="text-3xl font-semibold text-secondary mb-4">Interactive Human Body Map</h3>
            <p className="text-textlight/80 mb-8">
                Click on a region of the body or select a system from the list to learn more.
                (This is a highly simplified representation).
            </p>
            <div className="flex flex-col md:flex-row gap-8 items-start">
                <div className="w-full md:w-1/2 lg:w-2/5">
                     <HumanOutline onClickSystem={handleSystemClick} highlightedSystem={selectedSystem?.name || null} />
                </div>
                <div className="w-full md:w-1/2 lg:w-3/5">
                    <h4 className="text-xl font-semibold text-primary mb-3">Available Systems:</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        {systems.map(system => (
                            <button 
                                key={system.name}
                                onClick={(e) => handleSystemClick(system.name, e)}
                                className={`p-3 rounded-lg border-2 text-left transition-all ${selectedSystem?.name === system.name ? 'bg-secondary text-white border-secondary shadow-md' : 'bg-gray-100 hover:bg-accent/20 border-gray-300'}`}
                            >
                                {system.name}
                            </button>
                        ))}
                    </div>
                </div>
            </div>

            {selectedSystem && (
                 <Modal isOpen={!!selectedSystem} onClose={closeModal} title={selectedSystem.name} size="md">
                    <img src={selectedSystem.imageUrl} alt={selectedSystem.name} className="w-full h-48 object-cover rounded-lg mb-4"/>
                    <p className="font-semibold text-lg text-primary">Main Function:</p>
                    <p className="mb-3 text-textlight/90">{selectedSystem.mainFunction}</p>
                    <p className="font-semibold text-lg text-primary">Primary Organs:</p>
                    <p className="text-textlight/90">{selectedSystem.primaryOrgans.join(', ')}</p>
                    {selectedSystem.processVideoUrl && (
                        <div className="mt-4">
                            <p className="font-semibold text-lg text-primary">Key Process Video (Placeholder):</p>
                            <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg flex items-center justify-center mt-2">
                                <p className="text-gray-500">Video: {selectedSystem.name} Process</p>
                            </div>
                        </div>
                    )}
                </Modal>
            )}
        </div>
    );
};

export default InteractiveHumanBodyMap;
