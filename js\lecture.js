// ===== LECTURE FUNCTIONALITY =====

class LecturePresentation {
    constructor() {
        this.currentSlide = 1;
        this.totalSlides = 0;
        this.slides = [];
        this.isAutoPlay = false;
        this.autoPlayInterval = null;
        
        this.init();
    }
    
    init() {
        this.slides = document.querySelectorAll('.lecture-slide');
        this.totalSlides = this.slides.length;
        
        this.setupControls();
        this.setupIndicators();
        this.setupSidebar();
        this.setupKeyboardNavigation();
        this.updateProgress();
        this.animateCurrentSlide();
        
        // Update total slides display
        const totalSlidesElement = document.getElementById('totalSlides');
        if (totalSlidesElement) {
            totalSlidesElement.textContent = this.totalSlides;
        }
    }
    
    setupControls() {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        
        if (prevBtn) {
            prevBtn.addEventListener('click', () => this.previousSlide());
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', () => this.nextSlide());
        }
        
        this.updateControlButtons();
    }
    
    setupIndicators() {
        const indicatorsContainer = document.getElementById('slideIndicators');
        if (!indicatorsContainer) return;
        
        indicatorsContainer.innerHTML = '';
        
        for (let i = 1; i <= this.totalSlides; i++) {
            const indicator = document.createElement('div');
            indicator.className = `slide-indicator ${i === this.currentSlide ? 'active' : ''}`;
            indicator.addEventListener('click', () => this.goToSlide(i));
            indicatorsContainer.appendChild(indicator);
        }
    }
    
    setupSidebar() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('lectureSidebar');
        const outlineItems = document.querySelectorAll('.outline-item');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('active');
            });
        }
        
        outlineItems.forEach(item => {
            item.addEventListener('click', () => {
                const slideNumber = parseInt(item.dataset.slide);
                this.goToSlide(slideNumber);
                if (sidebar) {
                    sidebar.classList.remove('active');
                }
            });
        });
    }
    
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                case 'ArrowUp':
                    e.preventDefault();
                    this.previousSlide();
                    break;
                case 'ArrowRight':
                case 'ArrowDown':
                case ' ':
                    e.preventDefault();
                    this.nextSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    this.goToSlide(1);
                    break;
                case 'End':
                    e.preventDefault();
                    this.goToSlide(this.totalSlides);
                    break;
                case 'Escape':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
            }
        });
    }
    
    nextSlide() {
        if (this.currentSlide < this.totalSlides) {
            this.goToSlide(this.currentSlide + 1);
        }
    }
    
    previousSlide() {
        if (this.currentSlide > 1) {
            this.goToSlide(this.currentSlide - 1);
        }
    }
    
    goToSlide(slideNumber) {
        if (slideNumber < 1 || slideNumber > this.totalSlides) return;
        
        // Hide current slide
        const currentSlideElement = document.querySelector('.lecture-slide.active');
        if (currentSlideElement) {
            currentSlideElement.classList.remove('active');
        }
        
        // Show new slide
        const newSlideElement = document.querySelector(`[data-slide="${slideNumber}"]`);
        if (newSlideElement) {
            newSlideElement.classList.add('active');
        }
        
        this.currentSlide = slideNumber;
        this.updateProgress();
        this.updateIndicators();
        this.updateOutline();
        this.updateControlButtons();
        this.animateCurrentSlide();
        
        // Update current slide display
        const currentSlideElement = document.getElementById('currentSlide');
        if (currentSlideElement) {
            currentSlideElement.textContent = this.currentSlide;
        }
    }
    
    updateProgress() {
        const progressFill = document.getElementById('progressFill');
        if (progressFill) {
            const progressPercentage = (this.currentSlide / this.totalSlides) * 100;
            progressFill.style.width = `${progressPercentage}%`;
        }
    }
    
    updateIndicators() {
        const indicators = document.querySelectorAll('.slide-indicator');
        indicators.forEach((indicator, index) => {
            indicator.classList.toggle('active', index + 1 === this.currentSlide);
        });
    }
    
    updateOutline() {
        const outlineItems = document.querySelectorAll('.outline-item');
        outlineItems.forEach(item => {
            const slideNumber = parseInt(item.dataset.slide);
            item.classList.toggle('active', slideNumber === this.currentSlide);
        });
    }
    
    updateControlButtons() {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        
        if (prevBtn) {
            prevBtn.disabled = this.currentSlide === 1;
        }
        
        if (nextBtn) {
            nextBtn.disabled = this.currentSlide === this.totalSlides;
        }
    }
    
    animateCurrentSlide() {
        const currentSlideElement = document.querySelector('.lecture-slide.active');
        if (!currentSlideElement) return;
        
        // Animate elements with data-animation attribute
        const animatedElements = currentSlideElement.querySelectorAll('[data-animation]');
        animatedElements.forEach((element, index) => {
            const animation = element.dataset.animation;
            element.style.animationDelay = `${index * 0.2}s`;
            element.classList.add(animation);
        });
        
        // Animate theory cards
        const theoryCards = currentSlideElement.querySelectorAll('.theory-card');
        theoryCards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 200);
        });
        
        // Animate list items
        const listItems = currentSlideElement.querySelectorAll('.animated-list li');
        listItems.forEach((item, index) => {
            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, index * 150);
        });
    }
    
    toggleAutoPlay() {
        if (this.isAutoPlay) {
            this.stopAutoPlay();
        } else {
            this.startAutoPlay();
        }
    }
    
    startAutoPlay(interval = 5000) {
        this.isAutoPlay = true;
        this.autoPlayInterval = setInterval(() => {
            if (this.currentSlide < this.totalSlides) {
                this.nextSlide();
            } else {
                this.stopAutoPlay();
            }
        }, interval);
    }
    
    stopAutoPlay() {
        this.isAutoPlay = false;
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }
    
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log(`Error attempting to enable fullscreen: ${err.message}`);
            });
        } else {
            document.exitFullscreen();
        }
    }
    
    // Export lecture as PDF (placeholder for future implementation)
    exportToPDF() {
        console.log('PDF export functionality would be implemented here');
        // This would integrate with a library like jsPDF or Puppeteer
    }
    
    // Share lecture (placeholder for future implementation)
    shareLecture() {
        if (navigator.share) {
            navigator.share({
                title: document.title,
                url: window.location.href
            });
        } else {
            // Fallback: copy URL to clipboard
            navigator.clipboard.writeText(window.location.href);
            this.showNotification('Lecture URL copied to clipboard!');
        }
    }
    
    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'lecture-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--lecture-primary);
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            box-shadow: var(--lecture-shadow);
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// ===== LECTURE ANIMATIONS =====

// CSS Animation classes
const animationClasses = {
    slideInLeft: 'animate__animated animate__slideInLeft',
    slideInRight: 'animate__animated animate__slideInRight',
    slideInUp: 'animate__animated animate__slideInUp',
    slideInDown: 'animate__animated animate__slideInDown',
    fadeIn: 'animate__animated animate__fadeIn',
    fadeInUp: 'animate__animated animate__fadeInUp',
    zoomIn: 'animate__animated animate__zoomIn',
    bounceIn: 'animate__animated animate__bounceIn'
};

// Add animation styles to document
const animationStyles = `
    @keyframes slideInLeft {
        from { opacity: 0; transform: translateX(-100px); }
        to { opacity: 1; transform: translateX(0); }
    }
    
    @keyframes slideInRight {
        from { opacity: 0; transform: translateX(100px); }
        to { opacity: 1; transform: translateX(0); }
    }
    
    @keyframes slideInUp {
        from { opacity: 0; transform: translateY(100px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes slideInDown {
        from { opacity: 0; transform: translateY(-100px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes slideOutRight {
        from { opacity: 1; transform: translateX(0); }
        to { opacity: 0; transform: translateX(100px); }
    }
    
    .slideInLeft { animation: slideInLeft 0.6s ease-out; }
    .slideInRight { animation: slideInRight 0.6s ease-out; }
    .slideInUp { animation: slideInUp 0.6s ease-out; }
    .slideInDown { animation: slideInDown 0.6s ease-out; }
`;

// Inject animation styles
const styleSheet = document.createElement('style');
styleSheet.textContent = animationStyles;
document.head.appendChild(styleSheet);

// ===== INITIALIZE LECTURE =====

document.addEventListener('DOMContentLoaded', function() {
    // Initialize lecture presentation
    const lecture = new LecturePresentation();
    
    // Make lecture instance globally available
    window.lecturePresentation = lecture;
    
    // Add additional event listeners
    document.addEventListener('visibilitychange', function() {
        if (document.hidden && lecture.isAutoPlay) {
            lecture.stopAutoPlay();
        }
    });
    
    // Handle window resize
    window.addEventListener('resize', function() {
        // Recalculate any responsive elements if needed
        lecture.updateProgress();
    });
    
    // Add touch/swipe support for mobile
    let touchStartX = 0;
    let touchEndX = 0;
    
    document.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    });
    
    document.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    });
    
    function handleSwipe() {
        const swipeThreshold = 50;
        const swipeDistance = touchEndX - touchStartX;
        
        if (Math.abs(swipeDistance) > swipeThreshold) {
            if (swipeDistance > 0) {
                // Swipe right - previous slide
                lecture.previousSlide();
            } else {
                // Swipe left - next slide
                lecture.nextSlide();
            }
        }
    }
    
    console.log('Lecture presentation initialized successfully');
});
