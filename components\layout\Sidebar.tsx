
import React from 'react';
import type { Module } from '../../types';
import { APP_TITLE } from '../../constants';
import { CloseIcon } from '../icons/MenuIcon';

interface SidebarProps {
  modules: Module[];
  activeModule: string;
  onNavigate: (id: string) => void;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ modules, activeModule, onNavigate, isOpen, setIsOpen }) => {
  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/30 z-30 lg:hidden"
          onClick={() => setIsOpen(false)}
        ></div>
      )}

      <aside 
        className={`fixed lg:sticky top-0 left-0 h-full w-72 bg-primary text-white p-6 shadow-2xl transform ${isOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0 transition-transform duration-300 ease-in-out z-40 flex flex-col`}
      >
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-2xl font-bold text-accent">{APP_TITLE.split(':')[0]}</h1>
          <button onClick={() => setIsOpen(false)} className="lg:hidden text-accent hover:text-white">
            <CloseIcon className="w-6 h-6" />
          </button>
        </div>
        <nav className="flex-grow overflow-y-auto">
          <ul>
            {modules.map((module) => (
              <li key={module.id} className="mb-2">
                <button
                  onClick={() => onNavigate(module.id)}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center space-x-3 transition-all duration-200
                    ${activeModule === module.id ? 'bg-secondary text-white shadow-md' : 'hover:bg-secondary/70 hover:text-white text-accent/90'}
                  `}
                >
                  {module.icon && <module.icon className="w-5 h-5 flex-shrink-0" />}
                  <span>{module.title}</span>
                </button>
              </li>
            ))}
          </ul>
        </nav>
        <div className="mt-auto pt-6 border-t border-secondary/30">
            <p className="text-xs text-accent/70">© {new Date().getFullYear()} BME Interactive Learning</p>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
