
import React, { useState } from 'react';
import { GLOSSARY_TERMS } from '../../../constants';
import type { GlossaryTerm } from '../../../types';

const TerminologyGlossary: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedTerm, setExpandedTerm] = useState<string | null>(null);

  const filteredTerms = GLOSSARY_TERMS.filter(termObj =>
    termObj.term.toLowerCase().includes(searchTerm.toLowerCase()) ||
    termObj.definition.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const toggleTerm = (term: string) => {
    setExpandedTerm(expandedTerm === term ? null : term);
  };

  return (
    <div className="space-y-4">
      <input
        type="text"
        placeholder="Search terms..."
        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent transition-shadow"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
      />
      {filteredTerms.length > 0 ? (
        <div className="max-h-96 overflow-y-auto space-y-2 pr-2">
          {filteredTerms.map((termObj) => (
            <div key={termObj.term} className="border border-accent/30 rounded-lg shadow-sm bg-white">
              <button
                onClick={() => toggleTerm(termObj.term)}
                className="w-full flex justify-between items-center p-3 text-left font-medium text-primary hover:bg-accent/10 transition-colors"
              >
                <span>{termObj.term}</span>
                <span>{expandedTerm === termObj.term ? '-' : '+'}</span>
              </button>
              {expandedTerm === termObj.term && (
                <div className="p-3 border-t border-accent/30 text-textlight/80">
                  {termObj.definition}
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <p className="text-textlight/70">No terms found matching your search.</p>
      )}
      <p className="text-sm text-textlight/60 mt-4">
        These terms will also be highlighted throughout the page with hover-over definitions (feature placeholder).
      </p>
    </div>
  );
};

export default TerminologyGlossary;
