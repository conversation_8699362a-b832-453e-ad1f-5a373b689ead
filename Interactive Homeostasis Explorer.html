<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Homeostasis Interactive Learning</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: auto;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        h1, h2 {
            color: #333;
            text-align: center;
        }

        .instructions {
            background-color: #e7f3fe;
            border-left: 6px solid #2196F3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .main-content {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .concepts-panel {
            flex: 1;
            min-width: 200px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }

        .concepts-panel h3 {
            margin-top: 0;
        }

        .concept-item {
            padding: 10px;
            margin-bottom: 10px;
            background-color: #2196F3;
            color: white;
            border-radius: 4px;
            cursor: grab;
            text-align: center;
            user-select: none; /* Prevent text selection during drag */
        }

        .concept-item.explored .feedback-icon {
            color: lightgreen;
            font-weight: bold;
        }

        .diagrams-area {
            flex: 3;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .diagram-container {
            border: 2px dashed #ccc;
            padding: 15px;
            min-height: 300px;
            border-radius: 4px;
            position: relative; /* For absolute positioning of labels */
            background-color: #fff;
            overflow: hidden; /* Prevent labels from overflowing badly */
        }

        .diagram-container.drag-over {
            border-color: #2196F3;
            background-color: #e7f3fe;
        }

        .diagram-title {
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }

        /* Cell Diagram Specifics */
        .cell-diagram {
            background-color: #f0f8ff; /* Light blue for cell */
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .cell-representation {
            width: 200px;
            height: 200px;
            border: 3px solid #6495ed; /* Cornflower blue */
            border-radius: 50%;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #add8e6; /* Lighter blue */
        }
        .cell-label {
            position: absolute;
            font-size: 0.8em;
            padding: 2px 5px;
            background: rgba(255,255,255,0.8);
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .cell-label.highlight {
            background-color: yellow;
            font-weight: bold;
            transform: scale(1.1);
            transition: all 0.3s ease;
        }
        #cell-membrane { top: -20px; left: 50%; transform: translateX(-50%); }
        #cell-receptors { top: 10px; left: -30px; transform: rotate(-30deg); }
        #cell-glucose { bottom: 10px; left: -40px; color: green; }
        #cell-oxygen { top: 50%; transform: translateY(-50%); left: -40px; color: blue; }
        #cell-atp { top: 50%; left: 50%; transform: translate(-50%, -50%); color: red; font-weight: bold; }
        #cell-waste { bottom: 10px; right: -50px; color: brown; }

        /* Body Diagram Specifics */
        .body-diagram {
            background-color: #fff0f5; /* Lavender blush for body */
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column; /* Stack systems */
        }
        .body-representation {
            width: 150px; /* Smaller than cell for this representation */
            height: 250px;
            border: 3px solid #ffb6c1; /* Light pink */
            border-radius: 50px / 20px; /* Simple oval shape */
            position: relative;
            background-color: #ffe4e1; /* Misty rose */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-around;
            padding: 10px;
        }
        .organ-system {
            font-size: 0.8em;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            background-color: rgba(255,255,255,0.9);
            margin: 2px 0;
            text-align: center;
            width: 80%;
        }
        .organ-system.highlight {
            background-color: lightgreen;
            font-weight: bold;
            transform: scale(1.1);
            transition: all 0.3s ease;
        }

        .description-area {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #e9e9e9;
            min-height: 100px;
        }
        .description-area h4 { margin-top: 0; }
        .description-area p { margin-bottom: 0; }

        .quiz-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .quiz-question { margin-bottom: 15px; }
        .quiz-options button {
            display: block;
            margin: 5px 0;
            padding: 10px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
            text-align: left;
        }
        .quiz-options button:hover { background-color: #1976D2; }
        .quiz-feedback {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .quiz-feedback.correct { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .quiz-feedback.incorrect { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        #next-question-btn {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        #next-question-btn:disabled {
            background-color: #ccc;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            .diagrams-area {
                flex-direction: column;
            }
            .cell-representation, .body-representation {
                transform: scale(0.9); /* Slightly smaller diagrams on mobile */
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Interactive Homeostasis Explorer</h1>
        <div class="instructions">
            <p><strong>Welcome!</strong> Anatomy and physiology explore the body's structure and function. Homeostasis is the body's ability to maintain a stable internal environment.</p>
            <p><strong>Instructions:</strong>
                <ol>
                    <li>Drag the concepts from the "Homeostasis Concepts" list below.</li>
                    <li>Drop them onto either the "Cell Diagram" or the "Human Body Diagram".</li>
                    <li>Read the description that appears to understand how the concept applies in that context. Relevant parts of the diagram will highlight.</li>
                    <li>A <span style="color: green; font-weight: bold;">✔</span> will appear next to a concept once you've explored it.</li>
                    <li>After exploring, test your knowledge in the "Check My Understanding" section.</li>
                </ol>
            </p>
        </div>

        <div class="main-content">
            <div class="concepts-panel">
                <h3>Homeostasis Concepts</h3>
                <div id="concepts-list">
                    <!-- Concepts will be populated by JavaScript -->
                </div>
            </div>

            <div class="diagrams-area">
                <div id="cell-diagram-container" class="diagram-container">
                    <div class="diagram-title">Cell Diagram</div>
                    <div class="cell-diagram">
                        <div class="cell-representation">
                            <div id="cell-nucleus" class="cell-label" style="font-size:0.7em; background: #bbb; width: 50px; height: 50px; border-radius: 50%; display:flex; align-items:center; justify-content:center;">Nucleus</div>
                            <div id="cell-membrane" class="cell-label">Cell Membrane</div>
                            <div id="cell-receptors" class="cell-label">Receptors</div>
                            <div id="cell-glucose" class="cell-label">Glucose (In)</div>
                            <div id="cell-oxygen" class="cell-label">Oxygen (In)</div>
                            <div id="cell-atp" class="cell-label">ATP</div>
                            <div id="cell-waste" class="cell-label">Waste (Out)</div>
                        </div>
                    </div>
                </div>

                <div id="body-diagram-container" class="diagram-container">
                    <div class="diagram-title">Human Body Diagram</div>
                    <div class="body-diagram">
                         <div class="body-representation">
                            <div id="body-cardiovascular" class="organ-system">Cardiovascular System</div>
                            <div id="body-respiratory" class="organ-system">Respiratory System</div>
                            <div id="body-urinary" class="organ-system">Urinary System</div>
                            <div id="body-endocrine" class="organ-system">Endocrine System</div>
                            <div id="body-nervous" class="organ-system">Nervous System (Control)</div>
                         </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="description-display" class="description-area">
            <h4>Concept Description</h4>
            <p>Drag a concept onto a diagram to see its description here.</p>
        </div>

        <div class="quiz-section">
            <h2>Check My Understanding</h2>
            <div id="quiz-content">
                <div id="quiz-question-text" class="quiz-question"></div>
                <div id="quiz-options" class="quiz-options"></div>
                <div id="quiz-feedback" class="quiz-feedback" style="display: none;"></div>
                <button id="next-question-btn" style="display: none;">Next Question</button>
            </div>
            <div id="quiz-score" style="display: none; text-align: center; font-weight: bold; margin-top: 15px;"></div>
        </div>
    </div>

    <script>
        const conceptsData = [
            {
                id: "stimulus",
                name: "Stimulus",
                description: "A detectable change in the internal or external environment that can trigger a response.",
                cell_explanation: "<strong>Stimulus (Cellular):</strong> A change that affects the cell. Examples: variation in nutrient levels (like glucose), oxygen concentration, temperature, pH, or presence of signaling molecules (hormones, neurotransmitters). These stimuli can originate outside or inside the cell.",
                cell_highlights: ["cell-glucose", "cell-oxygen"],
                body_explanation: "<strong>Stimulus (Body):</strong> A change detected by the body. Examples: altered blood sugar levels, a shift in body temperature, changes in blood pressure, light, sound, or touch. These prompt the body to react to maintain balance.",
                body_highlights: ["body-nervous", "body-endocrine"], // Sensory inputs often processed by nervous/endocrine
                explored: false
            },
            {
                id: "receptor",
                name: "Receptor",
                description: "A structure (often a protein) that detects a stimulus. Receptors can be on cell surfaces or within cells, or part of sensory organs.",
                cell_explanation: "<strong>Receptor (Cellular):</strong> Specialized molecules, usually proteins, on the cell membrane or inside the cell. They bind to specific stimuli (e.g., hormones, glucose) and initiate a signal transduction pathway, informing the cell about environmental changes.",
                cell_highlights: ["cell-receptors", "cell-membrane"],
                body_explanation: "<strong>Receptor (Body):</strong> Sensory cells or organs that detect stimuli. Examples: thermoreceptors in skin detect temperature, chemoreceptors detect blood glucose, photoreceptors in eyes detect light. They convert stimuli into nerve impulses.",
                body_highlights: ["body-nervous"], // Sensory organs are part of nervous system
                explored: false
            },
            {
                id: "control-center",
                name: "Control Center",
                description: "Processes information received from receptors and determines the appropriate response. Often the brain, spinal cord, or endocrine glands.",
                cell_explanation: "<strong>Control Center (Cellular):</strong> The nucleus (containing DNA) acts as a primary control center, regulating gene expression and protein synthesis in response to signals. Other organelles also play roles in processing information and coordinating cellular activities.",
                cell_highlights: ["cell-nucleus"],
                body_explanation: "<strong>Control Center (Body):</strong> Typically the brain (e.g., hypothalamus), spinal cord, or specific endocrine glands (e.g., pancreas). It receives input from receptors, compares it to a set point, and sends out commands to effectors if a change is needed.",
                body_highlights: ["body-nervous", "body-endocrine"],
                explored: false
            },
            {
                id: "effector",
                name: "Effector",
                description: "A cell, tissue, or organ that carries out the response dictated by the control center to counteract the stimulus.",
                cell_explanation: "<strong>Effector (Cellular):</strong> Organelles or molecules within the cell that carry out the response. Examples: enzymes that alter metabolic pathways, transport proteins that move substances across the membrane, or components of the cytoskeleton that change cell shape.",
                cell_highlights: ["cell-atp", "cell-membrane"], // ATP for energy, membrane for transport
                body_explanation: "<strong>Effector (Body):</strong> Organs, glands, or muscles that execute the response. Examples: muscles contract or relax, glands secrete hormones, the liver stores or releases glucose. Their actions aim to restore homeostasis.",
                body_highlights: ["body-cardiovascular", "body-urinary", "body-endocrine"], // Systems that carry out responses
                explored: false
            },
            {
                id: "response",
                name: "Response",
                description: "The action taken by the effector to address the change caused by the stimulus, aiming to restore homeostasis.",
                cell_explanation: "<strong>Response (Cellular):</strong> The cell's action to the stimulus. Examples: increasing glucose uptake, producing more ATP, secreting waste products, altering enzyme activity, or undergoing apoptosis (programmed cell death) if damage is too severe.",
                cell_highlights: ["cell-glucose", "cell-waste", "cell-atp"],
                body_explanation: "<strong>Response (Body):</strong> The physiological change resulting from the effector's action. Examples: shivering to generate heat if cold, sweating to cool down if hot, increased heart rate during exercise, release of insulin to lower blood sugar. This helps return the internal environment to its optimal state.",
                body_highlights: ["body-cardiovascular", "body-respiratory", "body-urinary"],
                explored: false
            }
        ];

        const quizQuestions = [
            {
                question: "What is the primary role of a 'Receptor' in a homeostatic feedback loop?",
                options: ["To carry out the response", "To detect a stimulus", "To process information and decide action", "To be the change in environment"],
                correctAnswer: "To detect a stimulus",
                explanation: "Receptors are specialized to sense changes (stimuli) in the internal or external environment."
            },
            {
                question: "Which of these acts as a 'Control Center' in regulating blood glucose levels in the human body?",
                options: ["Muscle cells", "Skin sensors", "Pancreas", "Blood vessels"],
                correctAnswer: "Pancreas",
                explanation: "The pancreas detects blood glucose levels and releases hormones (insulin or glucagon) accordingly, acting as both a receptor and control center."
            },
            {
                question: "A rise in body temperature leads to sweating. In this scenario, sweat glands are acting as:",
                options: ["Stimulus", "Receptor", "Control Center", "Effector"],
                correctAnswer: "Effector",
                explanation: "Sweat glands are effectors that carry out the response (sweating) to cool the body down, as directed by the control center (hypothalamus)."
            },
            {
                question: "At the cellular level, if a cell is low on energy, the increased production of ATP is a type of:",
                options: ["Stimulus", "Receptor", "Response", "Control Center"],
                correctAnswer: "Response",
                explanation: "The production of ATP is the cell's response to the stimulus of low energy, aiming to restore its energy balance."
            },
            {
                question: "The overall goal of homeostasis is to:",
                options: ["Allow the internal environment to change with the external environment", "Maintain a relatively stable internal environment", "Stop all changes in the body", "Only respond to external stimuli"],
                correctAnswer: "Maintain a relatively stable internal environment",
                explanation: "Homeostasis is the dynamic process of maintaining internal conditions within a narrow, stable range despite external fluctuations."
            }
        ];

        let currentDraggedConcept = null;
        let currentQuestionIndex = 0;
        let score = 0;

        // Populate concepts list
        const conceptsListDiv = document.getElementById('concepts-list');
        conceptsData.forEach(concept => {
            const item = document.createElement('div');
            item.id = `concept-${concept.id}`;
            item.className = 'concept-item';
            item.draggable = true;
            item.textContent = concept.name;
            
            const feedbackIcon = document.createElement('span');
            feedbackIcon.className = 'feedback-icon';
            item.appendChild(feedbackIcon);

            item.addEventListener('dragstart', (e) => {
                currentDraggedConcept = concept;
                e.dataTransfer.setData('text/plain', concept.id);
            });
            conceptsListDiv.appendChild(item);
        });

        // Setup droppable areas
        const cellDiagramContainer = document.getElementById('cell-diagram-container');
        const bodyDiagramContainer = document.getElementById('body-diagram-container');
        const descriptionDisplay = document.getElementById('description-display');

        [cellDiagramContainer, bodyDiagramContainer].forEach(container => {
            container.addEventListener('dragover', (e) => {
                e.preventDefault();
                container.classList.add('drag-over');
            });
            container.addEventListener('dragleave', () => {
                container.classList.remove('drag-over');
            });
            container.addEventListener('drop', (e) => {
                e.preventDefault();
                container.classList.remove('drag-over');
                if (currentDraggedConcept) {
                    handleDrop(currentDraggedConcept, container.id.includes('cell') ? 'cell' : 'body');
                    currentDraggedConcept = null;
                }
            });
        });
        
        let highlightedElements = [];

        function clearHighlights() {
            highlightedElements.forEach(elId => {
                const el = document.getElementById(elId);
                if (el) el.classList.remove('highlight');
            });
            highlightedElements = [];
        }

        function handleDrop(concept, diagramType) {
            clearHighlights(); // Clear previous highlights

            let explanation = '';
            let highlights = [];

            if (diagramType === 'cell') {
                explanation = concept.cell_explanation;
                highlights = concept.cell_highlights || [];
            } else { // body
                explanation = concept.body_explanation;
                highlights = concept.body_highlights || [];
            }

            descriptionDisplay.innerHTML = `<h4>${concept.name} (${diagramType === 'cell' ? 'Cellular' : 'Body'} Context)</h4><p>${explanation}</p>`;
            
            // Animate: Highlight relevant parts
            highlights.forEach(elId => {
                const el = document.getElementById(elId);
                if (el) {
                    el.classList.add('highlight');
                    highlightedElements.push(elId);
                }
            });

            // Mark concept as explored and update UI
            const conceptListItem = document.getElementById(`concept-${concept.id}`);
            if (conceptListItem && !concept.explored) {
                concept.explored = true; // For simplicity, one interaction is enough for checkmark
                const icon = conceptListItem.querySelector('.feedback-icon');
                if(icon) icon.textContent = ' ✔';
                conceptListItem.classList.add('explored');
            }
        }

        // Quiz Logic
        const quizQuestionText = document.getElementById('quiz-question-text');
        const quizOptionsDiv = document.getElementById('quiz-options');
        const quizFeedbackDiv = document.getElementById('quiz-feedback');
        const nextQuestionBtn = document.getElementById('next-question-btn');
        const quizScoreDiv = document.getElementById('quiz-score');

        function displayQuestion() {
            if (currentQuestionIndex < quizQuestions.length) {
                const q = quizQuestions[currentQuestionIndex];
                quizQuestionText.textContent = q.question;
                quizOptionsDiv.innerHTML = '';
                q.options.forEach(option => {
                    const button = document.createElement('button');
                    button.textContent = option;
                    button.onclick = () => handleAnswer(option, q.correctAnswer, q.explanation);
                    quizOptionsDiv.appendChild(button);
                });
                quizFeedbackDiv.style.display = 'none';
                nextQuestionBtn.style.display = 'none';
                quizOptionsDiv.style.pointerEvents = 'auto'; // Re-enable options
            } else {
                // End of quiz
                quizQuestionText.textContent = "Quiz Complete!";
                quizOptionsDiv.innerHTML = '';
                quizFeedbackDiv.style.display = 'none';
                nextQuestionBtn.style.display = 'none';
                quizScoreDiv.textContent = `Your final score: ${score} out of ${quizQuestions.length}`;
                quizScoreDiv.style.display = 'block';
            }
        }

        function handleAnswer(selectedAnswer, correctAnswer, explanation) {
            quizOptionsDiv.style.pointerEvents = 'none'; // Disable options after answering
            quizFeedbackDiv.style.display = 'block';
            if (selectedAnswer === correctAnswer) {
                quizFeedbackDiv.textContent = "Correct! " + explanation;
                quizFeedbackDiv.className = 'quiz-feedback correct';
                score++;
            } else {
                quizFeedbackDiv.textContent = `Incorrect. The correct answer is ${correctAnswer}. ${explanation}`;
                quizFeedbackDiv.className = 'quiz-feedback incorrect';
            }
            nextQuestionBtn.style.display = 'inline-block';
            if (currentQuestionIndex >= quizQuestions.length -1) {
                nextQuestionBtn.textContent = "Show Final Score";
            }
        }

        nextQuestionBtn.addEventListener('click', () => {
            currentQuestionIndex++;
            displayQuestion();
        });

        // Initial call
        displayQuestion();

    </script>
</body>
</html>
