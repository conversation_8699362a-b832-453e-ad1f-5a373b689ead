
import React from 'react';
import InteractiveHotspotImage from '../../common/InteractiveHotspotImage';
import type { Hotspot } from '../../../types';

interface InteractiveCellModelProps {
  imageUrl: string;
  altText: string;
  hotspots: Hotspot[];
}

const InteractiveCellModel: React.FC<InteractiveCellModelProps> = ({ imageUrl, altText, hotspots }) => {
  return (
    <div className="my-8 p-6 bg-gradient-to-br from-secondary/10 to-accent/10 rounded-xl shadow-lg">
      <h4 className="text-2xl font-semibold text-primary mb-4 text-center">Explore the Animal Cell</h4>
      <p className="text-center text-textlight/80 mb-6">
        Click on the hotspots to learn about different parts of the cell. This is a simplified 2D representation.
        A full 3D model would allow rotation and zooming.
      </p>
      <InteractiveHotspotImage imageUrl={imageUrl} altText={altText} hotspots={hotspots} />
    </div>
  );
};

export default InteractiveCellModel;
