<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Human Body Organ Quiz</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f0f0;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        .app-container {
            width: 100%;
            max-width: 800px;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 20px;
            border-radius: 8px;
            box-sizing: border-box;
        }

        h1 {
            text-align: center;
            color: #2c3e50;
        }

        .body-diagram-container {
            position: relative;
            width: 100%;
            max-width: 400px; /* Adjust as needed */
            height: 500px; /* Adjust as needed, or use padding-bottom for aspect ratio */
            margin: 20px auto;
            border: 2px solid #ccc;
            background-color: #e9f7fd; /* Light blue, like a medical diagram background */
            border-radius: 5px;
            /* background-image: url('path_to_body_outline_if_you_had_one.svg');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center; */
        }

        .organ {
            position: absolute;
            background-color: rgba(255, 165, 0, 0.7); /* Orange, semi-transparent */
            border: 1px solid #e67e22; /* Darker orange */
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-size: 0.7em; /* Smaller font for organ names */
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            padding: 2px;
            box-sizing: border-box;
            color: #000;
        }

        .organ:hover {
            background-color: rgba(255, 140, 0, 0.9); /* Brighter orange on hover */
            transform: scale(1.05);
        }
        
        .organ.selected-organ-visual {
            border: 2px solid #0000FF; /* Blue border for the currently questioned organ */
            box-shadow: 0 0 10px #0000FF;
        }

        /* Organ positioning (approximate) */
        #brain { top: 3%; left: 35%; width: 30%; height: 12%; background-color: #ffcccb; }
        #lungs { top: 18%; left: 20%; width: 60%; height: 20%; background-color: #add8e6; }
        #heart { top: 25%; left: 40%; width: 20%; height: 10%; background-color: #ff69b4; z-index: 1; }
        #liver { top: 38%; left: 25%; width: 50%; height: 15%; background-color: #8b4513; }
        #stomach { top: 40%; left: 30%; width: 25%; height: 10%; background-color: #f0e68c; z-index: 1;}
        #spleen { top: 40%; left: 15%; width: 15%; height: 8%; background-color: #9370db; }
        #pancreas { top: 48%; left: 35%; width: 30%; height: 7%; background-color: #ffe4b5; z-index: 0;}
        #gallbladder { top: 45%; left: 50%; width: 10%; height: 5%; background-color: #3cb371; z-index: 2;}
        #kidneys { top: 53%; left: 20%; width: 60%; height: 12%; background-color: #c0c0c0; } /* Represents both */
        #small-intestine { top: 65%; left: 25%; width: 50%; height: 15%; background-color: #ffdab9; }
        #large-intestine { top: 63%; left: 20%; width: 60%; height: 20%; background-color: #d2b48c; z-index: -1; } /* Wraps around small */
        #bladder { top: 85%; left: 35%; width: 30%; height: 10%; background-color: #ffff00; }

        .quiz-area {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border: 1px solid #ddd;
            min-height: 150px; /* Ensure space for question and answers */
        }

        #question-text {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #34495e;
        }

        #answer-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .answer-btn {
            padding: 10px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-align: left;
            transition: background-color 0.3s;
        }

        .answer-btn:hover:not(:disabled) {
            background-color: #2980b9;
        }

        .answer-btn:disabled {
            cursor: not-allowed;
            opacity: 0.7;
        }

        .answer-btn.correct {
            background-color: #2ecc71 !important; /* Green */
            color: white !important;
        }

        .answer-btn.incorrect {
            background-color: #e74c3c !important; /* Red */
            color: white !important;
        }

        #feedback-text {
            margin-top: 15px;
            font-size: 1.1em;
            font-weight: bold;
            min-height: 1.2em; /* Reserve space */
        }

        .feedback-correct {
            color: #2ecc71; /* Green */
        }

        .feedback-incorrect {
            color: #e74c3c; /* Red */
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            .app-container {
                margin: 10px;
                padding: 15px;
            }
            h1 {
                font-size: 1.5em;
            }
            .body-diagram-container {
                height: 400px; /* Adjust height for smaller screens */
                max-width: 90%;
            }
            .organ {
                font-size: 0.6em; /* Even smaller font on small screens */
            }
            #question-text {
                font-size: 1em;
            }
            .answer-btn {
                padding: 8px 12px;
                font-size: 0.9em;
            }
            #feedback-text {
                font-size: 1em;
            }
        }
        @media (max-width: 400px) {
            .body-diagram-container {
                height: 350px;
            }
             .organ {
                /* Potentially adjust positions or sizes more drastically if needed */
                /* For simplicity, we rely on percentage scaling primarily */
                font-size: 0.5em; /* May become too small, consider removing text or using tooltips */
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <h1>Human Body Organ Quiz</h1>

        <div class="body-diagram-container">
            <div class="organ" id="brain" data-organ-name="Brain">Brain</div>
            <div class="organ" id="lungs" data-organ-name="Lungs">Lungs</div>
            <div class="organ" id="heart" data-organ-name="Heart">Heart</div>
            <div class="organ" id="liver" data-organ-name="Liver">Liver</div>
            <div class="organ" id="stomach" data-organ-name="Stomach">Stomach</div>
            <div class="organ" id="spleen" data-organ-name="Spleen">Spleen</div>
            <div class="organ" id="pancreas" data-organ-name="Pancreas">Pancreas</div>
            <div class="organ" id="gallbladder" data-organ-name="Gallbladder">Gallbladder</div>
            <div class="organ" id="kidneys" data-organ-name="Kidneys">Kidneys</div>
            <div class="organ" id="small-intestine" data-organ-name="Small Intestine">Small Int.</div>
            <div class="organ" id="large-intestine" data-organ-name="Large Intestine">Large Int.</div>
            <div class="organ" id="bladder" data-organ-name="Bladder">Bladder</div>
        </div>

        <div class="quiz-area" id="quiz-section" style="display: none;">
            <p id="question-text"></p>
            <div id="answer-options"></div>
            <p id="feedback-text"></p>
        </div>
    </div>

    <script>
        const organData = {
            Brain: {
                question: "Which of these is a primary function of the Brain?",
                answers: [
                    { text: "Controls thoughts, memory, and voluntary actions", correct: true },
                    { text: "Pumps blood throughout the body", correct: false },
                    { text: "Filters waste from blood", correct: false },
                    { text: "Absorbs nutrients from food", correct: false }
                ]
            },
            Lungs: {
                question: "What is the primary function of the Lungs?",
                answers: [
                    { text: "Facilitate the exchange of oxygen and carbon dioxide", correct: true },
                    { text: "Produce digestive enzymes", correct: false },
                    { text: "Store bile", correct: false },
                    { text: "Regulate body temperature", correct: false }
                ]
            },
            Heart: {
                question: "Which statement best describes the Heart's main role?",
                answers: [
                    { text: "Pumps blood to all parts of the body", correct: true },
                    { text: "Produces hormones for growth", correct: false },
                    { text: "Breaks down carbohydrates", correct: false },
                    { text: "Stores urine before elimination", correct: false }
                ]
            },
            Spleen: {
                question: "What is a key function of the Spleen?",
                answers: [
                    { text: "Filters blood and removes old red blood cells", correct: true },
                    { text: "Secretes insulin", correct: false },
                    { text: "Absorbs water from undigested food", correct: false },
                    { text: "Controls metabolism", correct: false }
                ]
            },
            Stomach: {
                question: "What is the primary function of the Stomach?",
                answers: [
                    { text: "Mixes food with digestive juices to break it down", correct: true },
                    { text: "Produces red blood cells", correct: false },
                    { text: "Regulates blood sugar levels", correct: false },
                    { text: "Filters toxins from the body", correct: false }
                ]
            },
            Pancreas: {
                question: "The Pancreas has several functions, including:",
                answers: [
                    { text: "Producing digestive enzymes and hormones like insulin", correct: true },
                    { text: "Storing and concentrating bile", correct: false },
                    { text: "Filtering air before it reaches the lungs", correct: false },
                    { text: "Controlling muscle movement", correct: false }
                ]
            },
            Liver: {
                question: "Which of these is a major function of the Liver?",
                answers: [
                    { text: "Detoxifies chemicals and metabolizes drugs", correct: true },
                    { text: "Exchanges gases between air and blood", correct: false },
                    { text: "Transmits nerve impulses", correct: false },
                    { text: "Produces antibodies", correct: false },
                    { text: "Secretes bile for digestion", correct: true } // Example of two correct, but for quiz we need one. Let's pick one primary.
                                                                      // Re-evaluating: Liver has many primary functions. Let's choose a distinct one.
                                                                      // The prompt said "primary function". Detoxification is a good one. Bile production is also key.
                                                                      // Let's refine to have one clearly "best" answer for a simple quiz.
                                                                      // "Detoxifies chemicals, metabolizes drugs, and makes bile" - too long for an option.
                                                                      // Changed the options to be more distinct.
                ],
                // Corrected Liver options:
                answers: [ // Overwriting previous liver answers for clarity
                    { text: "Produces bile and detoxifies substances", correct: true },
                    { text: "Coordinates balance and movement", correct: false },
                    { text: "Absorbs oxygen from the air", correct: false },
                    { text: "Controls the body's water balance", correct: false }
                ]
            },
            Gallbladder: {
                question: "What is the main role of the Gallbladder?",
                answers: [
                    { text: "Stores and concentrates bile produced by the liver", correct: true },
                    { text: "Digests proteins", correct: false },
                    { text: "Produces adrenaline", correct: false },
                    { text: "Regulates sleep cycles", correct: false }
                ]
            },
            Kidneys: {
                question: "What is the primary function of the Kidneys?",
                answers: [
                    { text: "Filter waste products from the blood and produce urine", correct: true },
                    { text: "Pump oxygenated blood to the brain", correct: false },
                    { text: "Store food before digestion", correct: false },
                    { text: "Produce growth hormones", correct: false }
                ]
            },
            "Small Intestine": {
                question: "The Small Intestine is primarily responsible for:",
                answers: [
                    { text: "Absorbing nutrients from digested food", correct: true },
                    { text: "Producing saliva", correct: false },
                    { text: "Storing feces", correct: false },
                    { text: "Filtering air particles", correct: false }
                ]
            },
            "Large Intestine": {
                question: "What is a key function of the Large Intestine?",
                answers: [
                    { text: "Absorbing water from undigested food and forming feces", correct: true },
                    { text: "Breaking down fats", correct: false },
                    { text: "Regulating heart rate", correct: false },
                    { text: "Producing vitamin D", correct: false }
                ]
            },
            Bladder: {
                question: "What is the main function of the Bladder?",
                answers: [
                    { text: "Stores urine before it is eliminated from the body", correct: true },
                    { text: "Secretes digestive enzymes into the small intestine", correct: false },
                    { text: "Controls body temperature", correct: false },
                    { text: "Produces sound for speech", correct: false }
                ]
            }
        };

        const positiveAffirmations = ['Correct!', 'Great Job!', 'Excellent!', 'Well Done!', 'Awesome!'];
        const negativeFeedback = ['Not quite!', 'Oops, that wasn\'t it.', 'Better luck next time!', 'Incorrect.'];

        const organElements = document.querySelectorAll('.organ');
        const quizSection = document.getElementById('quiz-section');
        const questionTextElement = document.getElementById('question-text');
        const answerOptionsElement = document.getElementById('answer-options');
        const feedbackTextElement = document.getElementById('feedback-text');
        
        let currentOrganName = null;
        let currentSelectedOrganElement = null;

        organElements.forEach(organEl => {
            organEl.addEventListener('click', () => {
                const organName = organEl.dataset.organName;
                currentOrganName = organName;

                if (currentSelectedOrganElement) {
                    currentSelectedOrganElement.classList.remove('selected-organ-visual');
                }
                currentSelectedOrganElement = organEl;
                organEl.classList.add('selected-organ-visual');
                
                displayQuestion(organName);
            });
        });

        function displayQuestion(organName) {
            const data = organData[organName];
            if (!data) {
                console.error("No data found for organ:", organName);
                return;
            }

            quizSection.style.display = 'block';
            questionTextElement.textContent = data.question;
            answerOptionsElement.innerHTML = ''; // Clear previous options
            feedbackTextElement.textContent = '';
            feedbackTextElement.className = ''; // Clear feedback styling

            // Shuffle answers for variety (optional, but good practice)
            const shuffledAnswers = [...data.answers].sort(() => Math.random() - 0.5);

            shuffledAnswers.forEach(answer => {
                const button = document.createElement('button');
                button.classList.add('answer-btn');
                button.textContent = answer.text;
                button.addEventListener('click', () => checkAnswer(answer, data.answers, button));
                answerOptionsElement.appendChild(button);
            });
        }

        function checkAnswer(selectedAnswer, allAnswers, button) {
            // Disable all answer buttons
            const buttons = answerOptionsElement.querySelectorAll('.answer-btn');
            buttons.forEach(btn => btn.disabled = true);

            if (selectedAnswer.correct) {
                button.classList.add('correct');
                feedbackTextElement.textContent = positiveAffirmations[Math.floor(Math.random() * positiveAffirmations.length)];
                feedbackTextElement.className = 'feedback-correct';
                // User can now select a new organ as per requirement 6
            } else {
                button.classList.add('incorrect');
                feedbackTextElement.textContent = negativeFeedback[Math.floor(Math.random() * negativeFeedback.length)];
                feedbackTextElement.className = 'feedback-incorrect';

                // Highlight the correct answer
                const correctButton = Array.from(buttons).find(btn => {
                    // Find the original answer object that corresponds to this button's text
                    const originalAnswer = allAnswers.find(ans => ans.text === btn.textContent);
                    return originalAnswer && originalAnswer.correct;
                });
                if (correctButton) {
                    correctButton.classList.add('correct');
                }
            }
        }

        // Initialize: Make sure organ names in HTML match keys in organData
        // Small Int. and Large Int. in HTML need to match "Small Intestine" and "Large Intestine" in JS keys
        // Corrected this by using data-organ-name attribute.
    </script>
</body>
</html>
