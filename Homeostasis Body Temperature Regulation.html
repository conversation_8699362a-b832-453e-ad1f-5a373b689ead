<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Homeostasis Demo</title>
    <style>
        :root {
            --color-primary: #3498db;
            --color-secondary: #2ecc71;
            --color-accent: #e74c3c;
            --color-background: #f4f6f7;
            --color-text: #333;
            --color-border: #ddd;
            --color-idle: #ecf0f1;
            --color-active-hot: #f5b7b1; /* Light red */
            --color-active-cold: #a9cce3; /* Light blue */
            --color-processing: #f9e79f; /* Light yellow */
            --color-correcting: #a3e4d7; /* Light green */
            --color-failed: #cacfd2; /* Grey */
            --color-arrow-inactive: #ccc;
            --color-arrow-active: #333;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--color-background);
            color: var(--color-text);
            display: flex;
            justify-content: center;
        }

        .app-container {
            max-width: 900px;
            width: 100%;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }

        h1, h2 {
            color: var(--color-primary);
            text-align: center;
        }
        h2 {
            color: var(--color-secondary);
            margin-top: 20px;
            border-bottom: 1px solid var(--color-border);
            padding-bottom: 5px;
        }

        .system-variable {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e8f6fd;
            border-radius: 6px;
        }
        .system-variable p {
            margin: 5px 0;
            font-size: 1.1em;
        }
        .system-variable input[type="range"] {
            width: 80%;
            max-width: 400px;
            margin-top: 10px;
        }
        #currentValueDisplay {
            font-weight: bold;
        }
        #stimulusInfo {
            font-style: italic;
            color: #555;
            font-size: 0.9em;
            min-height: 1.2em; /* Reserve space to prevent layout shifts */
        }

        .homeostasis-diagram {
            display: flex;
            align-items: stretch; /* Make components same height */
            justify-content: space-around;
            margin-bottom: 30px;
            padding: 10px;
            flex-wrap: wrap; /* Allow wrapping on smaller screens before full column */
        }

        .component {
            border: 2px solid var(--color-border);
            background-color: var(--color-idle);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            min-width: 180px; /* Minimum width for components */
            flex: 1; /* Allow components to grow */
            margin: 10px;
            transition: background-color 0.3s ease, border-color 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .component h3 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 1.2em;
            color: var(--color-text);
        }
        .component .subtitle {
            font-size: 0.8em;
            color: #666;
            margin-bottom: 10px;
        }
        .component .status {
            font-weight: bold;
            min-height: 1.2em; /* Reserve space */
            margin-bottom: 5px;
        }
        .component .action {
            font-style: italic;
            color: #555;
            min-height: 2.4em; /* Reserve space for two lines */
            font-size: 0.9em;
        }

        .arrow-horizontal {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5em;
            color: var(--color-arrow-inactive);
            transition: color 0.3s ease;
            padding: 0 10px; /* Space around horizontal arrows */
        }
        .arrow-vertical {
            font-size: 0.9em;
            color: var(--color-arrow-inactive);
            margin-top: auto; /* Push to bottom if component grows */
            padding-top: 10px;
            transition: color 0.3s ease;
        }
        .arrow-horizontal.active, .arrow-vertical.active {
            color: var(--color-arrow-active);
            font-weight: bold;
        }
        
        /* Specific component states */
        .component.active-hot { background-color: var(--color-active-hot); border-color: #c0392b; }
        .component.active-cold { background-color: var(--color-active-cold); border-color: #2980b9; }
        .component.processing { background-color: var(--color-processing); border-color: #f39c12; }
        .component.correcting { background-color: var(--color-correcting); border-color: #27ae60; }
        .component.failed { background-color: var(--color-failed); border-color: #7f8c8d; color: #555; }
        .component.failed .status, .component.failed .subtitle { color: #555; }


        .feedback-info, .imbalance-section {
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 6px;
            margin-top: 20px;
        }
        .feedback-info p, .imbalance-section p {
            margin-bottom: 10px;
        }
        
        button {
            background-color: var(--color-primary);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin-right: 10px;
            transition: background-color 0.2s ease;
        }
        button:hover {
            background-color: #2980b9;
        }
        button#toggleFailureButton.failed-active {
            background-color: var(--color-accent);
        }
        button#toggleFailureButton.failed-active:hover {
            background-color: #c0392b;
        }


        #imbalanceMessage {
            color: var(--color-accent);
            font-weight: bold;
            margin-top: 10px;
        }

        /* Responsive adjustments */
        @media (max-width: 800px) {
            .homeostasis-diagram {
                flex-direction: column;
                align-items: center; /* Center items when stacked */
            }
            .component {
                width: 80%; /* Make components wider when stacked */
                max-width: 400px; /* But not too wide */
                min-width: unset; /* Remove min-width */
                margin: 10px 0; /* Adjust margin for vertical stacking */
            }
            .arrow-horizontal {
                transform: rotate(90deg); /* Point down */
                margin: 15px 0; /* Adjust margin for vertical flow */
                padding: 10px 0;
            }
        }
        @media (max-width: 480px) {
            body { padding: 10px; }
            .app-container { padding: 15px; }
            h1 { font-size: 1.5em; }
            h2 { font-size: 1.2em; }
            .component { width: 95%; }
            button { 
                width: 100%;
                margin-bottom: 10px;
            }
            button:last-child { margin-bottom: 0; }
        }

    </style>
</head>
<body>
    <div class="app-container">
        <h1>Homeostasis: Body Temperature Regulation</h1>
        
        <div class="system-variable">
            <p>Ideal Body Temperature: <span id="idealValueDisplay">37.0</span> °C</p>
            <p>Current Body Temperature: <span id="currentValueDisplay">37.0</span> °C</p>
            <input type="range" id="tempSlider" min="30" max="44" value="37.0" step="0.1">
            <p id="stimulusInfo">Body temperature is normal.</p>
        </div>

        <div class="homeostasis-diagram">
            <div class="component receptor">
                <h3>Receptor</h3>
                <p class="subtitle">(e.g., Thermoreceptors)</p>
                <div class="arrow-vertical" id="arrow-stimulus-receptor">⬇️ Senses Current Temp.</div>
                <p class="status" id="receptorStatus">Idle</p>
            </div>
            
            <div class="arrow-horizontal" id="arrow-receptor-cc">➡️</div>

            <div class="component control-center">
                <h3>Control Center</h3>
                <p class="subtitle">(e.g., Hypothalamus)</p>
                <p class="status" id="ccStatus">Idle</p>
            </div>

            <div class="arrow-horizontal" id="arrow-cc-effector">➡️</div>

            <div class="component effector">
                <h3>Effector</h3>
                <p class="subtitle">(e.g., Sweat Glands, Muscles)</p>
                <p class="status" id="effectorStatus">Idle</p>
                <p class="action" id="effectorAction"></p>
                <div class="arrow-vertical" id="arrow-effector-response">⬆️ Acts to Change Temp.</div>
            </div>
        </div>
        
        <div class="feedback-info">
            <h2>Feedback Mechanism: <span id="feedbackType">Negative Feedback</span></h2>
            <p id="feedbackExplanation">
                This system uses <strong>negative feedback</strong>. When a change (stimulus) moves the body temperature away from the ideal set point, the system initiates responses that counteract this change, bringing the temperature back towards equilibrium.
            </p>
            <p id="positiveFeedbackInfo">
                <strong>Positive feedback</strong> systems, in contrast, amplify the initial change, moving the system further from equilibrium. Examples include blood clotting or uterine contractions during childbirth. (This demo focuses on negative feedback for body temperature regulation).
            </p>
        </div>

        <div class="imbalance-section">
            <h2>Homeostatic Imbalance</h2>
            <button id="toggleFailureButton">Simulate System Failure</button>
            <button id="resetSimulationButton">Reset Simulation</button>
            <p id="imbalanceMessage" style="display:none;"></p>
        </div>

    </div>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // DOM Elements
            const idealValueDisplay = document.getElementById('idealValueDisplay');
            const currentValueDisplay = document.getElementById('currentValueDisplay');
            const tempSlider = document.getElementById('tempSlider');
            const stimulusInfo = document.getElementById('stimulusInfo');

            const receptorComponent = document.querySelector('.receptor');
            const receptorStatus = document.getElementById('receptorStatus');
            const ccComponent = document.querySelector('.control-center');
            const ccStatus = document.getElementById('ccStatus');
            const effectorComponent = document.querySelector('.effector');
            const effectorStatus = document.getElementById('effectorStatus');
            const effectorAction = document.getElementById('effectorAction');

            const arrowStimulusReceptor = document.getElementById('arrow-stimulus-receptor');
            const arrowReceptorCc = document.getElementById('arrow-receptor-cc');
            const arrowCcEffector = document.getElementById('arrow-cc-effector');
            const arrowEffectorResponse = document.getElementById('arrow-effector-response');

            const toggleFailureButton = document.getElementById('toggleFailureButton');
            const resetSimulationButton = document.getElementById('resetSimulationButton');
            const imbalanceMessage = document.getElementById('imbalanceMessage');

            // Constants
            const IDEAL_TEMP = 37.0;
            const ACTIVATION_OFFSET = 0.5; // Trigger response if |current - ideal| > this
            const CORRECTION_TOLERANCE = 0.05; // Stop correction when |current - ideal| < this
            const CORRECTION_RATE = 0.1; // Temp change per interval
            const UPDATE_INTERVAL = 200; // ms

            // State Variables
            let currentTemp = IDEAL_TEMP;
            let regulationIntervalId = null;
            let systemFailed = false;

            // Initialize Display
            idealValueDisplay.textContent = IDEAL_TEMP.toFixed(1);
            currentValueDisplay.textContent = currentTemp.toFixed(1);
            tempSlider.value = currentTemp;

            // Event Listeners
            tempSlider.addEventListener('input', () => {
                currentTemp = parseFloat(tempSlider.value);
                currentValueDisplay.textContent = currentTemp.toFixed(1);
                if (!systemFailed) {
                     // If user manually adjusts, stop automatic regulation to allow new set point to be evaluated
                    if (regulationIntervalId) {
                        clearInterval(regulationIntervalId);
                        regulationIntervalId = null;
                    }
                   checkHomeostasis(); // Re-evaluate immediately
                } else {
                    updateVisuals(); // Just update visuals if system is failed
                }
            });

            toggleFailureButton.addEventListener('click', () => {
                systemFailed = !systemFailed;
                if (systemFailed) {
                    toggleFailureButton.textContent = 'Restore System Function';
                    toggleFailureButton.classList.add('failed-active');
                    imbalanceMessage.style.display = 'block';
                    if (regulationIntervalId) {
                        clearInterval(regulationIntervalId);
                        regulationIntervalId = null;
                    }
                } else {
                    toggleFailureButton.textContent = 'Simulate System Failure';
                    toggleFailureButton.classList.remove('failed-active');
                    imbalanceMessage.style.display = 'none';
                }
                checkHomeostasis(); // Update visuals and potentially restart regulation
            });

            resetSimulationButton.addEventListener('click', () => {
                systemFailed = false;
                toggleFailureButton.textContent = 'Simulate System Failure';
                toggleFailureButton.classList.remove('failed-active');
                imbalanceMessage.style.display = 'none';
                
                currentTemp = IDEAL_TEMP;
                tempSlider.value = currentTemp;
                currentValueDisplay.textContent = currentTemp.toFixed(1);
                
                if (regulationIntervalId) {
                    clearInterval(regulationIntervalId);
                    regulationIntervalId = null;
                }
                checkHomeostasis();
            });

            function checkHomeostasis() {
                updateVisuals();

                if (systemFailed) {
                    return; // No regulation if system has failed
                }

                const deviation = currentTemp - IDEAL_TEMP;

                if (Math.abs(deviation) > ACTIVATION_OFFSET) {
                    // Temperature is significantly off, needs correction
                    if (!regulationIntervalId) { // Start regulation if not already running
                        regulationIntervalId = setInterval(regulateTemperature, UPDATE_INTERVAL);
                    }
                } else {
                    // Temperature is within acceptable range or brought back by user
                    if (regulationIntervalId) {
                        clearInterval(regulationIntervalId);
                        regulationIntervalId = null;
                    }
                     // If very close to ideal after manual adjustment or minor fluctuation, snap to ideal.
                    if (Math.abs(deviation) < CORRECTION_TOLERANCE && Math.abs(deviation) > 0) {
                        currentTemp = IDEAL_TEMP;
                        tempSlider.value = currentTemp.toFixed(1);
                        currentValueDisplay.textContent = currentTemp.toFixed(1);
                        updateVisuals(); // Final update for snapped value
                    }
                }
            }
            
            function regulateTemperature() {
                if (systemFailed) {
                    clearInterval(regulationIntervalId);
                    regulationIntervalId = null;
                    updateVisuals();
                    return;
                }

                const deviation = currentTemp - IDEAL_TEMP;

                if (Math.abs(deviation) < CORRECTION_TOLERANCE) {
                    currentTemp = IDEAL_TEMP; // Snap to ideal
                    clearInterval(regulationIntervalId);
                    regulationIntervalId = null;
                } else if (deviation > 0) { // Too hot
                    currentTemp -= CORRECTION_RATE;
                } else { // Too cold
                    currentTemp += CORRECTION_RATE;
                }
                
                currentTemp = Math.max(parseFloat(tempSlider.min), Math.min(parseFloat(tempSlider.max), currentTemp)); // Clamp to slider bounds
                tempSlider.value = currentTemp.toFixed(1);
                currentValueDisplay.textContent = currentTemp.toFixed(1);
                updateVisuals(); // Update visuals during regulation
            }


            function updateVisuals() {
                const deviation = currentTemp - IDEAL_TEMP;
                let receptorActive = false, ccActive = false, effectorActive = false;
                
                // Reset classes
                [receptorComponent, ccComponent, effectorComponent].forEach(el => {
                    el.classList.remove('active-hot', 'active-cold', 'processing', 'correcting', 'failed');
                });
                [arrowStimulusReceptor, arrowReceptorCc, arrowCcEffector, arrowEffectorResponse].forEach(el => {
                    el.classList.remove('active');
                });

                // Stimulus Info
                if (systemFailed) {
                    if (deviation > ACTIVATION_OFFSET) stimulusInfo.textContent = "Body dangerously hot! System failed.";
                    else if (deviation < -ACTIVATION_OFFSET) stimulusInfo.textContent = "Body dangerously cold! System failed.";
                    else stimulusInfo.textContent = "System failed, regulation off.";
                    imbalanceMessage.textContent = `Homeostatic Imbalance: Temperature at ${currentTemp.toFixed(1)}°C. Regulation system is non-functional.`;
                } else {
                    if (deviation > ACTIVATION_OFFSET) stimulusInfo.textContent = "Body temperature is too high!";
                    else if (deviation < -ACTIVATION_OFFSET) stimulusInfo.textContent = "Body temperature is too low!";
                    else if (Math.abs(deviation) > CORRECTION_TOLERANCE) stimulusInfo.textContent = "Body temperature is slightly off, regulating...";
                    else stimulusInfo.textContent = "Body temperature is normal.";
                }


                // Component Logic if System NOT Failed
                if (!systemFailed) {
                    if (Math.abs(deviation) > ACTIVATION_OFFSET) {
                        receptorActive = true;
                        arrowStimulusReceptor.classList.add('active');
                        receptorStatus.textContent = "Deviation Detected!";
                        receptorComponent.classList.add(deviation > 0 ? 'active-hot' : 'active-cold');

                        ccActive = true;
                        arrowReceptorCc.classList.add('active');
                        ccStatus.textContent = "Analyzing Signal...";
                        ccComponent.classList.add('processing');
                        
                        effectorActive = true;
                        arrowCcEffector.classList.add('active');
                        effectorStatus.textContent = "Correcting Imbalance";
                        effectorComponent.classList.add('correcting');
                        arrowEffectorResponse.classList.add('active');
                        if (deviation > 0) {
                            effectorAction.textContent = "Initiating cooling (e.g., sweating, vasodilation)";
                        } else {
                            effectorAction.textContent = "Initiating warming (e.g., shivering, vasoconstriction)";
                        }
                    } else {
                        // Within acceptable range or ideal
                        receptorStatus.textContent = "Idle";
                        ccStatus.textContent = "Idle";
                        effectorStatus.textContent = "Idle";
                        effectorAction.textContent = "";
                    }
                } else { // System IS Failed
                    receptorComponent.classList.add('failed');
                    ccComponent.classList.add('failed');
                    effectorComponent.classList.add('failed');

                    if (Math.abs(deviation) > 0.1) { // Some deviation exists
                        receptorStatus.textContent = "Deviation Detected (System Unresponsive)";
                        arrowStimulusReceptor.classList.add('active'); // Stimulus is still there
                    } else {
                        receptorStatus.textContent = "Idle (System Unresponsive)";
                    }
                    ccStatus.textContent = "System Failure";
                    effectorStatus.textContent = "System Failure";
                    effectorAction.textContent = "Cannot regulate!";
                }
            }

            // Initial call to set up
            checkHomeostasis();
        });
    </script>
</body>
</html>

