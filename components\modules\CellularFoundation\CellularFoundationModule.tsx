
import React from 'react';
import TerminologyGlossary from './TerminologyGlossary';
import InteractiveCellModel from './InteractiveCellModel';
import MembraneTransportSim from './MembraneTransportSim';
import ActionPotentialSim from './ActionPotentialSim';
import Card from '../../common/Card';
import Quiz from '../../common/Quiz';
import { SAMPLE_QUIZ_QUESTIONS, CELL_PARTS } from '../../../constants';
import type { Hotspot } from '../../../types';

interface CellularFoundationModuleProps {
  id: string;
}

const cellModelHotspots: Hotspot[] = CELL_PARTS.map((part, index) => ({
    id: `cellpart-${index}`,
    x: 20 + (index * 30) % 60, // Distribute hotspots somewhat
    y: 30 + (index * 20) % 40,
    title: part.name,
    description: part.description
}));


const CellularFoundationModule: React.FC<CellularFoundationModuleProps> = ({ id }) => {
  const cellQuizQuestions = SAMPLE_QUIZ_QUESTIONS.filter(q => q.id.includes('_cell'));

  return (
    <section id={id} className="py-12 md:py-16 bg-gradient-to-b from-accent/10 to-background/10">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-primary mb-12 text-center">Module 1: The Cellular Foundation – Life's Smallest Engineers</h2>
        
        <div className="grid md:grid-cols-1 gap-8 mb-12">
          <Card title="Basic Terminology Glossary">
            <TerminologyGlossary />
          </Card>
        </div>

        <h3 className="text-3xl font-semibold text-secondary my-10 text-center">Cell Description & Diversity</h3>
        <InteractiveCellModel 
            imageUrl="https://picsum.photos/seed/animalcell/800/600" 
            altText="Generalized Animal Cell Model" 
            hotspots={cellModelHotspots}
        />
        <Card title="Cell Type Showcase" className="my-8">
          <p className="mb-4">The human body is composed of diverse cell types, each specialized for specific functions. Here are a few examples:</p>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {CELL_PARTS.slice(0,3).map(cellPart => ( // Using CELL_PARTS as placeholder for diverse cell types
                <div key={cellPart.name} className="p-4 border border-accent/50 rounded-lg bg-white shadow-sm hover:shadow-md transition-shadow">
                    <img src={cellPart.imageUrl || `https://picsum.photos/seed/${cellPart.name}/150/150`} alt={cellPart.name} className="w-full h-32 object-cover rounded mb-2" />
                    <h4 className="font-semibold text-lg text-primary">{cellPart.name} (Example)</h4>
                    <p className="text-sm text-textlight/80">{cellPart.description}</p>
                </div>
            ))}
          </div>
           <p className="mt-4 text-sm text-gray-600">Note: This section would showcase diverse cell types like neurons, muscle cells, red blood cells with their specific images and functions.</p>
        </Card>
        
        <h3 className="text-3xl font-semibold text-secondary my-10 text-center">The Cell Membrane: Gatekeeper & Communicator</h3>
        <Card title="Animated Video: The Secret of the Cell Membrane (Placeholder)" className="my-8">
            <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">Placeholder for animated video explaining lipid bilayer, proteins, and selective permeability.</p>
            </div>
        </Card>

        <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-12 my-12">
          <Card title="Virtual Simulation: Membrane Transport">
            <MembraneTransportSim />
          </Card>
          <Card title="Virtual Simulation: Generation of Action Potentials">
            <ActionPotentialSim />
          </Card>
        </div>
        <Card title="Animated Video: Ion Channels & Na+/K+ Pump (Placeholder)" className="my-8">
            <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">Placeholder for animated video on ion channels and the Na+/K+ pump.</p>
            </div>
        </Card>

        <Quiz questions={cellQuizQuestions} moduleId="cellular-foundation" />
      </div>
    </section>
  );
};

export default CellularFoundationModule;
