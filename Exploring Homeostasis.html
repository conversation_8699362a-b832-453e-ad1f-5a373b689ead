<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exploring Homeostasis</title>
    <style>
        :root {
            --cell-healthy-color: #87CEFA; /* LightSkyBlue */
            --cell-stress-low-color: #FFFFE0; /* LightYellow */
            --cell-stress-medium-color: #FFD700; /* Gold */
            --cell-stress-high-color: #FFA07A; /* LightSalmon */
            --cell-dead-color: #808080; /* Grey */
            --nucleus-color: #4682B4; /* SteelBlue */
            --bloodstream-color: #FFC0CB; /* Pinkish, like dilute blood plasma */
            --text-color: #333;
            --container-bg: #f0f0f0;
            --control-bg: #ffffff;
            --border-color: #ccc;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--container-bg);
            color: var(--text-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            line-height: 1.6;
        }

        .app-container {
            width: 100%;
            max-width: 1200px;
            padding: 10px;
            box-sizing: border-box;
        }

        header {
            text-align: center;
            margin-bottom: 20px;
        }

        header h1 {
            margin-bottom: 5px;
            color: var(--nucleus-color);
        }

        .main-content {
            display: flex;
            flex-direction: row;
            gap: 20px;
        }

        .simulation-column {
            flex: 3;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: var(--bloodstream-color);
            padding: 20px;
            border-radius: 8px;
            min-height: 350px; /* Ensure space for cell */
        }

        .cell-area {
            position: relative;
            width: 200px; /* Base size, will be scaled */
            height: 200px;
        }

        .cell {
            width: 100%;
            height: 100%;
            background-color: var(--cell-healthy-color);
            border-radius: 50%;
            border: 3px solid var(--nucleus-color); /* Cell membrane */
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: background-color 0.5s ease, transform 0.3s ease;
            box-shadow: 0 0 15px rgba(0,0,0,0.2);
            overflow: hidden; /* Important for death text positioning */
        }

        .nucleus {
            width: 30%;
            height: 30%;
            background-color: var(--nucleus-color);
            border-radius: 50%;
            position: absolute;
            top: 20%; /* Asymmetrically placed */
            left: 50%;
            transform: translateX(-50%);
            transition: background-color 0.5s ease;
        }

        .atp-display {
            position: absolute;
            bottom: 10%;
            font-size: 0.9em;
            font-weight: bold;
            color: #000;
            background-color: rgba(255,255,255,0.5);
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .cell-death-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.8em;
            font-weight: bold;
            color: white;
            background-color: rgba(0,0,0,0.6);
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            display: none; /* Initially hidden */
            z-index: 10;
        }

        .controls-column {
            flex: 2;
            background-color: var(--control-bg);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 0.9em;
        }

        .control-group input[type="range"] {
            width: 100%;
            cursor: pointer;
        }

        #reset-button {
            display: block;
            width: 100%;
            padding: 10px 15px;
            background-color: var(--nucleus-color);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1em;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        #reset-button:hover {
            background-color: #3a6a92;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            .simulation-column {
                min-height: 300px; /* Adjust for smaller screens */
            }
            .cell-area {
                width: 180px;
                height: 180px;
            }
            .controls-column {
                flex-basis: auto; /* Allow it to take natural height */
            }
        }
        @media (max-width: 480px) {
            .cell-area {
                width: 150px;
                height: 150px;
            }
            .atp-display {
                font-size: 0.8em;
            }
            .cell-death-text {
                font-size: 1.5em;
            }
            header h1 {
                font-size: 1.8em;
            }
            header p {
                font-size: 0.9em;
            }
        }

    </style>
</head>
<body>
    <div class="app-container">
        <header>
            <h1>Exploring Homeostasis</h1>
            <p>Adjust external conditions to keep the cell alive!</p>
        </header>

        <div class="main-content">
            <div class="simulation-column">
                <div class="cell-area">
                    <div id="cell" class="cell">
                        <div id="nucleus" class="nucleus"></div>
                        <div id="atp-display" class="atp-display">ATP: <span id="atp-value-in-cell">70</span>%</div>
                        <div id="cell-death-text" class="cell-death-text">DEAD</div>
                    </div>
                </div>
            </div>

            <div class="controls-column">
                <div class="control-group">
                    <label for="glucose-slider">Glucose (Outside): <span id="glucose-value">50</span></label>
                    <input type="range" id="glucose-slider" min="0" max="100" value="50">
                </div>
                <div class="control-group">
                    <label for="oxygen-slider">Oxygen (Outside): <span id="oxygen-value">60</span></label>
                    <input type="range" id="oxygen-slider" min="0" max="100" value="60">
                </div>
                <div class="control-group">
                    <label for="toxin-slider">Toxins (Outside): <span id="toxin-value">10</span></label>
                    <input type="range" id="toxin-slider" min="0" max="100" value="10">
                </div>
                <div class="control-group">
                    <label for="water-slider">Water (Outside): <span id="water-value">50</span> <span id="water-label-suffix">(Optimal)</span></label>
                    <input type="range" id="water-slider" min="0" max="100" value="50">
                </div>
                <div class="control-group">
                    <label for="atp-potential-slider">Cell Energy Potential (ATP Target): <span id="atp-potential-value">70</span>%</label>
                    <input type="range" id="atp-potential-slider" min="0" max="100" value="70">
                </div>
                <button id="reset-button">Reset Cell</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // DOM Elements
            const cellElement = document.getElementById('cell');
            const nucleusElement = document.getElementById('nucleus');
            const atpDisplayInCell = document.getElementById('atp-value-in-cell');
            const cellDeathText = document.getElementById('cell-death-text');

            const glucoseSlider = document.getElementById('glucose-slider');
            const oxygenSlider = document.getElementById('oxygen-slider');
            const toxinSlider = document.getElementById('toxin-slider');
            const waterSlider = document.getElementById('water-slider');
            const atpPotentialSlider = document.getElementById('atp-potential-slider');

            const glucoseValueDisplay = document.getElementById('glucose-value');
            const oxygenValueDisplay = document.getElementById('oxygen-value');
            const toxinValueDisplay = document.getElementById('toxin-value');
            const waterValueDisplay = document.getElementById('water-value');
            const waterLabelSuffix = document.getElementById('water-label-suffix');
            const atpPotentialValueDisplay = document.getElementById('atp-potential-value');

            const resetButton = document.getElementById('reset-button');

            // Default state values
            const DEFAULT_STATE = {
                glucose: 50,
                oxygen: 60,
                toxins: 10,
                water: 50, // Optimal
                atpPotential: 70,
                currentATP: 70,
                health: 100,
                isAlive: true
            };

            let state = { ...DEFAULT_STATE };
            let gameInterval;

            // Update functions
            function updateSliderDisplays() {
                glucoseValueDisplay.textContent = glucoseSlider.value;
                oxygenValueDisplay.textContent = oxygenSlider.value;
                toxinValueDisplay.textContent = toxinSlider.value;
                waterValueDisplay.textContent = waterSlider.value;
                atpPotentialValueDisplay.textContent = atpPotentialSlider.value;

                const water = parseInt(waterSlider.value);
                if (water < 30) waterLabelSuffix.textContent = "(Too Low)";
                else if (water > 70) waterLabelSuffix.textContent = "(Too High)";
                else waterLabelSuffix.textContent = "(Optimal)";
            }

            function updateCellLogic() {
                if (!state.isAlive) return;

                // Read current slider values into state
                state.glucose = parseInt(glucoseSlider.value);
                state.oxygen = parseInt(oxygenSlider.value);
                state.toxins = parseInt(toxinSlider.value);
                state.water = parseInt(waterSlider.value);
                state.atpPotential = parseInt(atpPotentialSlider.value);

                let atpChange = 0;
                // ATP Production (simplified)
                // Requires Oxygen and Glucose. Max production aims for atpPotential.
                const productionEfficiency = (state.oxygen / 100) * (state.glucose / 100);
                // If oxygen or glucose is very low, production is minimal
                const effectiveOxygen = Math.max(0, state.oxygen - 10) / 90; // Penalize low oxygen heavily
                const effectiveGlucose = Math.max(0, state.glucose - 10) / 90; // Penalize low glucose

                if (state.atpPotential > 0) {
                     // Regeneration towards target ATP, scaled by efficiency and potential
                    const maxRegen = state.atpPotential * 0.05; // Can regen up to 5% of potential per tick
                    atpChange += maxRegen * effectiveOxygen * effectiveGlucose;
                }


                // ATP Consumption
                // Base metabolic cost (higher for higher potential ATP)
                let consumption = state.atpPotential * 0.01; // 1% of potential
                // Toxin cost
                consumption += (state.toxins / 100) * (state.atpPotential * 0.04); // Toxins up to 4% of potential
                // Low oxygen stress cost (if cell is trying to survive without enough O2)
                if (state.oxygen < 20) {
                    consumption += ( (20 - state.oxygen) / 20 ) * (state.atpPotential * 0.02);
                }
                atpChange -= consumption;
                
                state.currentATP += atpChange;
                state.currentATP = Math.max(0, Math.min(state.currentATP, state.atpPotential));


                // Health Logic
                let healthChange = 0;
                // Stress factors
                if (state.currentATP < state.atpPotential * 0.25 && state.atpPotential > 0) healthChange -= 2; // Low ATP
                if (state.currentATP === 0 && state.atpPotential > 0) healthChange -= 5; // Critically low ATP
                
                if (state.toxins > 60) healthChange -= (state.toxins - 60) / 10; // High toxins
                if (state.water < 25 || state.water > 75) healthChange -= 2; // Osmotic stress
                if (state.glucose > 85) healthChange -= (state.glucose - 85) / 15; // Glucotoxicity
                if (state.oxygen < 10) healthChange -= 2; // Severe hypoxia

                // Regeneration (if conditions are good)
                if (healthChange === 0 && state.currentATP > state.atpPotential * 0.7 && state.toxins < 20 && state.water >=30 && state.water <=70) {
                    healthChange += 0.5;
                }
                
                state.health += healthChange;
                state.health = Math.max(0, Math.min(state.health, 100));

                if (state.health <= 0 || (state.currentATP <= 0 && state.atpPotential > 0)) {
                    state.isAlive = false;
                }
            }

            function updateVisuals() {
                // ATP display
                atpDisplayInCell.textContent = Math.round(state.currentATP);

                if (!state.isAlive) {
                    cellElement.style.backgroundColor = 'var(--cell-dead-color)';
                    nucleusElement.style.backgroundColor = '#505050';
                    cellDeathText.style.display = 'block';
                    cellElement.style.transform = 'scale(0.95)'; // Slight shrink
                    // Optionally disable sliders
                    // [glucoseSlider, oxygenSlider, toxinSlider, waterSlider, atpPotentialSlider].forEach(s => s.disabled = true);
                    return; // No more visual updates if dead, besides the death state
                }

                // Cell Health Color
                if (state.health > 80) cellElement.style.backgroundColor = 'var(--cell-healthy-color)';
                else if (state.health > 60) cellElement.style.backgroundColor = 'var(--cell-stress-low-color)';
                else if (state.health > 30) cellElement.style.backgroundColor = 'var(--cell-stress-medium-color)';
                else cellElement.style.backgroundColor = 'var(--cell-stress-high-color)';
                
                nucleusElement.style.backgroundColor = 'var(--nucleus-color)'; // Reset nucleus color if alive

                // Water Level Effect (Cell Size)
                const water = state.water;
                let scale = 1.0;
                if (water < 30) scale = 1.0 - ( (30 - water) / 30 ) * 0.3; // Shrink up to 30%
                else if (water > 70) scale = 1.0 + ( (water - 70) / 30 ) * 0.3; // Swell up to 30%
                cellElement.style.transform = `scale(${scale.toFixed(2)})`;

                cellDeathText.style.display = 'none';
            }

            function gameLoop() {
                updateCellLogic();
                updateVisuals();
            }

            function resetCell() {
                state = { ...DEFAULT_STATE, currentATP: DEFAULT_STATE.atpPotential }; // Ensure currentATP resets properly

                glucoseSlider.value = state.glucose;
                oxygenSlider.value = state.oxygen;
                toxinSlider.value = state.toxins;
                waterSlider.value = state.water;
                atpPotentialSlider.value = state.atpPotential;
                
                // [glucoseSlider, oxygenSlider, toxinSlider, waterSlider, atpPotentialSlider].forEach(s => s.disabled = false);

                updateSliderDisplays();
                updateVisuals(); // Initial visual update after reset

                if (!gameInterval && state.isAlive) { // Restart loop if it was stopped by death
                    gameInterval = setInterval(gameLoop, 250);
                } else if (!state.isAlive) { // If reset from dead state
                     state.isAlive = true; // Revive the cell
                     if (!gameInterval) gameInterval = setInterval(gameLoop, 250); // Ensure interval is running
                     updateVisuals(); // Call again to remove death visuals
                }
            }

            // Event Listeners
            [glucoseSlider, oxygenSlider, toxinSlider, waterSlider, atpPotentialSlider].forEach(slider => {
                slider.addEventListener('input', () => {
                    updateSliderDisplays();
                    if (state.isAlive) { // Only immediately update logic if alive, otherwise it happens on next tick
                         // updateCellLogic(); // Immediate logic update can be jarring, let interval handle it
                         // updateVisuals(); 
                    }
                });
            });

            resetButton.addEventListener('click', resetCell);

            // Initial Setup
            updateSliderDisplays();
            updateVisuals();
            gameInterval = setInterval(gameLoop, 250); // Start the simulation loop
        });
    </script>
</body>
</html>
