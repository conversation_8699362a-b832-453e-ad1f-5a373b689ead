
import React from 'react';
import type { CaseStudyScenario } from '../../../types';
import Card from '../../common/Card';

interface InteractiveCaseStudyProps {
  scenario: CaseStudyScenario;
}

const InteractiveCaseStudy: React.FC<InteractiveCaseStudyProps> = ({ scenario }) => {
  return (
    <Card className="my-4 bg-white shadow-lg">
      {/* <h4 className="text-xl font-semibold text-primary mb-2">{scenario.title}</h4> */}
      <p className="text-md text-textlight/80 mb-3">{scenario.description}</p>
      
      <div className="p-3 bg-gray-50 rounded-lg border border-gray-200 mb-4">
        <p className="font-medium text-secondary">Explanation / Key Points:</p>
        <p className="text-sm text-textlight/70 whitespace-pre-line">{scenario.explanation}</p>
      </div>

      {scenario.interactiveElements && (
        <div className="p-3 bg-gray-100 rounded-lg">
          <p className="font-medium text-secondary mb-2">Interactive Element:</p>
          {typeof scenario.interactiveElements === 'string' 
            ? <p className="text-sm text-gray-600">{scenario.interactiveElements}</p> 
            : scenario.interactiveElements
          }
          <p className="text-xs text-gray-500 mt-2">Actual simulation controls (e.g., sliders for intensity, graphs for vitals) would be implemented here.</p>
        </div>
      )}
    </Card>
  );
};

export default InteractiveCaseStudy;
