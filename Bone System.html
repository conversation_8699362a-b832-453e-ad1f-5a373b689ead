<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Human Skeleton</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 15px;
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
        }

        #app-wrapper {
            display: flex;
            flex-direction: column;
            gap: 20px;
            max-width: 1200px;
            margin: auto;
        }

        #diagram-column {
            background-color: #ffffff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 350px; /* Ensure space for SVG */
        }

        #skeleton-svg {
            width: 100%;
            max-width: 220px; /* Adjusted for better fit */
            height: auto;
        }

        .svg-bone-part > * { /* Target direct children like circle, line, etc. */
            fill: #d1d5db; /* Tailwind gray-300 */
            stroke: #4b5563; /* Tailwind gray-600 */
            stroke-width: 2.5px; /* SVG units */
            transition: fill 0.25s ease-in-out, stroke 0.25s ease-in-out;
        }

        .svg-bone-part.highlighted > * {
            fill: #fca5a5; /* Tailwind red-300 for highlight */
            stroke: #b91c1c; /* Tailwind red-700 for highlight stroke */
        }

        #controls-column {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        #labels-area {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
            gap: 10px;
            padding: 15px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .bone-label {
            padding: 12px 10px;
            background-color: #e5e7eb; /* Tailwind gray-200 */
            border: 1px solid #d1d5db; /* Tailwind gray-300 */
            border-radius: 6px;
            text-align: center;
            cursor: pointer;
            transition: background-color 0.2s, color 0.2s, border-color 0.2s, transform 0.1s;
            font-size: 0.95em;
            font-weight: 500;
        }

        .bone-label:hover {
            background-color: #d1d5db; /* Tailwind gray-300 */
            border-color: #9ca3af; /* Tailwind gray-400 */
        }
        
        .bone-label:active {
            transform: translateY(1px);
        }

        .bone-label.selected-label {
            background-color: #ef4444; /* Tailwind red-500 */
            color: white;
            border-color: #dc2626; /* Tailwind red-600 */
            font-weight: bold;
        }

        #info-box-area {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: 120px;
        }

        #info-box h3 {
            margin-top: 0;
            margin-bottom: 8px;
            color: #c2410c; /* Tailwind orange-700 */
            font-size: 1.1em;
            border-bottom: 1px solid #fed7aa; /* Tailwind orange-200 */
            padding-bottom: 5px;
        }

        #info-box p {
            margin-top: 0;
            margin-bottom: 12px;
            font-size: 0.9em;
            color: #374151; /* Tailwind gray-700 */
        }
        
        #info-box p.placeholder {
            color: #6b7280; /* Tailwind gray-500 */
            font-style: italic;
        }

        .info-group-item {
            margin-bottom: 15px;
        }

        .info-group-item:last-child {
            margin-bottom: 0;
        }

        #reset-button {
            padding: 12px 20px;
            background-color: #6b7280; /* Tailwind gray-500 */
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: background-color 0.2s, transform 0.1s;
            width: 100%;
        }

        #reset-button:hover {
            background-color: #4b5563; /* Tailwind gray-600 */
        }
        #reset-button:active {
            transform: translateY(1px);
        }

        /* Desktop layout adjustments */
        @media (min-width: 768px) {
            #app-wrapper {
                flex-direction: row;
                align-items: flex-start;
            }
            #diagram-column {
                flex: 0 0 280px; /* Slightly narrower fixed width for diagram */
                position: sticky; /* Make diagram sticky on scroll */
                top: 20px;
            }
            #skeleton-svg {
                max-width: 100%;
            }
            #controls-column {
                flex: 1;
            }
            #labels-area {
                grid-template-columns: repeat(2, 1fr); /* 2 columns for labels */
            }
            #reset-button {
                width: auto; /* Allow button to size to content on larger screens */
                align-self: flex-start; /* Align button to the left */
            }
        }
         @media (min-width: 1024px) {
            #labels-area {
                grid-template-columns: repeat(3, 1fr); /* 3 columns for labels on very large screens */
            }
        }
    </style>
</head>
<body>

    <div id="app-wrapper">
        <div id="diagram-column">
            <svg id="skeleton-svg" viewBox="0 0 200 380" preserveAspectRatio="xMidYMid meet">
                <!-- Skull -->
                <g id="svg-skull-g" class="svg-bone-part">
                    <circle cx="100" cy="40" r="30" />
                </g>
                <!-- Spine -->
                <g id="svg-spine-g" class="svg-bone-part">
                    <line x1="100" y1="70" x2="100" y2="180" stroke-linecap="round" />
                </g>
                <!-- Thorax (Rib cage) -->
                <g id="svg-thorax-g" class="svg-bone-part">
                    <ellipse cx="100" cy="115" rx="40" ry="50" />
                </g>
                <!-- Pelvis -->
                <g id="svg-pelvis-g" class="svg-bone-part">
                    <rect x="78" y="178" width="44" height="22" rx="5" ry="5" />
                </g>
                <!-- Upper Limbs -->
                <g id="svg-upperLimb-g" class="svg-bone-part">
                    <!-- Left Arm -->
                    <polyline points="100,85 65,100 55,145 48,175" stroke-linecap="round" stroke-linejoin="round"/>
                    <!-- Right Arm -->
                    <polyline points="100,85 135,100 145,145 152,175" stroke-linecap="round" stroke-linejoin="round"/>
                </g>
                <!-- Lower Limbs -->
                <g id="svg-lowerLimb-g" class="svg-bone-part">
                    <!-- Left Leg -->
                    <polyline points="85,200 75,260 70,330" stroke-linecap="round" stroke-linejoin="round"/>
                    <!-- Right Leg -->
                    <polyline points="115,200 125,260 130,330" stroke-linecap="round" stroke-linejoin="round"/>
                </g>
            </svg>
        </div>

        <div id="controls-column">
            <div id="labels-area">
                <div class="bone-label" data-groupid="skull">Skull</div>
                <div class="bone-label" data-groupid="spine">Spine</div>
                <div class="bone-label" data-groupid="thorax">Thorax</div>
                <div class="bone-label" data-groupid="pelvis">Pelvis</div>
                <div class="bone-label" data-groupid="upperLimb">Upper Limb</div>
                <div class="bone-label" data-groupid="lowerLimb">Lower Limb</div>
            </div>

            <div id="info-box-area">
                <div id="info-box">
                    <p class="placeholder">Click on a bone group label above to learn about its major components.</p>
                </div>
            </div>

            <button id="reset-button">Reset Selections</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const boneData = {
                skull: {
                    name: "Skull",
                    svgId: "svg-skull-g",
                    bones: ["Frontal bone", "Parietal bone", "Occipital bone", "Temporal bone", "Sphenoid bone", "Ethmoid bone", "Nasal bone", "Maxilla", "Zygomatic bones", "Mandible"]
                },
                spine: {
                    name: "Spine",
                    svgId: "svg-spine-g",
                    bones: ["Cervical vertebrae (7)", "Thoracic vertebrae (12)", "Lumbar vertebrae (5)", "Sacrum", "Coccyx"]
                },
                upperLimb: {
                    name: "Upper Limb",
                    svgId: "svg-upperLimb-g",
                    bones: ["Clavicle", "Scapula", "Humerus", "Radius", "Ulna", "Carpal bones (8)", "Metacarpal bones (5)", "Phalanges"]
                },
                thorax: {
                    name: "Thorax",
                    svgId: "svg-thorax-g",
                    bones: ["Clavicle", "Sternum", "Ribs (12 pairs)"] // Clarified Ribs
                },
                pelvis: {
                    name: "Pelvis",
                    svgId: "svg-pelvis-g",
                    bones: ["Ilium", "Ischium", "Pubis (these three fuse to form the hip bone)", "Sacrum"]
                },
                lowerLimb: {
                    name: "Lower Limb",
                    svgId: "svg-lowerLimb-g",
                    bones: ["Femur", "Patella (Kneecap)", "Tibia", "Fibula", "Tarsal bones (7)", "Metatarsal bones (5)", "Phalanges"] // Added Patella
                }
            };

            const labels = document.querySelectorAll('.bone-label');
            const infoBox = document.getElementById('info-box');
            const resetButton = document.getElementById('reset-button');
            const svgParts = {};
            Object.keys(boneData).forEach(key => {
                svgParts[key] = document.getElementById(boneData[key].svgId);
            });

            let selectedGroups = new Set();

            function updateInfoBox() {
                infoBox.innerHTML = '';
                if (selectedGroups.size === 0) {
                    infoBox.innerHTML = '<p class="placeholder">Click on a bone group label above to learn about its major components.</p>';
                    return;
                }

                selectedGroups.forEach(groupId => {
                    const groupData = boneData[groupId];
                    if (groupData) {
                        const groupDiv = document.createElement('div');
                        groupDiv.className = 'info-group-item';
                        
                        const heading = document.createElement('h3');
                        heading.textContent = groupData.name;
                        groupDiv.appendChild(heading);

                        const bonesList = document.createElement('p');
                        bonesList.textContent = groupData.bones.join(', ');
                        groupDiv.appendChild(bonesList);
                        
                        infoBox.appendChild(groupDiv);
                    }
                });
            }

            labels.forEach(label => {
                label.addEventListener('click', () => {
                    const groupId = label.dataset.groupid;
                    const svgElement = svgParts[groupId];

                    if (selectedGroups.has(groupId)) {
                        selectedGroups.delete(groupId);
                        label.classList.remove('selected-label');
                        if (svgElement) svgElement.classList.remove('highlighted');
                    } else {
                        selectedGroups.add(groupId);
                        label.classList.add('selected-label');
                        if (svgElement) svgElement.classList.add('highlighted');
                    }
                    updateInfoBox();
                });
            });

            resetButton.addEventListener('click', () => {
                selectedGroups.clear();
                labels.forEach(label => label.classList.remove('selected-label'));
                Object.values(svgParts).forEach(part => {
                    if(part) part.classList.remove('highlighted');
                });
                updateInfoBox();
            });

            // Initial call to set placeholder
            updateInfoBox();
        });
    </script>

</body>
</html>
