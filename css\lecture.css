/* ===== LECTURE PAGE STYLES ===== */

:root {
    --lecture-primary: #2563eb;
    --lecture-secondary: #06b6d4;
    --lecture-accent: #8b5cf6;
    --lecture-bg: #f8fafc;
    --lecture-surface: #ffffff;
    --lecture-text: #0f172a;
    --lecture-text-light: #475569;
    --lecture-border: #e2e8f0;
    --lecture-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    --lecture-transition: all 0.3s ease;
}

/* ===== LECTURE HEADER ===== */
.lecture-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--lecture-surface);
    border-bottom: 1px solid var(--lecture-border);
    box-shadow: var(--lecture-shadow);
    z-index: 1000;
}

.lecture-nav {
    padding: 1rem 0;
}

.lecture-nav .nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.lecture-title h1 {
    color: var(--lecture-text);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.btn-home {
    background: linear-gradient(135deg, var(--lecture-primary), var(--lecture-secondary));
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: var(--lecture-transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-home:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
}

/* ===== LECTURE PROGRESS ===== */
.lecture-progress {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    background: var(--lecture-surface);
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--lecture-border);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: var(--lecture-border);
    border-radius: 3px;
    overflow: hidden;
    margin-right: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--lecture-primary), var(--lecture-secondary));
    border-radius: 3px;
    transition: width 0.5s ease;
    width: 8.33%; /* 1/12 slides */
}

.progress-info {
    color: var(--lecture-text-light);
    font-weight: 500;
    font-size: 0.9rem;
}

/* ===== LECTURE MAIN ===== */
.lecture-main {
    margin-top: 140px;
    min-height: calc(100vh - 200px);
    background: var(--lecture-bg);
}

.lecture-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    position: relative;
}

/* ===== LECTURE SLIDES ===== */
.lecture-slide {
    display: none;
    min-height: 70vh;
    background: var(--lecture-surface);
    border-radius: 15px;
    box-shadow: var(--lecture-shadow);
    overflow: hidden;
    animation: slideIn 0.5s ease-out;
}

.lecture-slide.active {
    display: block;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-content {
    padding: 3rem;
    height: 100%;
}

.slide-header {
    text-align: center;
    margin-bottom: 3rem;
}

.slide-title {
    color: var(--lecture-text);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--lecture-primary), var(--lecture-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.slide-subtitle {
    color: var(--lecture-text-light);
    font-size: 1.2rem;
    font-weight: 400;
}

/* ===== CONTENT LAYOUTS ===== */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.content-text h3 {
    color: var(--lecture-text);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.animated-list {
    list-style: none;
    padding: 0;
}

.animated-list li {
    background: var(--lecture-bg);
    padding: 1rem 1.5rem;
    margin-bottom: 0.75rem;
    border-radius: 8px;
    border-left: 4px solid var(--lecture-primary);
    opacity: 0;
    animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== ANIMATED CELL ===== */
.content-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.animated-cell {
    width: 300px;
    height: 300px;
    position: relative;
    animation: cellPulse 3s ease-in-out infinite;
}

@keyframes cellPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.cell-membrane {
    width: 100%;
    height: 100%;
    border: 3px solid var(--lecture-primary);
    border-radius: 50%;
    position: absolute;
    animation: membraneRotate 10s linear infinite;
}

@keyframes membraneRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.nucleus {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--lecture-accent), #a78bfa);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: nucleusPulse 2s ease-in-out infinite;
}

@keyframes nucleusPulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

.organelles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.mitochondria {
    width: 30px;
    height: 15px;
    background: var(--lecture-secondary);
    border-radius: 15px;
    position: absolute;
    top: 30%;
    left: 20%;
    animation: organelleFloat 4s ease-in-out infinite;
}

.er {
    width: 40px;
    height: 8px;
    background: #10b981;
    border-radius: 4px;
    position: absolute;
    top: 60%;
    right: 25%;
    animation: organelleFloat 3s ease-in-out infinite reverse;
}

.golgi {
    width: 25px;
    height: 20px;
    background: #f59e0b;
    border-radius: 4px;
    position: absolute;
    bottom: 25%;
    left: 30%;
    animation: organelleFloat 5s ease-in-out infinite;
}

@keyframes organelleFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* ===== THEORY CARDS ===== */
.theory-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.theory-card {
    background: var(--lecture-surface);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: var(--lecture-shadow);
    border: 2px solid transparent;
    transition: var(--lecture-transition);
    opacity: 0;
    transform: translateY(30px);
    animation: cardSlideIn 0.6s ease-out forwards;
}

.theory-card:nth-child(1) { animation-delay: 0.2s; }
.theory-card:nth-child(2) { animation-delay: 0.4s; }
.theory-card:nth-child(3) { animation-delay: 0.6s; }

@keyframes cardSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.theory-card:hover {
    border-color: var(--lecture-primary);
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.2);
}

.card-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--lecture-primary), var(--lecture-secondary));
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 1rem;
}

.theory-card h3 {
    color: var(--lecture-text);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.theory-card p {
    color: var(--lecture-text-light);
    line-height: 1.6;
}

.card-icon {
    margin-top: 1rem;
    font-size: 2rem;
    color: var(--lecture-primary);
}

/* ===== COMPARISON CONTAINER ===== */
.comparison-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.cell-type {
    background: var(--lecture-surface);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: var(--lecture-shadow);
    text-align: center;
}

.cell-type h3 {
    color: var(--lecture-text);
    margin-bottom: 2rem;
    font-size: 1.5rem;
}

.cell-diagram {
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
}

.prokaryotic-cell, .eukaryotic-cell {
    width: 150px;
    height: 150px;
    position: relative;
    border-radius: 50%;
}

.prokaryotic-cell {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.eukaryotic-cell {
    background: linear-gradient(135deg, #34d399, #10b981);
}

.cell-type ul {
    text-align: left;
    color: var(--lecture-text-light);
}

.cell-type li {
    margin-bottom: 0.5rem;
    padding-left: 1rem;
    position: relative;
}

.cell-type li::before {
    content: "•";
    color: var(--lecture-primary);
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* ===== LECTURE CONTROLS ===== */
.lecture-controls {
    position: fixed;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    background: var(--lecture-surface);
    padding: 1rem 2rem;
    border-radius: 50px;
    box-shadow: var(--lecture-shadow);
    display: flex;
    align-items: center;
    gap: 2rem;
    z-index: 1000;
}

.control-btn {
    background: linear-gradient(135deg, var(--lecture-primary), var(--lecture-secondary));
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: var(--lecture-transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.3);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.slide-indicators {
    display: flex;
    gap: 0.5rem;
}

.slide-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--lecture-border);
    cursor: pointer;
    transition: var(--lecture-transition);
}

.slide-indicator.active {
    background: var(--lecture-primary);
}

/* ===== LECTURE SIDEBAR ===== */
.lecture-sidebar {
    position: fixed;
    top: 140px;
    right: -300px;
    width: 300px;
    height: calc(100vh - 200px);
    background: var(--lecture-surface);
    box-shadow: var(--lecture-shadow);
    transition: var(--lecture-transition);
    z-index: 998;
    border-radius: 15px 0 0 15px;
}

.lecture-sidebar.active {
    right: 0;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--lecture-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-toggle {
    background: var(--lecture-primary);
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
}

.lecture-outline {
    list-style: none;
    padding: 1rem;
}

.outline-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--lecture-transition);
    margin-bottom: 0.5rem;
}

.outline-item:hover {
    background: var(--lecture-bg);
}

.outline-item.active {
    background: linear-gradient(135deg, var(--lecture-primary), var(--lecture-secondary));
    color: white;
}

.outline-number {
    width: 30px;
    height: 30px;
    background: var(--lecture-border);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.outline-item.active .outline-number {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* ===== LECTURE FOOTER ===== */
.lecture-footer {
    background: var(--lecture-surface);
    padding: 1rem 2rem;
    border-top: 1px solid var(--lecture-border);
    text-align: center;
    margin-top: 2rem;
}

.lecture-footer .author-info p {
    margin: 0.25rem 0;
    color: var(--lecture-text-light);
    font-size: 0.9rem;
}

/* ===== PHYSIOLOGY SPECIFIC STYLES ===== */

/* Physiology Diagram */
.physiology-diagram {
    width: 300px;
    height: 400px;
    position: relative;
    margin: 0 auto;
}

.body-outline {
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #f3f4f6 0%, #e5e7eb 100%);
    border-radius: 50px 50px 20px 20px;
    position: relative;
    border: 2px solid var(--lecture-primary);
}

.organ-system {
    position: absolute;
    border-radius: 50%;
    animation: organPulse 2s ease-in-out infinite;
    cursor: pointer;
    transition: var(--lecture-transition);
}

.organ-system:hover {
    transform: scale(1.2);
}

.brain {
    width: 40px;
    height: 40px;
    background: #8b5cf6;
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
}

.heart {
    width: 35px;
    height: 35px;
    background: #ef4444;
    top: 35%;
    left: 45%;
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
}

.lungs {
    width: 30px;
    height: 45px;
    background: #06b6d4;
    top: 30%;
    right: 20%;
    border-radius: 15px;
}

.kidneys {
    width: 20px;
    height: 30px;
    background: #10b981;
    top: 55%;
    left: 25%;
    border-radius: 10px;
}

@keyframes organPulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

/* Homeostasis Styles */
.homeostasis-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.homeostasis-center {
    text-align: center;
}

.balance-scale {
    width: 200px;
    height: 150px;
    position: relative;
    margin: 0 auto 2rem;
}

.scale-base {
    width: 20px;
    height: 100px;
    background: var(--lecture-primary);
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

.scale-arm {
    width: 150px;
    height: 4px;
    background: var(--lecture-primary);
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    animation: scaleBalance 3s ease-in-out infinite;
}

@keyframes scaleBalance {
    0%, 100% { transform: translateX(-50%) rotate(0deg); }
    25% { transform: translateX(-50%) rotate(-5deg); }
    75% { transform: translateX(-50%) rotate(5deg); }
}

.scale-left, .scale-right {
    width: 40px;
    height: 30px;
    background: var(--lecture-secondary);
    border-radius: 5px;
    position: absolute;
    top: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: white;
    font-weight: 500;
}

.scale-left { left: 10px; }
.scale-right { right: 10px; }

.homeostasis-examples {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.example-card {
    background: var(--lecture-surface);
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: var(--lecture-shadow);
    border: 2px solid transparent;
    transition: var(--lecture-transition);
}

.example-card:hover {
    border-color: var(--lecture-primary);
    transform: translateY(-3px);
}

.example-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.example-card h4 {
    color: var(--lecture-text);
    margin-bottom: 0.5rem;
}

.example-card p {
    color: var(--lecture-text-light);
    font-weight: 500;
}

/* Feedback Systems */
.feedback-comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.feedback-type {
    background: var(--lecture-surface);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: var(--lecture-shadow);
}

.feedback-type h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--lecture-text);
}

.feedback-loop {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.feedback-step {
    background: var(--lecture-bg);
    padding: 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--lecture-transition);
}

.feedback-step:hover {
    background: var(--lecture-primary);
    color: white;
}

.step-icon {
    font-size: 1.5rem;
}

.feedback-arrow {
    text-align: center;
    font-size: 1.5rem;
    color: var(--lecture-primary);
}

.negative-arrow {
    color: #ef4444;
    font-size: 2rem;
}

.positive-arrow {
    color: #10b981;
    font-size: 2rem;
}

.feedback-example {
    background: var(--lecture-bg);
    padding: 1rem;
    border-radius: 8px;
    font-style: italic;
}

/* Cardiovascular Animations */
.cardiovascular-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.heart-animation {
    width: 250px;
    height: 200px;
    position: relative;
    margin: 0 auto;
}

.heart-chambers {
    width: 100%;
    height: 100%;
    position: relative;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border-radius: 50px 50px 20px 20px;
    animation: heartBeat 1.2s ease-in-out infinite;
}

@keyframes heartBeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.chamber {
    position: absolute;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 0.9rem;
}

.ra { width: 50px; height: 40px; top: 20px; right: 20px; }
.la { width: 50px; height: 40px; top: 20px; left: 20px; }
.rv { width: 50px; height: 60px; bottom: 20px; right: 20px; }
.lv { width: 50px; height: 60px; bottom: 20px; left: 20px; }

.cardiac-cycle {
    text-align: center;
}

.cycle-phases {
    display: flex;
    gap: 2rem;
    justify-content: center;
    margin-top: 2rem;
}

.phase {
    background: var(--lecture-bg);
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    min-width: 120px;
}

.phase h4 {
    color: var(--lecture-text);
    margin-bottom: 0.5rem;
}

.phase p {
    color: var(--lecture-text-light);
    margin-bottom: 0.5rem;
}

.phase span {
    color: var(--lecture-primary);
    font-weight: 600;
}

/* Respiratory Animations */
.respiratory-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.breathing-animation {
    width: 250px;
    height: 300px;
    position: relative;
    margin: 0 auto;
}

.lungs {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-bottom: 20px;
}

.lung {
    width: 80px;
    height: 120px;
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    border-radius: 40px 40px 20px 20px;
    position: relative;
    animation: breathing 3s ease-in-out infinite;
}

@keyframes breathing {
    0%, 100% { transform: scaleY(1); }
    50% { transform: scaleY(1.2); }
}

.diaphragm {
    width: 200px;
    height: 20px;
    background: #8b5cf6;
    border-radius: 50%;
    position: absolute;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
    animation: diaphragmMove 3s ease-in-out infinite;
}

@keyframes diaphragmMove {
    0%, 100% { transform: translateX(-50%) translateY(0); }
    50% { transform: translateX(-50%) translateY(10px); }
}

.gas-exchange {
    text-align: center;
}

.exchange-process {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
}

.gas-info {
    background: var(--lecture-bg);
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    min-width: 120px;
}

.gas-molecule {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-weight: bold;
}

.oxygen .gas-molecule {
    background: #ef4444;
    animation: moleculeFloat 2s ease-in-out infinite;
}

.carbon-dioxide .gas-molecule {
    background: #6b7280;
    animation: moleculeFloat 2s ease-in-out infinite reverse;
}

@keyframes moleculeFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.exchange-arrow {
    font-size: 2rem;
    color: var(--lecture-primary);
    animation: arrowPulse 1.5s ease-in-out infinite;
}

@keyframes arrowPulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .lecture-nav .nav-container {
        padding: 0 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .lecture-title h1 {
        font-size: 1.25rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .theory-cards {
        grid-template-columns: 1fr;
    }

    .comparison-container {
        grid-template-columns: 1fr;
    }

    .homeostasis-container {
        grid-template-columns: 1fr;
    }

    .feedback-comparison {
        grid-template-columns: 1fr;
    }

    .cardiovascular-container {
        grid-template-columns: 1fr;
    }

    .respiratory-container {
        grid-template-columns: 1fr;
    }

    .slide-content {
        padding: 2rem 1rem;
    }

    .lecture-controls {
        bottom: 1rem;
        padding: 0.75rem 1rem;
        gap: 1rem;
    }

    .control-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}
