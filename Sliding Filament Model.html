<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sliding Filament Model Explorer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        #app-container {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 800px;
        }

        h1 {
            text-align: center;
            color: #2c3e50;
        }

        #sarcomere-visualization {
            position: relative;
            width: 100%;
            height: 250px; /* Increased height for better visualization */
            border: 1px solid #ccc;
            margin-bottom: 20px;
            overflow: hidden; /* To contain elements that might move */
            background-color: #e9f5ff; /* Light blue background for sarcomere */
        }

        .z-line {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 10px;
            background-color: #3498db; /* Blue Z-lines */
            transition: transform 0.5s ease-in-out;
        }
        #z-line-left { left: 5%; }
        #z-line-right { right: 5%; }

        .actin-filament {
            position: absolute;
            height: 10px;
            background-color: #e74c3c; /* Red actin */
            transition: transform 0.5s ease-in-out, width 0.5s ease-in-out;
            display: flex; /* For binding sites */
            align-items: center;
        }
        #actin-filament-left {
            left: calc(5% + 10px); /* Next to left Z-line */
            top: 35%;
            width: 35%;
            transform-origin: left center;
        }
        #actin-filament-right {
            right: calc(5% + 10px); /* Next to right Z-line */
            top: 55%; /* Slightly offset for clarity */
            width: 35%;
            transform-origin: right center;
            flex-direction: row-reverse; /* Binding sites from right */
        }
        
        .myosin-binding-site {
            width: 8px;
            height: 8px;
            background-color: #c0392b; /* Darker red for sites */
            border-radius: 50%;
            margin: 0 10px;
            opacity: 0.3; /* Initially hidden/covered */
            transition: opacity 0.3s ease-in-out;
        }
        .myosin-binding-site.exposed {
            opacity: 1;
        }

        .tropomyosin {
            position: absolute;
            height: 4px;
            background-color: #f1c40f; /* Yellow tropomyosin */
            width: 100%;
            top: 3px; /* On top of actin */
            opacity: 0.7;
            transition: transform 0.3s ease-in-out;
            transform-origin: center;
        }
        .tropomyosin.shifted {
            transform: translateY(-6px); /* Shift to expose sites */
        }

        #myosin-filament {
            position: absolute;
            left: 25%;
            right: 25%;
            top: 45%;
            height: 20px;
            background-color: #2ecc71; /* Green myosin */
            display: flex;
            justify-content: center; /* Center myosin heads */
            align-items: center;
        }

        .myosin-head {
            position: absolute; /* Position relative to myosin filament */
            width: 25px;
            height: 15px;
            background-color: #27ae60; /* Darker green head */
            border-radius: 5px 5px 0 0;
            transform-origin: bottom center;
            transition: transform 0.4s ease-in-out;
            /* Initial resting state, pointing slightly up */
            transform: rotate(20deg) translateY(-10px); 
        }
        /* Position one head for interaction, others could be static for visual */
        #myosin-head-1 { 
            bottom: 100%; /* Attaches to top of myosin filament */
            left: 50%; 
            margin-left: -12.5px; /* Center it */
        }
        /* Myosin head states */
        .myosin-head.cocked { transform: rotate(45deg) translateY(-15px); }
        .myosin-head.bound { transform: rotate(45deg) translateY(-25px) translateX(-5px); } /* Adjust to bind */
        .myosin-head.power-stroke { transform: rotate(-30deg) translateY(-20px) translateX(5px); }
        .myosin-head.detached { transform: rotate(0deg) translateY(-10px); }


        .calcium-ion, .atp-molecule {
            position: absolute;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
        }
        .calcium-ion {
            background-color: #007bff; /* Blue calcium */
            /* Position these dynamically via JS */
        }
        .atp-molecule {
            background-color: #ffc107; /* Yellow ATP */
            border-radius: 3px; /* Square-ish */
        }
        .calcium-ion.visible, .atp-molecule.visible {
            opacity: 1;
        }

        #controls-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .control-button {
            padding: 10px 15px;
            font-size: 1em;
            color: white;
            background-color: #5cb85c;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .control-button:hover {
            background-color: #4cae4c;
        }
        .control-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .action-button { background-color: #f0ad4e; }
        .action-button:hover { background-color: #ec971f; }


        #annotation-container {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            min-height: 60px;
            text-align: center;
        }
        #annotation-text {
            margin: 0;
            font-size: 1.1em;
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            body { padding: 10px; }
            #app-container { padding: 15px; }
            #sarcomere-visualization { height: 200px; }
            .control-button { font-size: 0.9em; padding: 8px 10px; }
            #annotation-text { font-size: 1em; }
            .actin-filament { height: 8px; }
            #actin-filament-left { top: 30%; }
            #actin-filament-right { top: 60%; }
            .myosin-binding-site { width: 6px; height: 6px; margin: 0 8px;}
            .myosin-head { width: 20px; height: 12px; }
             #myosin-head-1 { margin-left: -10px; }
        }

    </style>
</head>
<body>
    <div id="app-container">
        <h1>Sliding Filament Model</h1>

        <div id="sarcomere-visualization">
            <div class="z-line" id="z-line-left"></div>
            <div class="z-line" id="z-line-right"></div>

            <div class="actin-filament" id="actin-filament-left">
                <div class="tropomyosin"></div>
                <div class="myosin-binding-site"></div>
                <div class="myosin-binding-site"></div>
                <div class="myosin-binding-site"></div>
            </div>
            <div class="actin-filament" id="actin-filament-right">
                 <div class="tropomyosin"></div>
                <div class="myosin-binding-site"></div>
                <div class="myosin-binding-site"></div>
                <div class="myosin-binding-site"></div>
            </div>

            <div id="myosin-filament">
                <div class="myosin-head" id="myosin-head-1"></div>
                <!-- More static heads for visual fullness if desired -->
            </div>
            
            <div class="calcium-ion" id="calcium-1" style="top: 20%; left: 20%;"></div>
            <div class="calcium-ion" id="calcium-2" style="top: 70%; left: 30%;"></div>
            <div class="calcium-ion" id="calcium-3" style="top: 25%; left: 70%;"></div>

            <div class="atp-molecule" id="atp-mol-1" style="top: 60%; left: 45%;"></div>
        </div>

        <div id="controls-container">
            <button id="add-calcium-btn" class="control-button action-button">Add Ca²⁺</button>
            <button id="remove-calcium-btn" class="control-button action-button">Remove Ca²⁺</button>
            <button id="supply-atp-btn" class="control-button action-button">Supply ATP</button>
            <button id="step-prev-btn" class="control-button">&laquo; Prev Step</button>
            <button id="step-next-btn" class="control-button">Next Step &raquo;</button>
        </div>

        <div id="annotation-container">
            <p id="annotation-text">Welcome! Click 'Next Step' to begin.</p>
        </div>
    </div>

    <script>
        const state = {
            currentStepIndex: -1, // Start before the first step
            calciumPresent: false,
            atpForCocking: false,
            atpForDetachment: false,
            tropomyosinShifted: false,
            myosinHeadState: 'resting', // resting, cocked, bound, power-stroke, detached
            crossBridgeFormed: false,
            sarcomereContracted: 0, // 0 for normal, 1 for slightly, 2 for more contracted
            history: [],
        };

        // DOM Elements
        const zLineLeft = document.getElementById('z-line-left');
        const zLineRight = document.getElementById('z-line-right');
        const actinLeft = document.getElementById('actin-filament-left');
        const actinRight = document.getElementById('actin-filament-right');
        const tropomyosinElements = document.querySelectorAll('.tropomyosin');
        const bindingSites = document.querySelectorAll('.myosin-binding-site');
        const myosinHead = document.getElementById('myosin-head-1');
        const calciumIonElements = [
            document.getElementById('calcium-1'),
            document.getElementById('calcium-2'),
            document.getElementById('calcium-3')
        ];
        const atpMoleculeElement = document.getElementById('atp-mol-1');

        const annotationText = document.getElementById('annotation-text');
        const addCalciumBtn = document.getElementById('add-calcium-btn');
        const removeCalciumBtn = document.getElementById('remove-calcium-btn');
        const supplyAtpBtn = document.getElementById('supply-atp-btn');
        const prevStepBtn = document.getElementById('step-prev-btn');
        const nextStepBtn = document.getElementById('step-next-btn');

        const steps = [
            { // 0
                annotation: "Resting sarcomere. Myosin binding sites on actin are covered by tropomyosin. Click 'Add Ca²⁺' or 'Next Step' to introduce calcium.",
                action: () => {
                    state.calciumPresent = false;
                    state.atpForCocking = false;
                    state.atpForDetachment = false;
                    state.tropomyosinShifted = false;
                    state.myosinHeadState = 'resting';
                    state.crossBridgeFormed = false;
                    state.sarcomereContracted = 0;
                },
                awaits: null
            },
            { // 1
                annotation: "Calcium ions (Ca²⁺) are released. If not already added, click 'Add Ca²⁺'.",
                action: () => { state.calciumPresent = true; },
                awaits: { button: addCalciumBtn, flag: 'calciumPresent', value: true }
            },
            { // 2
                annotation: "Ca²⁺ binds to troponin (not explicitly shown, but assumed). This triggers tropomyosin.",
                action: () => {}, 
                prerequisite: () => state.calciumPresent
            },
            { // 3
                annotation: "Tropomyosin shifts, exposing myosin-binding sites on actin.",
                action: () => { state.tropomyosinShifted = true; },
                prerequisite: () => state.calciumPresent
            },
            { // 4
                annotation: "ATP is required to energize the myosin head. Click 'Supply ATP'.",
                action: () => { state.atpForCocking = true; }, // ATP becomes available
                awaits: { button: supplyAtpBtn, flag: 'atpForCocking', value: true },
                prerequisite: () => state.tropomyosinShifted
            },
            { // 5
                annotation: "ATP binds to myosin head and is hydrolyzed to ADP + Pi (not shown). Myosin head cocks (energized).",
                action: () => {
                    state.myosinHeadState = 'cocked';
                    state.atpForCocking = false; // ATP is used
                },
                prerequisite: () => state.tropomyosinShifted && document.getElementById('atp-mol-1').classList.contains('visible') // Check if ATP was supplied visually
            },
            { // 6
                annotation: "Energized myosin head binds to an exposed site on actin, forming a cross-bridge.",
                action: () => {
                    state.myosinHeadState = 'bound';
                    state.crossBridgeFormed = true;
                },
                prerequisite: () => state.myosinHeadState === 'cocked' && state.tropomyosinShifted
            },
            { // 7
                annotation: "Power stroke: Myosin head pivots, pulling the actin filament toward the center. Sarcomere shortens. ADP and Pi are released.",
                action: () => {
                    state.myosinHeadState = 'power-stroke';
                    state.sarcomereContracted = 1;
                },
                prerequisite: () => state.crossBridgeFormed && state.myosinHeadState === 'bound'
            },
            { // 8
                annotation: "A new ATP molecule is required for detachment. Click 'Supply ATP'.",
                action: () => { state.atpForDetachment = true; }, // ATP becomes available
                awaits: { button: supplyAtpBtn, flag: 'atpForDetachment', value: true },
                prerequisite: () => state.myosinHeadState === 'power-stroke'
            },
            { // 9
                annotation: "New ATP binds to myosin head, causing it to detach from actin.",
                action: () => {
                    state.myosinHeadState = 'detached';
                    state.crossBridgeFormed = false;
                    state.atpForDetachment = false; // ATP is used
                },
                prerequisite: () => state.myosinHeadState === 'power-stroke' && document.getElementById('atp-mol-1').classList.contains('visible') // Check if ATP was supplied
            },
            { // 10
                annotation: "If Ca²⁺ is still present and ATP available, the cycle can repeat (myosin re-cocks). Otherwise, click 'Remove Ca²⁺' for relaxation.",
                action: () => {
                    // If Ca2+ is present, prepare for re-cocking (implicitly head returns to resting then cocks if ATP available)
                    if(state.calciumPresent) state.myosinHeadState = 'resting'; // Ready to re-cock if ATP
                },
                prerequisite: () => !state.crossBridgeFormed
            },
            { // 11
                annotation: "Calcium is removed. Click 'Remove Ca²⁺' if not already done.",
                action: () => { state.calciumPresent = false; },
                awaits: { button: removeCalciumBtn, flag: 'calciumPresent', value: false }
            },
            { // 12
                annotation: "Tropomyosin covers binding sites. Myosin heads detach (if still bound and ATP available). Muscle relaxes, sarcomere returns to resting length.",
                action: () => {
                    state.tropomyosinShifted = false;
                    state.myosinHeadState = 'resting';
                    state.crossBridgeFormed = false;
                    state.sarcomereContracted = 0;
                    state.calciumPresent = false; // ensure it's false
                },
                prerequisite: () => !state.calciumPresent
            },
            { // 13
                annotation: "The cycle is complete or muscle is relaxed. You can restart by going to the previous step or clicking 'Add Ca²⁺' again for a new cycle.",
                action: () => {},
            }
        ];

        function updateVisuals() {
            // Calcium
            calciumIonElements.forEach(el => {
                if (state.calciumPresent) el.classList.add('visible');
                else el.classList.remove('visible');
            });
            if (state.currentStepIndex === 1 || (state.currentStepIndex === 0 && state.calciumPresent)) { // Show Ca2+ when added
                 calciumIonElements.forEach(el => el.classList.add('visible'));
            } else if (state.currentStepIndex >= 11 && !state.calciumPresent) { // Hide Ca2+ when removed
                 calciumIonElements.forEach(el => el.classList.remove('visible'));
            }


            // ATP
            // Show ATP if it's being supplied for cocking (step 4) or detachment (step 8)
            if ((state.currentStepIndex === 4 && state.atpForCocking) || (state.currentStepIndex === 8 && state.atpForDetachment)) {
                atpMoleculeElement.classList.add('visible');
            } else if (state.myosinHeadState === 'cocked' || state.myosinHeadState === 'detached') {
                // ATP has been "used" or is not immediately needed
                atpMoleculeElement.classList.remove('visible');
            }
             // Explicit hide after use
            if(state.currentStepIndex === 5 && !state.atpForCocking) atpMoleculeElement.classList.remove('visible');
            if(state.currentStepIndex === 9 && !state.atpForDetachment) atpMoleculeElement.classList.remove('visible');


            // Tropomyosin and Binding Sites
            tropomyosinElements.forEach(t => {
                if (state.tropomyosinShifted) t.classList.add('shifted');
                else t.classList.remove('shifted');
            });
            bindingSites.forEach(bs => {
                if (state.tropomyosinShifted) bs.classList.add('exposed');
                else bs.classList.remove('exposed');
            });

            // Myosin Head
            myosinHead.className = 'myosin-head'; // Reset classes
            if (state.myosinHeadState) {
                myosinHead.classList.add(state.myosinHeadState);
            }
            // Adjust myosin head attachment point visually during bound/power-stroke
            if (state.myosinHeadState === 'bound') {
                myosinHead.style.transform = 'rotate(45deg) translateY(-25px) translateX(-5px)';
            } else if (state.myosinHeadState === 'power-stroke') {
                myosinHead.style.transform = 'rotate(-30deg) translateY(-20px) translateX(5px)';
            } else if (state.myosinHeadState === 'cocked') {
                 myosinHead.style.transform = 'rotate(45deg) translateY(-15px)';
            } else if (state.myosinHeadState === 'detached' || state.myosinHeadState === 'resting') {
                 myosinHead.style.transform = 'rotate(20deg) translateY(-10px)';
            }


            // Sarcomere Contraction (Z-lines and Actin)
            const contractionAmount = state.sarcomereContracted * 15; // e.g. 0, 15, 30 pixels
            zLineLeft.style.transform = `translateX(${contractionAmount}px)`;
            zLineRight.style.transform = `translateX(-${contractionAmount}px)`;
            
            // Actin filaments slide with Z-lines.
            // For simplicity, we'll just move Z-lines. A more complex anim would slide actin too.
            // To make actin appear to slide:
            if (state.sarcomereContracted > 0) {
                // This is a simplified visual cue of shortening.
                // True actin sliding relative to myosin is harder without more complex positioning.
                actinLeft.style.transform = `translateX(${contractionAmount * 0.5}px)`;
                actinRight.style.transform = `translateX(-${contractionAmount * 0.5}px)`;
            } else {
                actinLeft.style.transform = `translateX(0px)`;
                actinRight.style.transform = `translateX(0px)`;
            }
        }
        
        function updateUI() {
            if (state.currentStepIndex < 0 || state.currentStepIndex >= steps.length) {
                annotationText.textContent = "Welcome! Click 'Next Step' to begin or 'Prev Step' to go back.";
                if(state.currentStepIndex === -1) prevStepBtn.disabled = true;
                return;
            }

            const currentStep = steps[state.currentStepIndex];
            annotationText.textContent = currentStep.annotation;
            
            // Call action for the current step if it exists
            // This was moved to handleStepForward to ensure it runs when step becomes current
            // if (currentStep.action) {
            //    currentStep.action(); // Apply state changes for this step
            // }

            updateVisuals();
            updateButtonStates();
        }

        function updateButtonStates() {
            prevStepBtn.disabled = state.history.length === 0;
            nextStepBtn.disabled = state.currentStepIndex >= steps.length - 1;

            const currentStepDef = steps[state.currentStepIndex];
            if (currentStepDef && currentStepDef.awaits) {
                // If current step is waiting for a button press, disable Next until that button is pressed
                // (or more accurately, until the flag it sets is true)
                if (!state[currentStepDef.awaits.flag] === currentStepDef.awaits.value) {
                     // This logic needs refinement. The flag check is tricky with awaits.
                }
            }
            
            // Disable action buttons if not relevant or conditions not met
            addCalciumBtn.disabled = state.calciumPresent;
            removeCalciumBtn.disabled = !state.calciumPresent;

            // ATP button logic: enable if current step implies ATP need
            let atpNeeded = false;
            if (state.currentStepIndex === 4 && !document.getElementById('atp-mol-1').classList.contains('visible')) atpNeeded = true; // For cocking
            if (state.currentStepIndex === 8 && !document.getElementById('atp-mol-1').classList.contains('visible')) atpNeeded = true; // For detachment
            
            // More general: enable if ATP is needed for *next* logical actions (cocking or detachment)
            // This can be complex. For now, enable if not used yet for current phase.
            if (state.myosinHeadState === 'resting' && state.tropomyosinShifted && !state.atpForCocking) {
                 // needs ATP for cocking
            } else if (state.myosinHeadState === 'power-stroke' && !state.atpForDetachment) {
                // needs ATP for detachment
            }
            // Simplification: enable supply ATP if it's not visibly present for current need
            supplyAtpBtn.disabled = (state.currentStepIndex === 4 && state.atpForCocking && document.getElementById('atp-mol-1').classList.contains('visible')) ||
                                  (state.currentStepIndex === 8 && state.atpForDetachment && document.getElementById('atp-mol-1').classList.contains('visible')) ||
                                  (!(state.currentStepIndex === 4 || state.currentStepIndex === 8));
             if(state.currentStepIndex === 4 || state.currentStepIndex === 8) supplyAtpBtn.disabled = false; else supplyAtpBtn.disabled = true;
             // If ATP already supplied for this stage, disable
             if(state.currentStepIndex === 4 && state.atpForCocking && document.getElementById('atp-mol-1').classList.contains('visible')) supplyAtpBtn.disabled = true;
             if(state.currentStepIndex === 8 && state.atpForDetachment && document.getElementById('atp-mol-1').classList.contains('visible')) supplyAtpBtn.disabled = true;


        }

        function handleStepForward() {
            if (state.currentStepIndex < steps.length - 1) {
                const currentStepDef = steps[state.currentStepIndex];
                
                // Check prerequisite for *current* step before advancing
                // Or, if current step awaits a user action that hasn't happened
                if (currentStepDef) {
                    if (currentStepDef.awaits && state[currentStepDef.awaits.flag] !== currentStepDef.awaits.value) {
                         annotationText.textContent = `Please click '${currentStepDef.awaits.button.textContent}' to proceed.`;
                         return; // Don't advance
                    }
                    if (currentStepDef.prerequisite && !currentStepDef.prerequisite()) {
                        // This case should ideally be handled by disabling Next or by specific user action steps
                        // For now, just prevent advance if a hard prerequisite isn't met
                        annotationText.textContent = currentStepDef.annotation + " (Condition not met to proceed)";
                        return;
                    }
                }

                state.history.push(JSON.parse(JSON.stringify(state)));
                state.currentStepIndex++;
                const nextStepDef = steps[state.currentStepIndex];
                if (nextStepDef.action) {
                    nextStepDef.action(); // Apply state changes for the new step
                }
                updateUI();
            }
        }

        function handleStepBackward() {
            if (state.history.length > 0) {
                state = JSON.parse(JSON.stringify(state.history.pop()));
                updateUI();
            }
        }
        
        addCalciumBtn.addEventListener('click', () => {
            if (!state.calciumPresent) {
                state.calciumPresent = true;
                // If current step is waiting for calcium, this fulfills it.
                // Auto-advance or let user click next. For now, let user click next.
                const currentStepDef = steps[state.currentStepIndex];
                 if (currentStepDef && currentStepDef.awaits && currentStepDef.awaits.flag === 'calciumPresent') {
                    // Condition met, user can now press "Next Step"
                 }
            }
            updateUI();
        });

        removeCalciumBtn.addEventListener('click', () => {
            if (state.calciumPresent) {
                state.calciumPresent = false;
                // This might trigger relaxation steps if the user is at an appropriate point
                // For simplicity, just set the flag. Next/Prev will guide.
                // If myosin bound, it should detach first. This model simplifies that.
                if (state.currentStepIndex >= 10) { // If in relaxation phase
                    state.tropomyosinShifted = false;
                    state.myosinHeadState = 'resting';
                    state.sarcomereContracted = 0;
                }
            }
            updateUI();
        });

        supplyAtpBtn.addEventListener('click', () => {
            // ATP becomes "available" visually. The step logic will "consume" it.
            atpMoleculeElement.classList.add('visible');
            if (state.currentStepIndex === 4) { // Supplying for cocking
                state.atpForCocking = true;
            } else if (state.currentStepIndex === 8) { // Supplying for detachment
                state.atpForDetachment = true;
            }
            updateUI();
        });

        prevStepBtn.addEventListener('click', handleStepBackward);
        nextStepBtn.addEventListener('click', handleStepForward);

        // Initial Setup
        function init() {
            state.currentStepIndex = 0; // Start at the first defined step
            if (steps[0] && steps[0].action) {
                steps[0].action(); // Initialize state for step 0
            }
            updateUI();
        }

        init();

    </script>
</body>
</html>
