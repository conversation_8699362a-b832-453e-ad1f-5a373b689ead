
import React, { useState } from 'react';
import type { Hotspot } from '../../types';
import Modal from './Modal';

interface InteractiveHotspotImageProps {
  imageUrl: string;
  altText: string;
  hotspots: Hotspot[];
}

const InteractiveHotspotImage: React.FC<InteractiveHotspotImageProps> = ({ imageUrl, altText, hotspots }) => {
  const [activeHotspot, setActiveHotspot] = useState<Hotspot | null>(null);

  return (
    <div className="relative w-full max-w-2xl mx-auto my-4">
      <img src={imageUrl} alt={altText} className="w-full h-auto rounded-lg shadow-lg" />
      {hotspots.map((hotspot) => (
        <button
          key={hotspot.id}
          className="absolute w-6 h-6 bg-red-500 rounded-full transform -translate-x-1/2 -translate-y-1/2 animate-pulse-slow border-2 border-white shadow-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300"
          style={{ top: `${hotspot.y}%`, left: `${hotspot.x}%` }}
          onClick={() => setActiveHotspot(hotspot)}
          aria-label={`Hotspot: ${hotspot.title}`}
        >
          <span className="sr-only">{hotspot.title}</span>
        </button>
      ))}
      
      <Modal isOpen={!!activeHotspot} onClose={() => setActiveHotspot(null)} title={activeHotspot?.title}>
        <p>{activeHotspot?.description}</p>
      </Modal>
    </div>
  );
};

export default InteractiveHotspotImage;
