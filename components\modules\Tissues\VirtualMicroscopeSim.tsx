
import React, { useState } from 'react';
import { TISSUE_TYPES } from '../../../constants';
import type { TissueType } from '../../../types';
import Button from '../../common/Button';

const VirtualMicroscopeSim: React.FC = () => {
  const [selectedSlide, setSelectedSlide] = useState<TissueType | null>(null);
  const [feedback, setFeedback] = useState('');

  const handleSelectSlide = (tissue: TissueType) => {
    setSelectedSlide(tissue);
    setFeedback('');
  };

  const handleIdentify = (guessName: string) => {
    if (selectedSlide) {
      if (guessName === selectedSlide.name) {
        setFeedback(`Correct! This is ${selectedSlide.name}.`);
      } else {
        setFeedback(`Incorrect. This is ${selectedSlide.name}, not ${guessName}. Try another or check the Tissue Explorer.`);
      }
    }
  };

  return (
    <div className="p-4 bg-gray-50 rounded-lg shadow-inner space-y-6">
      <p className="text-center text-textlight/80">
        Drag and drop (simulated by clicking) a tissue sample onto the "microscope stage" to view and identify it.
      </p>
      <div className="flex flex-col md:flex-row gap-6">
        {/* Slide Tray */}
        <div className="md:w-1/3 border border-gray-300 p-4 rounded-lg bg-white">
          <h4 className="font-semibold text-primary mb-2">Tissue Samples (Click to "Mount")</h4>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {TISSUE_TYPES.map(tissue => (
              <Button 
                key={tissue.name} 
                onClick={() => handleSelectSlide(tissue)}
                variant="outline"
                size="sm"
                className="w-full"
              >
                Sample: {tissue.name.substring(0,10)}...
              </Button>
            ))}
          </div>
        </div>

        {/* Microscope View & Identification */}
        <div className="md:w-2/3 border border-gray-300 p-4 rounded-lg bg-white">
          <h4 className="font-semibold text-primary mb-2">Microscope View</h4>
          {selectedSlide ? (
            <div className="text-center">
              <img src={selectedSlide.imageUrl} alt={`Microscopic view of ${selectedSlide.name}`} className="w-full max-w-md mx-auto h-64 object-cover rounded-lg border border-gray-200 mb-4" />
              <p className="mb-2 font-medium">Identify this tissue:</p>
              <div className="flex flex-wrap justify-center gap-2 mb-4">
                {TISSUE_TYPES.map(tissue => (
                  <Button key={`guess-${tissue.name}`} onClick={() => handleIdentify(tissue.name)} size="sm" variant="secondary">
                    {tissue.name}
                  </Button>
                ))}
              </div>
              {feedback && <p className={`p-2 rounded-md text-sm ${feedback.includes('Correct') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>{feedback}</p>}
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center text-gray-400">
              Select a sample to view
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VirtualMicroscopeSim;
