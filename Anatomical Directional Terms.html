<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Anatomical Terms</title>
    <style>
        :root {
            --bg-color: #f4f4f9;
            --text-color: #333;
            --primary-color: #007bff;
            --figure-base-color: #d0d0d0;
            --figure-stroke-color: #505050;
            --skeleton-color: #606060;
            --highlight-opacity: 0.75;
            --container-bg: #ffffff;
            --border-color: #ccc;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--bg-color);
            color: var(--text-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            box-sizing: border-box;
        }

        .app-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .app-header h1 {
            color: var(--primary-color);
            margin: 0;
        }

        .app-container {
            display: flex;
            flex-direction: row;
            gap: 20px;
            width: 100%;
            max-width: 900px;
            background-color: var(--container-bg);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .figure-container {
            flex: 2;
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 250px; /* Minimum width for the figure */
        }

        #anatomical-figure {
            width: 100%;
            max-width: 300px; /* Max width of SVG, will scale down */
            height: auto;
            stroke-linecap: round;
            stroke-linejoin: round;
        }

        .body-part {
            fill: var(--figure-base-color);
            stroke: var(--figure-stroke-color);
            stroke-width: 2;
        }

        .skeleton-part {
            fill: none;
            stroke: var(--skeleton-color);
            stroke-width: 2.5;
        }

        .highlight-area {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .terms-container {
            flex: 1;
            padding-left: 20px;
            border-left: 1px solid var(--border-color);
            min-width: 200px;
        }

        .terms-container h2 {
            margin-top: 0;
            color: var(--primary-color);
            font-size: 1.5em;
        }

        #terms-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        #terms-list li {
            padding: 10px 15px;
            margin-bottom: 8px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s ease, border-color 0.2s ease;
            font-weight: 500;
        }

        #terms-list li:hover, #terms-list li:focus {
            background-color: #e9ecef;
            border-color: var(--primary-color);
            outline: none;
        }

        #terms-list li.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            font-weight: bold;
        }

        #term-description {
            margin-top: 15px;
            font-style: italic;
            color: #555;
            min-height: 1.5em; /* Reserve space for description */
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .app-container {
                flex-direction: column;
                padding: 15px;
            }
            .terms-container {
                padding-left: 0;
                border-left: none;
                border-top: 1px solid var(--border-color);
                padding-top: 15px;
            }
            #anatomical-figure {
                max-width: 250px; /* Adjust for smaller screens */
            }
        }
         @media (max-width: 480px) {
            .app-header h1 {
                font-size: 1.8em;
            }
            .terms-container h2 {
                font-size: 1.3em;
            }
             #terms-list li {
                padding: 8px 12px;
            }
        }

    </style>
</head>
<body>

    <header class="app-header">
        <h1>Anatomical Directional Terms</h1>
        <p>Click a term to see it highlighted on the figure.</p>
    </header>

    <main class="app-container">
        <div class="figure-container">
            <svg id="anatomical-figure" viewBox="0 0 200 380" aria-labelledby="figure-title" role="img">
                <title id="figure-title">Human figure for anatomical terms</title>
                <desc id="figure-desc">A gender-neutral cartoon human figure in anatomical position with a simple skeleton overlay. Interactive areas highlight to show directional terms.</desc>

                <!-- Base Figure -->
                <g id="base-figure">
                    <!-- Head -->
                    <ellipse class="body-part" cx="100" cy="45" rx="30" ry="35"/>
                    <!-- Neck -->
                    <rect class="body-part" x="88" y="78" width="24" height="20"/>
                    <!-- Torso -->
                    <path class="body-part" d="M70,98 Q70,90 80,90 L120,90 Q130,90 130,98 L130,200 L70,200 Z"/>
                    <!-- Arms -->
                    <g id="left-arm">
                        <path class="body-part" d="M70,105 Q60,110 55,125 L45,180 Q40,195 50,200 L58,195 Q60,180 55,170 L65,115 Z"/> <!-- Left Arm -->
                        <ellipse class="body-part" cx="50" cy="200" rx="10" ry="15"/> <!-- Left Hand (palm forward) -->
                    </g>
                    <g id="right-arm">
                        <path class="body-part" d="M130,105 Q140,110 145,125 L155,180 Q160,195 150,200 L142,195 Q140,180 145,170 L135,115 Z"/> <!-- Right Arm -->
                        <ellipse class="body-part" cx="150" cy="200" rx="10" ry="15"/> <!-- Right Hand (palm forward) -->
                    </g>
                    <!-- Pelvis -->
                    <path class="body-part" d="M75,200 L125,200 L115,230 L85,230 Z"/>
                    <!-- Legs -->
                    <g id="left-leg">
                        <rect class="body-part" x="75" y="230" width="22" height="100"/>
                        <ellipse class="body-part" cx="86" cy="335" rx="12" ry="8"/> <!-- Left Foot -->
                    </g>
                    <g id="right-leg">
                        <rect class="body-part" x="103" y="230" width="22" height="100"/>
                        <ellipse class="body-part" cx="114" cy="335" rx="12" ry="8"/> <!-- Right Foot -->
                    </g>
                </g>

                <!-- Skeleton Overlay -->
                <g id="skeleton">
                    <!-- Skull -->
                    <ellipse class="skeleton-part" cx="100" cy="45" rx="25" ry="30" fill="none"/>
                    <!-- Spine -->
                    <line class="skeleton-part" x1="100" y1="80" x2="100" y2="215"/>
                    <!-- Ribs (simplified) -->
                    <path class="skeleton-part" d="M100,110 C90,115 80,130 80,145 M100,110 C110,115 120,130 120,145 M100,125 C90,130 83,145 83,160 M100,125 C110,130 117,145 117,160 M100,140 C90,145 85,160 85,175 M100,140 C110,145 115,160 115,175" fill="none"/>
                    <!-- Pelvis bone -->
                    <path class="skeleton-part" d="M85,205 Q100,195 115,205 L110,225 L90,225 Z" fill="none"/>
                    <!-- Arm bones -->
                    <line class="skeleton-part" x1="100" y1="105" x2="65" y2="115"/> <line class="skeleton-part" x1="65" y1="115" x2="50" y2="195"/> <!-- Left arm bones -->
                    <line class="skeleton-part" x1="100" y1="105" x2="135" y2="115"/> <line class="skeleton-part" x1="135" y1="115" x2="150" y2="195"/> <!-- Right arm bones -->
                    <!-- Leg bones -->
                    <line class="skeleton-part" x1="90" y1="220" x2="86" y2="325"/> <!-- Left leg bone -->
                    <line class="skeleton-part" x1="110" y1="220" x2="114" y2="325"/> <!-- Right leg bone -->
                </g>

                <!-- Highlight Areas -->
                <g id="highlight-layers">
                    <!-- Anterior: Front of the figure -->
                    <path id="highlight-anterior" class="highlight-area" fill="rgba(255, 87, 51, var(--highlight-opacity))" 
                          d="M100,10 A60,35 0 0 0 100,80 A60,35 0 0 0 100,10 Z  
                             M88,78 h24 v20 h-24 Z 
                             M70,98 Q70,90 80,90 L120,90 Q130,90 130,98 L130,200 L70,200 Z
                             M55,120 L45,180 Q40,195 50,200 L58,195 Q60,180 55,170 L65,110 Z
                             M145,120 L155,180 Q160,195 150,200 L142,195 Q140,180 145,170 L135,110 Z
                             M75,230 h22 v100 h-22 Z
                             M103,230 h22 v100 h-22 Z
                             M70,10 L130,10 L130,80 L70,80 Z
                             M88,78 h24 v20 h-24 Z
                             M70,98 h60 v102 h-60 Z
                             M50,110 L65,115 L55,170 L45,180 L40,195 L50,200 L58,195 L50,190 L45,175 Z
                             M135,115 L145,110 L155,170 L145,180 L150,195 L142,200 L150,190 L155,175 Z
                             M75,230 h22 v100 h-22 Z M103,230 h22 v100 h-22 Z
                             M70,10 L130,10 L130,80 L70,80 Z 
                             M88,78 h24 v20 h-24 Z 
                             M70,98 h60 v102 h-60 Z 
                             M40,105 h30 v95 h-30 Z 
                             M130,105 h30 v95 h-30 Z 
                             M75,200 h50 v30 h-50 Z 
                             M75,230 h22 v100 h-22 Z 
                             M103,230 h22 v100 h-22 Z
                             M70,10 C70,10 130,10 130,10 L130,80 C130,80 70,80 70,80 Z
                             M88,78 h24 v20 h-24 Z
                             M70,98 h60 v102 h-60 Z
                             M40,98 h30 v110 h-30 Z
                             M130,98 h30 v110 h-30 Z
                             M75,200 h50 v30 h-50 Z
                             M75,230 h22 v110 h-22 Z
                             M103,230 h22 v110 h-22 Z
                             M68,10 L132,10 L132,80 L68,80 Z
                             M87,78 h26 v21 h-26 Z
                             M68,97 h64 v104 h-64 Z
                             M38,97 h32 v112 h-32 Z
                             M130,97 h32 v112 h-32 Z
                             M73,198 h54 v33 h-54 Z
                             M73,228 h24 v112 h-24 Z
                             M103,228 h24 v112 h-24 Z
                             " />

                    <!-- Posterior: Spine and back of head/neck -->
                    <path id="highlight-posterior" class="highlight-area" fill="rgba(51, 102, 255, var(--highlight-opacity))" 
                          d="M100,10 A28,33 0 0 1 100,78 A28,33 0 0 1 100,10 M95,80 v135 h10 V80 Z" />

                    <!-- Superior: Head and neck -->
                    <path id="highlight-superior" class="highlight-area" fill="rgba(51, 204, 51, var(--highlight-opacity))" 
                          d="M100,10 A30,35 0 0 0 100,80 A30,35 0 0 0 100,10 Z M88,78 h24 v20 h-24 Z" />

                    <!-- Inferior: Pelvis and legs -->
                    <path id="highlight-inferior" class="highlight-area" fill="rgba(255, 204, 51, var(--highlight-opacity))" 
                          d="M75,200 L125,200 L115,230 L85,230 Z 
                             M75,230 h22 v100 h-22 Z M86,330 A12,8 0 1 0 86,340 A12,8 0 1 0 86,330 Z
                             M103,230 h22 v100 h-22 Z M114,330 A12,8 0 1 0 114,340 A12,8 0 1 0 114,330 Z"/>

                    <!-- Medial: Central axis (spine area) -->
                    <rect id="highlight-medial" class="highlight-area" fill="rgba(153, 51, 255, var(--highlight-opacity))" 
                          x="92" y="40" width="16" height="190"/>

                    <!-- Lateral: Arms -->
                    <path id="highlight-lateral" class="highlight-area" fill="rgba(51, 204, 204, var(--highlight-opacity))" 
                          d="M70,105 Q60,110 55,125 L45,180 Q40,195 50,200 L58,195 Q60,180 55,170 L65,115 Z M50,192 A10,15 0 1 0 50,208 A10,15 0 1 0 50,192 Z
                             M130,105 Q140,110 145,125 L155,180 Q160,195 150,200 L142,195 Q140,180 145,170 L135,115 Z M150,192 A10,15 0 1 0 150,208 A10,15 0 1 0 150,192 Z"/>
                </g>
            </svg>
        </div>

        <div class="terms-container">
            <h2>Directional Terms</h2>
            <ul id="terms-list" role="listbox">
                <li tabindex="0" role="option" data-term="anterior">Anterior (Ventral)</li>
                <li tabindex="0" role="option" data-term="posterior">Posterior (Dorsal)</li>
                <li tabindex="0" role="option" data-term="superior">Superior (Cranial)</li>
                <li tabindex="0" role="option" data-term="inferior">Inferior (Caudal)</li>
                <li tabindex="0" role="option" data-term="medial">Medial</li>
                <li tabindex="0" role="option" data-term="lateral">Lateral</li>
            </ul>
            <div id="term-description" aria-live="polite">Select a term to learn more.</div>
        </div>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const termsListItems = document.querySelectorAll('#terms-list li');
            const termDescription = document.getElementById('term-description');
            
            const highlightAnterior = document.getElementById('highlight-anterior');
            const highlightPosterior = document.getElementById('highlight-posterior');
            const highlightSuperior = document.getElementById('highlight-superior');
            const highlightInferior = document.getElementById('highlight-inferior');
            const highlightMedial = document.getElementById('highlight-medial');
            const highlightLateral = document.getElementById('highlight-lateral');

            const allHighlights = [
                highlightAnterior, highlightPosterior, highlightSuperior, 
                highlightInferior, highlightMedial, highlightLateral
            ];

            const termDetails = {
                anterior: { element: highlightAnterior, text: "Anterior (Ventral): Towards the front of the body." },
                posterior: { element: highlightPosterior, text: "Posterior (Dorsal): Towards the back of the body." },
                superior: { element: highlightSuperior, text: "Superior (Cranial): Towards the head or upper part of a structure." },
                inferior: { element: highlightInferior, text: "Inferior (Caudal): Towards the feet or lower part of a structure." },
                medial: { element: highlightMedial, text: "Medial: Towards the midline of the body." },
                lateral: { element: highlightLateral, text: "Lateral: Away from the midline of the body; on the outer side." }
            };

            function clearAllHighlights() {
                allHighlights.forEach(h => {
                    if (h) h.style.opacity = '0';
                });
                termsListItems.forEach(item => item.classList.remove('active'));
            }

            function activateTerm(termKey, termListItem) {
                clearAllHighlights();

                if (termDetails[termKey] && termDetails[termKey].element) {
                    termDetails[termKey].element.style.opacity = '1';
                    termDescription.textContent = termDetails[termKey].text;
                    if (termListItem) {
                        termListItem.classList.add('active');
                    }
                } else {
                    termDescription.textContent = "Select a term to learn more.";
                }
            }

            termsListItems.forEach(item => {
                item.addEventListener('click', () => {
                    const termKey = item.getAttribute('data-term');
                    activateTerm(termKey, item);
                });

                item.addEventListener('keydown', (event) => {
                    if (event.key === 'Enter' || event.key === ' ') {
                        event.preventDefault(); // Prevent page scroll on Space
                        const termKey = item.getAttribute('data-term');
                        activateTerm(termKey, item);
                    }
                });
            });

            // Path data for anterior highlight was complex and caused issues. 
            // Simplified for stability. The SVG part is tricky for complex shapes in pure path data.
            // The anterior path has been simplified significantly to avoid rendering issues with very long path data.
            // Ideally, complex highlight shapes are composed of multiple simpler shapes or optimized paths.
            // For the purpose of this exercise, the provided anterior path is a placeholder.
            // A more robust anterior highlight would combine the front surfaces of individual body parts.
            // Let's redefine anterior more simply:
            const anteriorHighlightPath = `
                M70,10 L130,10 L130,80 L70,80 Z /* Head front */
                M88,78 h24 v20 h-24 Z /* Neck front */
                M70,98 h60 v102 h-60 Z /* Torso front */
                M45,105 h20 v95 h-20 Z /* Left arm front part */
                M135,105 h20 v95 h-20 Z /* Right arm front part */
                M75,230 h22 v100 h-22 Z /* Left leg front */
                M103,230 h22 v100 h-22 Z /* Right leg front */
            `;
            if (highlightAnterior) {
                highlightAnterior.setAttribute('d', anteriorHighlightPath);
            }
        });
    </script>

</body>
</html>
