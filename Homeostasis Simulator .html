<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Homeostasis Simulator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            margin: 0;
            padding: 15px;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
        }
        .container {
            width: 100%;
            max-width: 700px;
            background: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        #narrative {
            margin-bottom: 25px;
            background-color: #eef7ff;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #5dade2;
            text-align: left;
            font-size: 0.95em;
        }
        #narrative p {
            margin: 0.5em 0;
        }
        #narrative ul {
            padding-left: 20px;
        }
        .controls {
            margin-bottom: 25px;
        }
        .controls label {
            font-weight: bold;
            margin-right: 8px;
            font-size: 0.9em;
        }
        #tempSlider {
            width: 60%;
            min-width: 180px;
            max-width: 300px;
            vertical-align: middle;
            cursor: grab;
        }
        #currentTempDisplay {
            font-weight: bold;
            margin-left: 10px;
            display: inline-block;
            min-width: 200px;
            padding: 8px 12px;
            background-color: #e8f6f3;
            border-radius: 5px;
            color: #1abc9c;
            font-size: 1em;
            border: 1px solid #d0ece7;
        }
        #organismCanvas {
            display: block;
            margin: 0 auto;
            background-color: #f0f8ff;
            border: 1px solid #d1e0e0;
            border-radius: 5px;
            max-width: 100%;
        }

        @media (max-width: 600px) {
            body { padding: 10px; }
            .container { padding: 15px; }
            h1 { font-size: 1.5em; }
            .controls label {
                display: block;
                margin-bottom: 5px;
            }
            #tempSlider { 
                width: calc(100% - 20px); /* Adjust for padding/margin */
                margin-top: 10px; 
            }
            #currentTempDisplay { 
                margin-left: 0; 
                margin-top: 10px; 
                display: block;
                width: fit-content;
                margin-left: auto;
                margin-right: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Homeostasis Simulator</h1>
        <div id="narrative">
            <p><strong>Welcome to the Homeostasis Simulator!</strong></p>
            <p>Homeostasis is an organism's ability to keep its internal conditions stable and optimal for survival, even when the external environment changes. Think of it like your body's internal thermostat.</p>
            <p><strong>How to use:</strong></p>
            <ul>
                <li>The stick figure represents an organism. Its ideal body temperature is <strong>37.0°C</strong>.</li>
                <li>Use the slider below to apply an external temperature influence. This will directly affect the organism's body temperature.</li>
                <li>Watch how the organism reacts:
                    <ul>
                        <li>If its temperature goes too high (above 37°C), it will start to sweat to cool down.</li>
                        <li>If its temperature goes too low (below 37°C), it will start to shiver to generate heat.</li>
                    </ul>
                </li>
                <li>The organism will automatically try to return its body temperature to the optimal 37.0°C.</li>
            </ul>
        </div>

        <div class="controls">
            <label for="tempSlider">External Temperature Influence:</label>
            <input type="range" id="tempSlider" min="30" max="44" value="37" step="0.1">
            <span id="currentTempDisplay">Body Temperature: 37.0°C</span>
        </div>

        <canvas id="organismCanvas"></canvas>
    </div>

    <script>
        const tempSlider = document.getElementById('tempSlider');
        const currentTempDisplay = document.getElementById('currentTempDisplay');
        const canvas = document.getElementById('organismCanvas');
        const ctx = canvas.getContext('2d');

        const OPTIMAL_TEMP = 37.0;
        const MIN_ALLOWED_TEMP = 30.0;
        const MAX_ALLOWED_TEMP = 44.0;
        const TEMP_ADJUST_RATE = 0.025; 
        const SHIVER_INTENSITY = 1.5; 
        const SWEAT_DROP_GENERATION_RATE = 5; 
        let sweatDropCounter = 0;

        let bodyTemperature = OPTIMAL_TEMP;
        let isShivering = false;
        let isSweating = false;
        let sweatDrops = [];

        let figureCenterX, figureBaseY, headRadius, bodyLength, limbLength;

        function setupCanvas() {
            const container = document.querySelector('.container');
            let canvasWidth = container.clientWidth * 0.95;
            if (canvasWidth > 500) canvasWidth = 500;
            if (canvasWidth < 280) canvasWidth = 280;

            canvas.width = canvasWidth;
            canvas.height = canvas.width * 0.75; 
        }

        function calculateFigureDimensions() {
            figureCenterX = canvas.width / 2;
            figureBaseY = canvas.height * 0.9; // Feet on ground
            
            const figureTotalHeight = canvas.height * 0.65;
            headRadius = figureTotalHeight * 0.15;
            bodyLength = figureTotalHeight * 0.35;
            limbLength = figureTotalHeight * 0.30; 
        }

        function drawStickFigure() {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = Math.max(2, canvas.width * 0.008);

            let currentShiverX = 0;
            let currentShiverY = 0;
            if (isShivering) {
                currentShiverX = (Math.random() - 0.5) * SHIVER_INTENSITY * 2;
                currentShiverY = (Math.random() - 0.5) * SHIVER_INTENSITY * 2;
            }
            
            const _headCenterY = figureBaseY - limbLength - bodyLength - headRadius;
            const _bodyTopY = _headCenterY + headRadius;
            const _bodyBottomY = _bodyTopY + bodyLength;
            const _shoulderY = _bodyTopY + headRadius * 0.3; 

            const finalFigureCenterX = figureCenterX + currentShiverX;
            const headCenterY = _headCenterY + currentShiverY;
            const bodyTopY = _bodyTopY + currentShiverY;
            const bodyBottomY = _bodyBottomY + currentShiverY;
            const shoulderY = _shoulderY + currentShiverY;

            ctx.beginPath(); // Head
            ctx.arc(finalFigureCenterX, headCenterY, headRadius, 0, Math.PI * 2);
            ctx.stroke();

            ctx.beginPath(); // Body
            ctx.moveTo(finalFigureCenterX, bodyTopY);
            ctx.lineTo(finalFigureCenterX, bodyBottomY);
            ctx.stroke();

            ctx.beginPath(); // Arms
            ctx.moveTo(finalFigureCenterX, shoulderY);
            ctx.lineTo(finalFigureCenterX - limbLength * 0.7, shoulderY + limbLength * 0.5);
            ctx.moveTo(finalFigureCenterX, shoulderY);
            ctx.lineTo(finalFigureCenterX + limbLength * 0.7, shoulderY + limbLength * 0.5);
            ctx.stroke();

            ctx.beginPath(); // Legs
            ctx.moveTo(finalFigureCenterX, bodyBottomY);
            ctx.lineTo(finalFigureCenterX - limbLength * 0.5, bodyBottomY + limbLength);
            ctx.moveTo(finalFigureCenterX, bodyBottomY);
            ctx.lineTo(finalFigureCenterX + limbLength * 0.5, bodyBottomY + limbLength);
            ctx.stroke();
        }

        function addSweatDrop() {
            const _headCenterY = figureBaseY - limbLength - bodyLength - headRadius;
            const _headTopYRelative = _headCenterY - headRadius;

            const shiverEffectX = isShivering ? (Math.random() - 0.5) * SHIVER_INTENSITY : 0; // smaller jitter for drop source
            const shiverEffectY = isShivering ? (Math.random() - 0.5) * SHIVER_INTENSITY : 0;

            const dropSourceX = figureCenterX + shiverEffectX + (Math.random() - 0.5) * headRadius * 1.5;
            const dropSourceY = _headTopYRelative + shiverEffectY + (Math.random() * headRadius * 2);

            sweatDrops.push({
                x: dropSourceX,
                y: dropSourceY,
                size: (Math.random() * 1.5 + 1.5) * (canvas.width / 500),
                speedY: (Math.random() * (canvas.height * 0.0015) + (canvas.height * 0.001)),
                opacity: 1.0
            });
        }

        function updateAndDrawSweatDrops() {
            if (!isSweating && sweatDrops.length === 0) return;

            sweatDropCounter++;
            if (isSweating && sweatDropCounter >= SWEAT_DROP_GENERATION_RATE) {
                addSweatDrop();
                sweatDropCounter = 0;
            }
            
            for (let i = sweatDrops.length - 1; i >= 0; i--) {
                let drop = sweatDrops[i];
                drop.y += drop.speedY;
                drop.opacity -= 0.015;

                if (drop.y > canvas.height || drop.opacity <= 0) {
                    sweatDrops.splice(i, 1);
                } else {
                    ctx.beginPath();
                    ctx.fillStyle = `rgba(0, 150, 255, ${drop.opacity})`;
                    ctx.arc(drop.x, drop.y, Math.max(0.5, drop.size), 0, Math.PI * 2);
                    ctx.fill();
                }
            }
        }

        function updateHomeostasis() {
            const difference = bodyTemperature - OPTIMAL_TEMP;
            isSweating = false;
            isShivering = false;
            
            if (difference > 0.05) { 
                isSweating = true;
                bodyTemperature -= TEMP_ADJUST_RATE;
                if (bodyTemperature < OPTIMAL_TEMP) bodyTemperature = OPTIMAL_TEMP;
            } else if (difference < -0.05) {
                isShivering = true;
                bodyTemperature += TEMP_ADJUST_RATE;
                if (bodyTemperature > OPTIMAL_TEMP) bodyTemperature = OPTIMAL_TEMP;
            } else {
                 if (Math.abs(bodyTemperature - OPTIMAL_TEMP) < TEMP_ADJUST_RATE) {
                     // If very close, and slider is not actively pushing it away, snap to optimal
                     const sliderValue = parseFloat(tempSlider.value);
                     if (Math.abs(sliderValue - OPTIMAL_TEMP) < 0.1 || Math.abs(bodyTemperature - sliderValue) > TEMP_ADJUST_RATE*2 ) {
                        bodyTemperature = OPTIMAL_TEMP;
                     }
                 }
            }
            bodyTemperature = Math.max(MIN_ALLOWED_TEMP, Math.min(MAX_ALLOWED_TEMP, bodyTemperature));
        }

        function gameLoop() {
            ctx.fillStyle = '#f0f8ff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            updateHomeostasis();
            
            drawStickFigure();
            updateAndDrawSweatDrops();

            currentTempDisplay.textContent = `Body Temperature: ${bodyTemperature.toFixed(1)}°C`;
            
            // If body temp was clamped by homeostasis, and slider is outside this, update slider
            // This can happen if user sets slider to e.g. 25 (not allowed) and it gets clamped to 30.
            // Or if homeostasis tries to adjust beyond limits (less likely with current logic).
            if (bodyTemperature === MIN_ALLOWED_TEMP && parseFloat(tempSlider.value) < MIN_ALLOWED_TEMP) {
                tempSlider.value = MIN_ALLOWED_TEMP.toFixed(1);
            } else if (bodyTemperature === MAX_ALLOWED_TEMP && parseFloat(tempSlider.value) > MAX_ALLOWED_TEMP) {
                tempSlider.value = MAX_ALLOWED_TEMP.toFixed(1);
            }


            requestAnimationFrame(gameLoop);
        }

        tempSlider.addEventListener('input', () => {
            bodyTemperature = parseFloat(tempSlider.value);
        });

        window.addEventListener('resize', () => {
            setupCanvas();
            calculateFigureDimensions();
        });

        window.onload = () => {
            setupCanvas();
            calculateFigureDimensions();
            currentTempDisplay.textContent = `Body Temperature: ${bodyTemperature.toFixed(1)}°C`;
            tempSlider.value = bodyTemperature.toFixed(1);
            gameLoop();
        };
    </script>
</body>
</html>
