<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Human Anatomy Explorer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 15px;
            background-color: #f4f4f9;
            color: #333;
            line-height: 1.6;
        }
        #app-container {
            width: 100%;
            max-width: 600px; /* Increased max-width for better viewing */
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            padding: 20px;
        }
        #controls {
            margin-bottom: 20px;
            display: flex;
            flex-direction: column; /* <PERSON>ack controls vertically on small screens */
            align-items: center;
            gap: 15px;
        }
        @media (min-width: 480px) { /* Horizontal layout for larger screens */
            #controls {
                flex-direction: row;
                justify-content: space-between;
            }
        }
        #organ-name-display {
            font-size: 1.4em;
            font-weight: bold;
            min-height: 1.5em;
            color: #2c3e50;
            text-align: center;
            padding: 5px 10px;
            border-radius: 5px;
            background-color: #ecf0f1;
            flex-grow: 1; /* Allow it to take available space */
        }
        #quiz-button {
            padding: 12px 20px;
            font-size: 1em;
            cursor: pointer;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        #quiz-button:hover {
            background-color: #2980b9;
        }
        #quiz-button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        #anatomy-svg-container {
            width: 100%;
            max-width: 500px; /* Max width for the SVG itself */
            margin: 0 auto; /* Center the SVG container */
        }
        svg#anatomy-diagram {
            width: 100%;
            height: auto;
            border: 1px solid #bdc3c7;
            background-color: #ffffff;
            border-radius: 5px;
        }
        .organ path, .organ ellipse, .organ rect {
            fill: #e0e0e0; /* Default organ color */
            stroke: #555;
            stroke-width: 1.5; /* Slightly thicker stroke for definition */
            cursor: pointer;
            transition: fill 0.2s ease-in-out, stroke 0.2s ease-in-out, stroke-width 0.2s ease-in-out;
        }
        .organ:hover path, .organ:hover ellipse, .organ:hover rect {
            fill: #d0d0d0; /* Hover color */
        }
        .organ.highlighted path, .organ.highlighted ellipse, .organ.highlighted rect {
            fill: #5dade2; /* Highlight color - a pleasant blue */
            stroke: #1a5276; /* Darker blue stroke for highlight */
            stroke-width: 2.5px;
        }
        .organ text {
            font-size: 10px; /* Base size, scales with viewBox */
            font-family: Arial, sans-serif;
            fill: #000;
            pointer-events: none; /* So text doesn't interfere with clicks on organ shape */
            text-anchor: middle;
            paint-order: stroke; /* Make text more readable over complex backgrounds */
            stroke: white;
            stroke-width: 0.3px; /* Thin white outline for text */
            stroke-linejoin: round;
        }
        .feedback-correct {
            color: #27ae60; /* Green for correct */
            font-weight: bold;
        }
        .feedback-incorrect {
            color: #c0392b; /* Red for incorrect */
            font-weight: bold;
        }
        .shake {
            animation: shake 0.4s cubic-bezier(.36,.07,.19,.97) both;
        }
        @keyframes shake {
            10%, 90% { transform: translate3d(-1px, 0, 0); }
            20%, 80% { transform: translate3d(2px, 0, 0); }
            30%, 50%, 70% { transform: translate3d(-3px, 0, 0); }
            40%, 60% { transform: translate3d(3px, 0, 0); }
        }
    </style>
</head>
<body>
    <div id="app-container">
        <div id="controls">
            <button id="quiz-button">Quiz Me</button>
            <div id="organ-name-display">Click an organ to learn its name.</div>
        </div>

        <div id="anatomy-svg-container">
            <svg id="anatomy-diagram" viewBox="0 0 400 600">
                <!-- Body Outline -->
                <path id="body-outline" d="M200,10 C150,10 120,40 120,80 L100,200 C95,350 110,550 200,580 C290,550 305,350 300,200 L280,80 C280,40 250,10 200,10 Z" fill="#f0e4d7" stroke="#795548" stroke-width="2"/>

                <!-- Organs (layered somewhat posterior to anterior, or by region) -->
                <g class="organ" id="brain" data-name="Brain">
                    <path d="M160,25 C150,30 150,60 170,70 C180,75 220,75 230,70 C250,60 250,30 240,25 C220,15 180,15 160,25 Z"/>
                    <text x="200" y="18" font-size="9px">Brain</text>
                </g>

                <g class="organ" id="kidneys" data-name="Kidneys">
                    <!-- Left Kidney -->
                    <path d="M145,330 C130,340 130,370 145,380 C160,375 160,335 145,330 Z"/>
                    <!-- Right Kidney -->
                    <path d="M255,330 C270,340 270,370 255,380 C240,375 240,335 255,330 Z"/>
                    <text x="200" y="320" font-size="9px">Kidneys</text>
                </g>

                <g class="organ" id="pancreas" data-name="Pancreas">
                    <path d="M160,310 C200,305 240,315 230,330 L170,330 C160,320 160,310 Z"/>
                    <text x="200" y="325" font-size="8px">Pancreas</text>
                </g>

                <g class="organ" id="lungs" data-name="Lungs">
                    <!-- Left Lung -->
                    <path d="M185,100 C130,100 120,150 120,190 C120,230 150,240 185,230 V100 Z"/>
                    <!-- Right Lung -->
                    <path d="M215,100 C270,100 280,150 280,190 C280,230 250,240 215,230 V100 Z"/>
                    <text x="200" y="95">Lungs</text>
                </g>

                <g class="organ" id="liver" data-name="Liver">
                    <path d="M200,240 C290,240 300,290 230,305 L150,300 C130,290 110,250 200,240 Z"/>
                    <text x="225" y="275">Liver</text>
                </g>
                
                <g class="organ" id="gallbladder" data-name="Gallbladder">
                    <ellipse cx="220" cy="295" rx="10" ry="15"/>
                    <text x="220" y="315" font-size="7px">Gallbladder</text>
                </g>

                <g class="organ" id="stomach" data-name="Stomach">
                    <path d="M130,250 C100,260 110,320 170,320 C200,320 210,260 160,250 Z"/>
                    <text x="160" y="280">Stomach</text>
                </g>

                <g class="organ" id="heart" data-name="Heart">
                    <path d="M200,160 C180,170 170,200 200,220 C230,200 220,170 200,160 Z"/>
                    <text x="200" y="190">Heart</text>
                </g>

                <g class="organ" id="large-intestine" data-name="Large Intestine">
                    <path d="M125,390 C115,480 285,480 275,390 L255,390 C265,460 135,460 145,390 Z M145,390 Q145,370 200,370 Q255,370 255,390 Z"/>
                    <text x="200" y="470" font-size="9px">Large Intestine</text>
                </g>

                <g class="organ" id="small-intestine" data-name="Small Intestine">
                    <path d="M150,395 C150,430 180,440 200,440 C220,440 250,430 250,395 C250,420 220,435 200,435 C180,435 150,420 150,395 Z M160,405 Q160,425 200,425 Q240,425 240,405 Q240,415 200,415 Q160,415 160,405 Z" />
                    <text x="200" y="415">Small Intestine</text>
                </g>

                <g class="organ" id="bladder" data-name="Bladder">
                    <path d="M170,490 C160,520 240,520 230,490 Q200,480 170,490 Z"/>
                    <text x="200" y="510">Bladder</text>
                </g>
            </svg>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const svg = document.getElementById('anatomy-diagram');
            const organNameDisplay = document.getElementById('organ-name-display');
            const quizButton = document.getElementById('quiz-button');
            const organElements = Array.from(svg.querySelectorAll('.organ'));

            let currentMode = 'learn'; // 'learn' or 'quiz'
            let quizTargetOrgan = null;
            let highlightedOrganElement = null;
            let interactionTimeout = null; // To manage timeouts for feedback

            const organsData = organElements.map(el => {
                return {
                    id: el.id,
                    name: el.dataset.name,
                    element: el,
                    labelElement: el.querySelector('text')
                };
            });

            function displayMessage(message, type = '') {
                organNameDisplay.textContent = message;
                organNameDisplay.className = 'organ-name-display'; // Reset class
                if (type) {
                    organNameDisplay.classList.add(`feedback-${type}`);
                }
            }

            function highlightOrgan(organData) {
                if (highlightedOrganElement) {
                    highlightedOrganElement.classList.remove('highlighted');
                }
                if (organData && organData.element) {
                    organData.element.classList.add('highlighted');
                    highlightedOrganElement = organData.element;
                } else {
                    highlightedOrganElement = null;
                }
            }
            
            function clearHighlight() {
                if (highlightedOrganElement) {
                    highlightedOrganElement.classList.remove('highlighted');
                    highlightedOrganElement = null;
                }
            }

            function handleOrganClick(event) {
                const clickedOrganElement = event.currentTarget;
                const organData = organsData.find(o => o.id === clickedOrganElement.id);

                if (!organData) return;
                
                clearTimeout(interactionTimeout); // Clear any pending feedback/reset

                if (currentMode === 'learn') {
                    displayMessage(organData.name);
                    highlightOrgan(organData);
                } else if (currentMode === 'quiz') {
                    if (quizTargetOrgan && organData.id === quizTargetOrgan.id) {
                        // Correct answer
                        if (quizTargetOrgan.labelElement) {
                            quizTargetOrgan.labelElement.textContent = quizTargetOrgan.name;
                        }
                        displayMessage(`Correct! It's the ${quizTargetOrgan.name}. ✓`, 'correct');
                        highlightOrgan(quizTargetOrgan);
                        
                        interactionTimeout = setTimeout(() => {
                            if (currentMode === 'quiz') {
                                setupNewQuestion();
                            }
                        }, 2000); // Delay for user to see "Correct!" then new question

                    } else {
                        // Incorrect answer
                        displayMessage('Incorrect. Try again!', 'incorrect');
                        clickedOrganElement.classList.add('shake');
                        interactionTimeout = setTimeout(() => {
                            clickedOrganElement.classList.remove('shake');
                            // Restore quiz prompt if needed, or leave "Incorrect" for a bit
                            if (currentMode === 'quiz' && quizTargetOrgan) {
                                displayMessage(`Find the organ with "???" label.`);
                            }
                        }, 1500); // Shake duration and message reset
                    }
                }
            }

            function setupNewQuestion() {
                clearHighlight();
                displayMessage('Find the organ with "???" label.');

                organsData.forEach(org => {
                    if (org.labelElement) {
                         org.labelElement.textContent = org.name;
                    }
                });

                let availableOrgans = organsData;
                if (quizTargetOrgan) { 
                    availableOrgans = organsData.filter(o => o.id !== quizTargetOrgan.id);
                    if (availableOrgans.length === 0) { // If all organs quizzed once or only one left
                        availableOrgans = organsData; 
                    }
                }
                
                const randomIndex = Math.floor(Math.random() * availableOrgans.length);
                quizTargetOrgan = availableOrgans[randomIndex];

                if (quizTargetOrgan && quizTargetOrgan.labelElement) {
                    quizTargetOrgan.labelElement.textContent = '???';
                }
            }

            function startQuiz() {
                currentMode = 'quiz';
                quizButton.textContent = 'Back to Learn Mode';
                clearHighlight();
                setupNewQuestion();
            }
            
            function resetToLearnMode() {
                currentMode = 'learn';
                quizButton.textContent = 'Quiz Me';
                clearHighlight();
                clearTimeout(interactionTimeout); 
                organsData.forEach(org => {
                    if (org.labelElement) {
                        org.labelElement.textContent = org.name;
                    }
                });
                quizTargetOrgan = null;
                displayMessage('Click an organ to learn its name.');
            }

            organElements.forEach(organEl => {
                organEl.addEventListener('click', handleOrganClick);
            });

            quizButton.addEventListener('click', () => {
                if (currentMode === 'learn') {
                    startQuiz();
                } else {
                    resetToLearnMode();
                }
            });
            
            // Initial state
            resetToLearnMode(); // Sets up learn mode correctly
        });
    </script>
</body>
</html>
