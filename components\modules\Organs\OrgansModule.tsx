
import React from 'react';
import InteractiveOrganDissector from './InteractiveOrganDissector';
import Card from '../../common/Card';
import Quiz from '../../common/Quiz';
import { ORGANS_DATA, SAMPLE_QUIZ_QUESTIONS } from '../../../constants';

interface OrgansModuleProps {
  id: string;
}

const OrgansModule: React.FC<OrgansModuleProps> = ({ id }) => {
  // Placeholder for organ-specific quiz questions
  const organQuizQuestions = SAMPLE_QUIZ_QUESTIONS.slice(0,1); 

  return (
    <section id={id} className="py-12 md:py-16 bg-gradient-to-b from-accent/10 to-background/10">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-primary mb-12 text-center">Module 3: Organs – Functional Units</h2>
        
        <Card title="Definition of an Organ" className="mb-12">
          <p className="text-lg text-textlight/90">
            An organ is a structure composed of two or more different types of tissues that work together to perform one or more specific functions. Organs are the body's recognizable structures (e.g., heart, lungs, liver, stomach) that perform vital roles.
          </p>
        </Card>

        <InteractiveOrganDissector organs={ORGANS_DATA} />

        <Card title="Animated Video: Organ Teamwork (Placeholder)" className="my-8">
            <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">Placeholder for animated video demonstrating how different tissues within an organ (e.g., the heart) work together.</p>
            </div>
        </Card>
        
        <Quiz questions={organQuizQuestions} moduleId="organs" />
      </div>
    </section>
  );
};

export default OrgansModule;
