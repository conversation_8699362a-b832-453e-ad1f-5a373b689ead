<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anatomical Directional Terms Explorer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            margin: 0;
            background-color: #f0f4f8;
            display: flex;
            justify-content: center;
            align-items: flex-start; /* Align to top for better scroll on small screens */
            min-height: 100vh;
            padding: 20px;
            box-sizing: border-box;
            color: #333;
        }

        .container {
            background-color: #ffffff;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 900px;
            width: 100%;
        }

        h1 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 25px;
            font-size: 1.8em;
        }

        .controls {
            margin-bottom: 25px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .controls label {
            font-weight: 500;
            color: #34495e;
        }

        select {
            padding: 12px 15px;
            font-size: 1em;
            border-radius: 6px;
            border: 1px solid #bdc3c7;
            background-color: #fff;
            min-width: 250px;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        select:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .display-area {
            display: flex;
            flex-direction: column; /* Stack on small screens */
            align-items: center;
            gap: 25px;
        }

        .svg-container {
            flex: 1 1 auto; /* Allow shrinking but prioritize content size */
            min-width: 280px; /* Minimum width for the SVG */
            max-width: 350px; /* Maximum width for the SVG */
            width: 100%; /* Responsive width */
            margin: 0 auto;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fdfdfd;
        }

        #anatomical-figure {
            width: 100%;
            height: auto; /* Maintain aspect ratio */
        }

        #definition-box {
            flex: 1 1 auto; /* Allow shrinking but prioritize content size */
            background-color: #eaf2f8; /* Light blue tint */
            padding: 20px;
            border-radius: 8px;
            text-align: left;
            min-height: 80px; /* Ensure it has some height even when empty */
            width: 100%;
            max-width: 500px; /* Max width for definition box */
            box-sizing: border-box;
        }

        #definition-text {
            margin: 0;
            font-size: 1em;
            line-height: 1.6;
            color: #2c3e50;
        }

        /* Responsive adjustments */
        @media (min-width: 768px) {
            .display-area {
                flex-direction: row; /* Side-by-side on larger screens */
                align-items: flex-start;
            }
            .svg-container {
                 /* Don't set fixed flex-basis to allow natural sizing */
            }
            #definition-box {
                max-width: none; /* Allow it to take available space */
            }
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>Anatomical Directional Terms</h1>
        <div class="controls">
            <label for="term-select">Select a term:</label>
            <select id="term-select">
                <option value="">-- Select --</option>
                <option value="Anterior">Anterior</option>
                <option value="Posterior">Posterior</option>
                <option value="Superior">Superior (Cranial)</option>
                <option value="Inferior">Inferior (Caudal)</option>
                <option value="Medial">Medial</option>
                <option value="Lateral">Lateral</option>
                <option value="Proximal">Proximal</option>
                <option value="Distal">Distal</option>
            </select>
        </div>

        <div class="display-area">
            <div class="svg-container">
                <svg id="anatomical-figure" viewBox="0 0 200 400" preserveAspectRatio="xMidYMid meet">
                    <!-- Default style for all parts -->
                    <defs>
                        <style>
                            .body-part {
                                fill: #E0E0E0; /* Light gray */
                                stroke: #555555; /* Dark gray */
                                stroke-width: 1;
                                transition: fill 0.3s ease, stroke 0.3s ease;
                            }
                        </style>
                    </defs>
                    <!-- Body Parts -->
                    <circle id="s-head" class="body-part" cx="100" cy="50" r="30" />
                    <rect id="s-torso" class="body-part" x="75" y="85" width="50" height="110" rx="5" />

                    <!-- Left Arm -->
                    <rect id="s-arm-left-upper" class="body-part" x="45" y="90" width="25" height="50" rx="5" />
                    <rect id="s-arm-left-lower" class="body-part" x="45" y="145" width="25" height="50" rx="5" />
                    <ellipse id="s-hand-left" class="body-part" cx="57.5" cy="200" rx="10" ry="8" />

                    <!-- Right Arm -->
                    <rect id="s-arm-right-upper" class="body-part" x="130" y="90" width="25" height="50" rx="5" />
                    <rect id="s-arm-right-lower" class="body-part" x="130" y="145" width="25" height="50" rx="5" />
                    <ellipse id="s-hand-right" class="body-part" cx="142.5" cy="200" rx="10" ry="8" />

                    <!-- Left Leg -->
                    <rect id="s-leg-left-upper" class="body-part" x="70" y="200" width="25" height="70" rx="5" />
                    <rect id="s-leg-left-lower" class="body-part" x="70" y="275" width="25" height="70" rx="5" />
                    <ellipse id="s-foot-left" class="body-part" cx="82.5" cy="350" rx="15" ry="10" />

                    <!-- Right Leg -->
                    <rect id="s-leg-right-upper" class="body-part" x="105" y="200" width="25" height="70" rx="5" />
                    <rect id="s-leg-right-lower" class="body-part" x="105" y="275" width="25" height="70" rx="5" />
                    <ellipse id="s-foot-right" class="body-part" cx="117.5" cy="350" rx="15" ry="10" />
                </svg>
            </div>
            <div id="definition-box">
                <p id="definition-text">Select a term to see its definition and visual representation.</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const termSelect = document.getElementById('term-select');
            const definitionTextElement = document.getElementById('definition-text');

            const defaultFillColor = '#E0E0E0';
            const defaultStrokeColor = '#555555';
            const highlightFillColor = '#87CEFA'; // Light Sky Blue
            const highlightStrokeColor = '#4682B4'; // Steel Blue

            const allPartIds = [
                's-head', 's-torso',
                's-arm-left-upper', 's-arm-left-lower', 's-hand-left',
                's-arm-right-upper', 's-arm-right-lower', 's-hand-right',
                's-leg-left-upper', 's-leg-left-lower', 's-foot-left',
                's-leg-right-upper', 's-leg-right-lower', 's-foot-right'
            ];

            const svgParts = {};
            allPartIds.forEach(id => {
                svgParts[id] = document.getElementById(id);
            });

            const definitions = {
                "Anterior": "Towards the front of the body (e.g., the breastbone is anterior to the spine).",
                "Posterior": "Towards the back of the body (e.g., the heart is posterior to the breastbone).",
                "Superior": "Towards the head end or upper part of a structure or the body; above (e.g., the head is superior to the abdomen).",
                "Inferior": "Away from the head end or towards the lower part of a structure or the body; below (e.g., the navel is inferior to the chin).",
                "Medial": "Towards or at the midline of the body; on the inner side of (e.g., the heart is medial to the arm).",
                "Lateral": "Away from the midline of the body; on the outer side of (e.g., the arms are lateral to the chest).",
                "Proximal": "Closer to the origin of the body part or the point of attachment of a limb to the body trunk (e.g., the elbow is proximal to the wrist).",
                "Distal": "Farther from the origin of a body part or the point of attachment of a limb to the body trunk (e.g., the knee is distal to the thigh)."
            };

            function clearHighlights() {
                allPartIds.forEach(id => {
                    if (svgParts[id]) {
                        svgParts[id].setAttribute('fill', defaultFillColor);
                        svgParts[id].setAttribute('stroke', defaultStrokeColor);
                        svgParts[id].setAttribute('stroke-width', '1');
                    }
                });
            }

            function applyHighlight(partIdArray) {
                partIdArray.forEach(id => {
                    if (svgParts[id]) {
                        svgParts[id].setAttribute('fill', highlightFillColor);
                        svgParts[id].setAttribute('stroke', highlightStrokeColor);
                        svgParts[id].setAttribute('stroke-width', '2');
                    }
                });
            }

            const highlightLogic = {
                "Anterior": () => applyHighlight(allPartIds), // Entire front view represents anterior
                "Posterior": () => applyHighlight(allPartIds), // Entire figure highlighted to represent posterior concept
                "Superior": () => applyHighlight(['s-head', 's-arm-left-upper', 's-arm-right-upper', 's-torso']), // Highlight upper parts
                "Inferior": () => applyHighlight(['s-leg-left-upper', 's-leg-left-lower', 's-foot-left', 's-leg-right-upper', 's-leg-right-lower', 's-foot-right']),
                "Medial": () => applyHighlight(['s-torso', 's-leg-left-upper', 's-leg-right-upper']), // Parts closest to midline
                "Lateral": () => applyHighlight([
                    's-arm-left-upper', 's-arm-left-lower', 's-hand-left',
                    's-arm-right-upper', 's-arm-right-lower', 's-hand-right',
                    // Optionally include outer aspects of legs if distinct parts existed
                    // For simplicity, arms are clearly lateral.
                ]),
                "Proximal": () => applyHighlight([ // Showing for both left arm and left leg as examples
                    's-arm-left-upper', 's-arm-right-upper',
                    's-leg-left-upper', 's-leg-right-upper'
                ]),
                "Distal": () => applyHighlight([ // Showing for both left arm and left leg as examples
                    's-arm-left-lower', 's-hand-left', 's-arm-right-lower', 's-hand-right',
                    's-leg-left-lower', 's-foot-left', 's-leg-right-lower', 's-foot-right'
                ])
            };
            
            // Refined Superior/Inferior logic for clarity
            highlightLogic["Superior"] = () => applyHighlight(['s-head', 's-arm-left-upper', 's-arm-right-upper']); // Head and upper arms
            // For torso, it's more complex to highlight only upper part without more SVG elements or clip-paths.
            // So, let's adjust Superior to be head + upper part of torso conceptually. Highlighting head is clearest.
            // Let's stick to: Head and shoulders (upper arms) for Superior
            highlightLogic["Superior"] = () => applyHighlight(['s-head', 's-arm-left-upper', 's-arm-right-upper']);
            // For Inferior, it's fine to highlight all leg parts.

            // Refined Medial/Lateral
            highlightLogic["Medial"] = () => applyHighlight(['s-torso']); // Torso is the most central medial structure
            highlightLogic["Lateral"] = () => applyHighlight([ // Full arms are lateral to the torso
                's-arm-left-upper', 's-arm-left-lower', 's-hand-left',
                's-arm-right-upper', 's-arm-right-lower', 's-hand-right'
            ]);

            // For proximal/distal, demonstrating on one side is often clearer, but requirements imply general.
            // Let's highlight both sides for proximal/distal to be comprehensive.
            // Proximal: Upper segments of limbs
            highlightLogic["Proximal"] = () => applyHighlight([
                's-arm-left-upper', 's-arm-right-upper',
                's-leg-left-upper', 's-leg-right-upper'
            ]);
            // Distal: Lower segments of limbs including hands/feet
            highlightLogic["Distal"] = () => applyHighlight([
                's-arm-left-lower', 's-hand-left',
                's-arm-right-lower', 's-hand-right',
                's-leg-left-lower', 's-foot-left',
                's-leg-right-lower', 's-foot-right'
            ]);


            termSelect.addEventListener('change', function() {
                const selectedTerm = this.value;
                clearHighlights();
                
                if (selectedTerm && definitions[selectedTerm]) {
                    definitionTextElement.textContent = definitions[selectedTerm];
                    if (highlightLogic[selectedTerm]) {
                        highlightLogic[selectedTerm]();
                    }
                } else {
                    definitionTextElement.textContent = 'Select a term to see its definition and visual representation.';
                }
            });
        });
    </script>
</body>
</html>
