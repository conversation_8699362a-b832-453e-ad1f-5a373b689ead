<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organ Systems & Homeostasis Explorer</title>
    <style>
        /* Basic Reset */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            color: #333;
            background-color: #f4f7f6; /* Light grayish-green background */
            padding: 20px;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #00796b; /* Teal accent */
        }

        header h1 {
            color: #004d40; /* Darker Teal */
            font-size: 2em;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .app-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            background-color: #ffffff; /* White background for the main app area */
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .body-map-container {
            flex: 1 1 300px; /* Grow, shrink, base width 300px */
            min-width: 280px;
            max-width: 400px; /* Prevent SVG from becoming too large on wide screens */
            margin: 0 auto; /* Center if space allows and it's not flexing fully */
            padding: 10px;
            border: 1px solid #e0e0e0; /* Lighter border */
            border-radius: 8px;
            background-color: #fdfdfd; /* Slightly off-white for the map background */
        }

        .body-map-container svg {
            width: 100%;
            height: auto;
            display: block;
        }

        .info-panel {
            flex: 2 1 350px; /* Grow more, shrink, base width 350px */
            min-width: 280px;
            padding: 20px;
            background-color: #e8f5e9; /* Light green background, distinct from page */
            border-radius: 8px;
            border: 1px solid #c8e6c9; /* Border matching background theme */
        }

        .info-panel h2 {
            color: #1b5e20; /* Dark Green */
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        .info-panel h3 {
            color: #2e7d32; /* Medium Green */
            margin-top: 20px;
            margin-bottom: 8px;
            font-size: 1.2em;
            border-bottom: 1px solid #a5d6a7;
            padding-bottom: 4px;
        }

        .info-panel p, .info-panel ul {
            margin-bottom: 12px;
            font-size: 0.95em;
        }

        .info-panel ul {
            list-style-position: inside;
            padding-left: 10px;
        }
        .info-panel ul li {
            margin-bottom: 5px;
        }

        #initialMessage {
            color: #555;
            font-style: italic;
            text-align: center;
            padding: 20px 0;
        }

        .any-cell-container {
            background-color: #ffffff; /* White background */
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05); /* Softer shadow */
        }

        .any-cell-container h2 {
            color: #004d40; /* Dark Teal, consistent with header */
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        .any-cell-container h3 {
            color: #00695c; /* Teal */
            margin-top: 15px;
            margin-bottom: 8px;
            font-size: 1.2em;
        }
        .any-cell-container ul {
            list-style-type: disc;
            list-style-position: inside;
            padding-left: 10px;
            font-size: 0.95em;
        }
        .any-cell-container ul li {
            margin-bottom: 6px;
        }
        .any-cell-container ul li b {
            color: #004d40; /* Consistent dark teal for emphasis */
        }


        footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #ccc;
            font-size: 0.9em;
            color: #777;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            header h1 {
                font-size: 1.8em;
            }
            .app-container {
                flex-direction: column;
                padding: 15px;
            }
            .body-map-container {
                max-width: 100%; /* Allow full width on mobile */
                margin-bottom: 20px;
            }
            .info-panel {
                min-height: 200px; /* Ensure it has some height */
            }
            .info-panel h2, .any-cell-container h2 {
                font-size: 1.3em;
            }
            .info-panel h3, .any-cell-container h3 {
                font-size: 1.1em;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>Organ Systems & Homeostasis Explorer</h1>
    </header>

    <div class="main-content">
        <div class="app-container">
            <div class="body-map-container">
                <svg id="humanBodySVG" viewBox="0 0 300 600" preserveAspectRatio="xMidYMid meet">
                    <defs>
                        <style>
                            .organ-area {
                                fill: rgba(128, 128, 128, 0.25); /* Default semi-transparent fill */
                                stroke: #777; /* Default stroke */
                                stroke-width: 1px;
                                cursor: pointer;
                                transition: fill 0.2s ease-in-out, stroke 0.2s ease-in-out, stroke-width 0.2s ease-in-out;
                            }
                            .organ-area:hover {
                                fill: rgba(100, 100, 100, 0.45); /* Darker on hover */
                                stroke: #333; /* Darker stroke on hover */
                            }
                            .organ-area.active {
                                /* Active fill color is set by JavaScript */
                                stroke: #000000; /* Black stroke for active selection */
                                stroke-width: 2px; /* Bolder stroke for active selection */
                            }
                            #body-outline {
                                pointer-events: none; /* Make sure the base outline is not clickable */
                            }
                        </style>
                    </defs>

                    <!-- Body Outline (decorative, not interactive) -->
                    <path id="body-outline" d="M150 30 C120 50 100 80 95 120 L85 250 Q85 300 100 380 L110 500 Q125 570 150 580 Q175 570 190 500 L200 380 Q215 300 215 250 L205 120 Q200 80 180 50 C170 35 150 30 150 30 Z" fill="#E9E9E9" stroke="#BDBDBD" stroke-width="2"/>

                    <!-- Clickable Organ System Areas -->
                    <!-- Order matters for click priority if they overlap. Last defined is on top. -->

                    <!-- 1. Digestive System (Abdomen) -->
                    <rect data-system="digestive" class="organ-area" x="100" y="240" width="100" height="130" rx="10" ry="10"/>

                    <!-- 2. Urinary System (Lower Abdomen) -->
                    <rect data-system="urinary" class="organ-area" x="115" y="355" width="70" height="65" rx="10" ry="10"/>

                    <!-- 3. Respiratory System (Lungs/Chest area - two separate shapes for lungs) -->
                    <path data-system="respiratory" class="organ-area" d="M100 130 Q85 180 100 235 L135 235 L135 125 Z"/> <!-- Left Lung -->
                    <path data-system="respiratory" class="organ-area" d="M200 130 Q215 180 200 235 L165 235 L165 125 Z"/> <!-- Right Lung -->

                    <!-- 4. Cardiovascular System (Heart, central in chest) -->
                    <ellipse data-system="cardiovascular" class="organ-area" cx="150" cy="185" rx="38" ry="48"/>

                    <!-- 5. Endocrine System (Symbolic spots: Head/Neck for Pituitary/Thyroid, Abdomen for Pancreas/Adrenals) -->
                    <circle data-system="endocrine" class="organ-area" cx="150" cy="110" r="15"/> 
                    <rect data-system="endocrine" class="organ-area" x="135" y="230" width="30" height="35" rx="5"/>

                    <!-- 6. Lymphatic System (Symbolic spots for major lymph node groups: Neck, Axillary, Inguinal) -->
                    <circle data-system="lymphatic" class="organ-area" cx="120" cy="120" r="10"/> 
                    <circle data-system="lymphatic" class="organ-area" cx="180" cy="120" r="10"/> 
                    <circle data-system="lymphatic" class="organ-area" cx="95" cy="175" r="12"/>  
                    <circle data-system="lymphatic" class="organ-area" cx="205" cy="175" r="12"/> 
                    <circle data-system="lymphatic" class="organ-area" cx="115" cy="380" r="12"/> 
                    <circle data-system="lymphatic" class="organ-area" cx="185" cy="380" r="12"/> 
                </svg>
            </div>

            <div class="info-panel">
                <h2 id="systemName">Welcome!</h2>
                <p id="initialMessage">Select an organ system on the diagram to learn about its function, role in homeostasis, and related blood values.</p>
                <div class="details" style="display: none;"> <!-- Details are hidden initially -->
                    <h3>Function:</h3>
                    <div id="systemDescription"></div>
                    <h3>Role in Homeostasis:</h3>
                    <div id="homeostasisRole"></div>
                    <h3>Blood Values Maintained/Affected:</h3>
                    <ul id="bloodValuesList"></ul>
                </div>
            </div>
        </div>

        <div class="any-cell-container">
            <h2>What "Any Cell" Needs</h2>
            <div id="cellNeedsContent">
                <h3>For Basic Function & Survival:</h3>
                <ul id="cellNeedsFunction"></ul>
                <h3>For Building & Repair:</h3>
                <ul id="cellNeedsBuild"></ul>
                <h3>For Energy Production:</h3>
                <ul id="cellNeedsEnergy"></ul>
            </div>
        </div>
    </div>

    <footer>
        <p>Interactive Homeostasis Explorer</p>
    </footer>

    <script>
        const organSystemsData = {
            cardiovascular: {
                name: "Cardiovascular System",
                description: "Consists of the heart, blood vessels (arteries, veins, capillaries), and blood. Its primary function is to pump and transport oxygen, nutrients, hormones, and other essential substances to cells throughout the body, and remove waste products like carbon dioxide.",
                homeostasis: "Maintains homeostasis by ensuring adequate blood flow and pressure, delivering oxygen and nutrients, removing wastes, distributing heat, and transporting hormones. It's vital for regulating blood pressure, blood volume, and body temperature.",
                bloodValues: ["Blood Pressure", "Oxygen (O<sub>2</sub>) transport", "Carbon Dioxide (CO<sub>2</sub>) transport", "Nutrient delivery (e.g., glucose, amino acids)", "Hormone transport", "Waste removal (transport to kidneys/lungs)"],
                fillColor: "rgba(229, 57, 53, 0.65)" // Material Red (slightly more opaque)
            },
            respiratory: {
                name: "Respiratory System",
                description: "Includes the lungs, airways (trachea, bronchi), and diaphragm. Its main function is to facilitate gas exchange: taking in oxygen from the atmosphere and expelling carbon dioxide from the body.",
                homeostasis: "Maintains stable levels of oxygen and carbon dioxide in the blood, which directly impacts blood pH (acid-base balance). It ensures cells receive enough oxygen for cellular respiration and removes CO<sub>2</sub>, a metabolic waste.",
                bloodValues: ["Blood Oxygen (O<sub>2</sub>) levels", "Blood Carbon Dioxide (CO<sub>2</sub>) levels", "Blood pH (via CO<sub>2</sub> regulation)"],
                fillColor: "rgba(30, 136, 229, 0.65)" // Material Blue
            },
            urinary: {
                name: "Urinary System",
                description: "Composed of the kidneys, ureters, bladder, and urethra. Its primary role is to filter blood, remove waste products (like urea and uric acid), and produce urine to excrete these wastes.",
                homeostasis: "Regulates blood volume and pressure (via water balance), controls levels of electrolytes (e.g., sodium, potassium, calcium) and metabolites, maintains fluid balance, and helps regulate blood pH by excreting hydrogen ions or conserving bicarbonate.",
                bloodValues: ["Water balance (blood volume/pressure)", "Electrolyte levels (Na<sup>+</sup>, K<sup>+</sup>, Ca<sup>2+</sup>, etc.)", "Blood pH (acid-base balance)", "Waste product levels (urea, creatinine)"],
                fillColor: "rgba(251, 192, 45, 0.65)" // Material Amber/Yellow
            },
            endocrine: {
                name: "Endocrine System",
                description: "Consists of glands (e.g., pituitary, thyroid, adrenals, pancreas) that produce and secrete hormones. Hormones are chemical messengers that travel through the bloodstream to target cells and organs, regulating various bodily functions.",
                homeostasis: "Regulates metabolism, growth and development, tissue function, sexual function, reproduction, sleep, and mood through hormonal control. For example, insulin (from pancreas) regulates blood glucose; ADH (from pituitary) controls water reabsorption by kidneys.",
                bloodValues: ["Blood Glucose levels (e.g., via insulin, glucagon)", "Hormone levels (various systemic effects)", "Calcium levels (via PTH/calcitonin)", "Sodium/Potassium balance (via aldosterone)"],
                fillColor: "rgba(123, 31, 162, 0.65)" // Material Purple
            },
            lymphatic: {
                name: "Lymphatic System",
                description: "A network of tissues, vessels, and organs (like lymph nodes, spleen, thymus) that work together to return fluid (lymph) leaked from blood vessels back into the bloodstream. It also plays a crucial role in the immune response.",
                homeostasis: "Maintains fluid balance in tissues by draining excess interstitial fluid, preventing edema. It transports fats absorbed from the digestive system. Its immune cells (lymphocytes) protect the body against pathogens, contributing to overall health and stability.",
                bloodValues: ["Interstitial fluid balance (indirectly blood volume)", "Fat transport (chylomicrons)", "Immune cell circulation and pathogen clearance (indirectly blood health)"],
                fillColor: "rgba(67, 160, 71, 0.65)" // Material Green
            },
            digestive: {
                name: "Digestive System",
                description: "Comprises the gastrointestinal tract (mouth, esophagus, stomach, small and large intestines) and accessory organs (liver, pancreas, gallbladder). It's responsible for breaking down food into absorbable nutrients and eliminating solid waste.",
                homeostasis: "Provides the body with essential nutrients (glucose, amino acids, fatty acids), water, and electrolytes required for energy, growth, and repair by digesting food and absorbing these substances into the bloodstream. It influences blood sugar levels post-meal.",
                bloodValues: ["Nutrient absorption (glucose, amino acids, fatty acids levels post-absorption)", "Water absorption (influences blood volume)", "Electrolyte absorption", "Vitamin/Mineral absorption"],
                fillColor: "rgba(109, 76, 65, 0.65)" // Material Brown
            }
        };

        const cellNeedsData = {
            function: [
                "<b>Oxygen (O<sub>2</sub>):</b> Essential for aerobic cellular respiration, the primary way cells generate ATP (energy).",
                "<b>Glucose (or other fuels):</b> Primary energy source, broken down to release energy.",
                "<b>Water:</b> Acts as a solvent, maintains cell turgor, and is involved in many metabolic reactions.",
                "<b>Ions (Electrolytes like Na<sup>+</sup>, K<sup>+</sup>, Ca<sup>2+</sup>, Cl<sup>-</sup>):</b> Crucial for maintaining osmotic balance, nerve impulses, muscle contraction, and as cofactors for enzymes.",
                "<b>Appropriate Temperature:</b> Cells require a stable temperature range for optimal enzyme function and membrane fluidity.",
                "<b>Appropriate pH:</b> Cells need a stable pH environment for enzyme activity and protein structure.",
                "<b>Removal of Waste Products:</b> Metabolic wastes (e.g., CO<sub>2</sub>, urea) must be removed to prevent toxicity."
            ],
            build_repair: [
                "<b>Amino Acids:</b> Building blocks for proteins, which form enzymes, structural components, hormones, etc.",
                "<b>Lipids (Fatty Acids, Glycerol):</b> Components of cell membranes, energy storage, and signaling molecules.",
                "<b>Nucleic Acid Precursors (Nucleotides):</b> Building blocks for DNA (genetic information) and RNA (protein synthesis).",
                "<b>Vitamins:</b> Organic compounds often acting as coenzymes or regulators of metabolic processes.",
                "<b>Minerals:</b> Inorganic elements essential for various functions, e.g., calcium for bones, iron for hemoglobin."
            ],
            energy_production: [
                "<b>Glucose:</b> The main carbohydrate fuel, broken down via glycolysis and the Krebs cycle.",
                "<b>Fatty Acids:</b> Can be metabolized through beta-oxidation to produce acetyl-CoA, which enters the Krebs cycle.",
                "<b>Oxygen (O<sub>2</sub>):</b> The final electron acceptor in the electron transport chain, critical for efficient ATP production in aerobic respiration."
            ]
        };

        document.addEventListener('DOMContentLoaded', () => {
            let currentlySelectedSystem = null;
            const organAreas = document.querySelectorAll('.organ-area');
            const systemNameEl = document.getElementById('systemName');
            const initialMessageEl = document.getElementById('initialMessage');
            const detailsPanelEl = document.querySelector('.info-panel .details');
            const systemDescriptionEl = document.getElementById('systemDescription');
            const homeostasisRoleEl = document.getElementById('homeostasisRole');
            const bloodValuesListEl = document.getElementById('bloodValuesList');

            organAreas.forEach(area => {
                area.addEventListener('click', function() {
                    const systemKey = this.dataset.system;
                    selectSystem(systemKey);
                });
            });

            function selectSystem(systemKey) {
                // Reset styles for previously selected system's areas
                if (currentlySelectedSystem) {
                    document.querySelectorAll(`.organ-area[data-system="${currentlySelectedSystem}"]`).forEach(el => {
                        el.classList.remove('active');
                        el.style.fill = ''; // Revert to CSS default fill (from <defs><style>)
                    });
                }
                
                // Apply styles to newly selected system's areas
                const systemData = organSystemsData[systemKey];
                document.querySelectorAll(`.organ-area[data-system="${systemKey}"]`).forEach(el => {
                    el.classList.add('active');
                    el.style.fill = systemData.fillColor; // Apply specific fill color
                });
                currentlySelectedSystem = systemKey;

                // Update info panel content
                systemNameEl.textContent = systemData.name;
                systemDescriptionEl.innerHTML = `<p>${systemData.description}</p>`; // Use innerHTML for potential HTML in description
                homeostasisRoleEl.innerHTML = `<p>${systemData.homeostasis}</p>`; // Use innerHTML for potential HTML

                bloodValuesListEl.innerHTML = ''; // Clear previous list items
                systemData.bloodValues.forEach(value => {
                    const li = document.createElement('li');
                    li.innerHTML = value; // Use innerHTML for potential <sub> tags
                    bloodValuesListEl.appendChild(li);
                });

                // Show details and hide initial message
                initialMessageEl.style.display = 'none';
                detailsPanelEl.style.display = 'block';
            }

            function populateCellNeeds() {
                const functionList = document.getElementById('cellNeedsFunction');
                cellNeedsData.function.forEach(item => {
                    const li = document.createElement('li');
                    li.innerHTML = item; // Use innerHTML for <b>, <sub> tags etc.
                    functionList.appendChild(li);
                });

                const buildList = document.getElementById('cellNeedsBuild');
                cellNeedsData.build_repair.forEach(item => {
                    const li = document.createElement('li');
                    li.innerHTML = item;
                    buildList.appendChild(li);
                });

                const energyList = document.getElementById('cellNeedsEnergy');
                cellNeedsData.energy_production.forEach(item => {
                    const li = document.createElement('li');
                    li.innerHTML = item;
                    energyList.appendChild(li);
                });
            }

            populateCellNeeds(); // Populate the "Any Cell" needs section on load
        });
    </script>
</body>
</html>
