
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Interactive A&P for BME</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: 'var(--color-primary, #005f73)',
              secondary: 'var(--color-secondary, #0a9396)',
              accent: 'var(--color-accent, #94d2bd)',
              background: 'var(--color-background, #e9d8a6)',
              textlight: 'var(--color-textlight, #001219)',
              texthighlight: 'var(--color-texthighlight, #ee9b00)',
            },
            animation: {
              'fade-in-up': 'fadeInUp 0.5s ease-out',
              'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            },
            keyframes: {
              fadeInUp: {
                '0%': { opacity: '0', transform: 'translateY(20px)' },
                '100%': { opacity: '1', transform: 'translateY(0)' },
              },
            }
          }
        }
      }
    </script>
    <style>
      /* Custom scrollbar for better aesthetics */
      ::-webkit-scrollbar {
        width: 8px;
      }
      ::-webkit-scrollbar-track {
        background: #f1f1f1;
      }
      ::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background: #555;
      }
      /* Basic styles for smooth scrolling and body */
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: 'Inter', sans-serif; /* A modern sans-serif font */
        @apply bg-background text-textlight;
      }
      /* Add a more suitable font if Inter is not available via Tailwind CDN */
      @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    </style>
  <script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.1",
    "recharts": "https://esm.sh/recharts@^2.15.3"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>
