
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Anatomy & Physiology for Biomedical Engineers</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/animations.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation Header -->
    <header class="main-header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <i class="fas fa-heartbeat"></i>
                    <span>A&P for BME</span>
                </div>
                <ul class="nav-menu">
                    <li><a href="#home" class="nav-link active">Home</a></li>
                    <li><a href="#modules" class="nav-link">Learning Modules</a></li>
                    <li><a href="#simulations" class="nav-link">Simulations</a></li>
                    <li><a href="#videos" class="nav-link">Video Library</a></li>
                    <li><a href="#about" class="nav-link">About</a></li>
                </ul>
                <div class="nav-actions">
                    <button class="nav-sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="hamburger">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Navigation Sidebar -->
    <div class="nav-sidebar" id="navSidebar">
        <div class="sidebar-header">
            <h3>Quick Navigation</h3>
            <button class="sidebar-close" id="sidebarClose">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="sidebar-content">
            <div class="sidebar-section">
                <h4>Learning Modules</h4>
                <ul class="sidebar-links">
                    <li><a href="Interactive Anatomical Terms.html"><i class="fas fa-microscope"></i> Anatomical Terms</a></li>
                    <li><a href="Human Anatomy Explorer.html"><i class="fas fa-user-md"></i> Anatomy Explorer</a></li>
                    <li><a href="Homeostasis Simulator .html"><i class="fas fa-balance-scale"></i> Homeostasis Simulator</a></li>
                    <li><a href="Interactive Sliding Filament Model.html"><i class="fas fa-dumbbell"></i> Muscle Contraction</a></li>
                    <li><a href="Bone System.html"><i class="fas fa-bone"></i> Skeletal System</a></li>
                    <li><a href="Electrophysiology Signal Generation.html"><i class="fas fa-bolt"></i> Electrophysiology</a></li>
                </ul>
            </div>
            <div class="sidebar-section">
                <h4>Interactive Simulations</h4>
                <ul class="sidebar-links">
                    <li><a href="Homeostasis Body Temperature Regulation.html"><i class="fas fa-thermometer-half"></i> Temperature Regulation</a></li>
                    <li><a href="Organ Systems and Homeostasis Explorer.html"><i class="fas fa-brain"></i> System Integration</a></li>
                    <li><a href="Exploring Homeostasis.html"><i class="fas fa-chart-line"></i> Homeostasis Concepts</a></li>
                </ul>
            </div>
            <div class="sidebar-section">
                <h4>Assessment Tools</h4>
                <ul class="sidebar-links">
                    <li><a href="Human Body Organ Quiz.html"><i class="fas fa-question-circle"></i> Organ Quiz</a></li>
                    <li><a href="Click an organ to learn its name.html"><i class="fas fa-mouse-pointer"></i> Organ Identification</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title">Interactive Anatomy & Physiology</h1>
                <h2 class="hero-subtitle">for Biomedical Engineers</h2>
                <p class="hero-description">
                    Explore the human body through interactive simulations, 3D models, and engaging animations.
                    Master anatomy and physiology concepts essential for biomedical engineering.
                </p>
                <div class="hero-buttons">
                    <a href="#modules" class="btn btn-primary">Start Learning</a>
                    <a href="#videos" class="btn btn-secondary">Watch Videos</a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="animated-heart">
                    <i class="fas fa-heartbeat pulse-animation"></i>
                </div>
                <div class="floating-elements">
                    <div class="element element-1"><i class="fas fa-dna"></i></div>
                    <div class="element element-2"><i class="fas fa-brain"></i></div>
                    <div class="element element-3"><i class="fas fa-lungs"></i></div>
                    <div class="element element-4"><i class="fas fa-bone"></i></div>
                </div>
            </div>
        </div>
        <div class="scroll-indicator">
            <div class="scroll-arrow">
                <i class="fas fa-chevron-down"></i>
            </div>
        </div>
    </section>

    <!-- Learning Modules Section -->
    <section id="modules" class="modules-section">
        <div class="container">
            <h2 class="section-title">Learning Modules</h2>
            <p class="section-subtitle">Comprehensive study modules organized by anatomical systems and physiological processes</p>

            <div class="modules-grid">
                <!-- Cellular Foundation Module -->
                <div class="module-card" data-category="foundation">
                    <div class="module-icon">
                        <i class="fas fa-microscope"></i>
                    </div>
                    <h3>Cellular Foundation</h3>
                    <p>Explore cell structure, membrane transport, and cellular processes</p>
                    <div class="module-links">
                        <a href="Interactive Anatomical Terms.html" class="module-link">Anatomical Terms</a>
                        <a href="Anatomy and physiology .html" class="module-link">Cell Biology</a>
                    </div>
                    <div class="module-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0% Complete</span>
                    </div>
                </div>

                <!-- Body Systems Module -->
                <div class="module-card" data-category="systems">
                    <div class="module-icon">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <h3>Body Systems</h3>
                    <p>Study major organ systems and their interactions</p>
                    <div class="module-links">
                        <a href="Human Anatomy Explorer.html" class="module-link">Anatomy Explorer</a>
                        <a href="Explore the Human Body.html" class="module-link">Body Systems</a>
                        <a href="Organ Systems and Homeostasis Explorer.html" class="module-link">System Integration</a>
                    </div>
                    <div class="module-progress">
                        <div class="progress-bar">
                            <div class="progress-fill progress-0"></div>
                        </div>
                        <span class="progress-text">0% Complete</span>
                    </div>
                </div>

                <!-- Homeostasis & Regulation Module -->
                <div class="module-card" data-category="physiology">
                    <div class="module-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h3>Homeostasis & Regulation</h3>
                    <p>Understand feedback mechanisms and physiological regulation</p>
                    <div class="module-links">
                        <a href="Homeostasis Simulator .html" class="module-link">Temperature Regulation</a>
                        <a href="Homeostasis Body Temperature Regulation.html" class="module-link">Body Temperature</a>
                        <a href="Interactive Homeostasis Explorer.html" class="module-link">Homeostasis Explorer</a>
                        <a href="Exploring Homeostasis.html" class="module-link">Homeostasis Concepts</a>
                    </div>
                    <div class="module-progress">
                        <div class="progress-bar">
                            <div class="progress-fill progress-0"></div>
                        </div>
                        <span class="progress-text">0% Complete</span>
                    </div>
                </div>

                <!-- Muscle & Movement Module -->
                <div class="module-card" data-category="movement">
                    <div class="module-icon">
                        <i class="fas fa-dumbbell"></i>
                    </div>
                    <h3>Muscle & Movement</h3>
                    <p>Explore muscle contraction and biomechanics</p>
                    <div class="module-links">
                        <a href="Interactive Sliding Filament Model.html" class="module-link">Sliding Filament Model</a>
                        <a href="Sliding Filament Model.html" class="module-link">Muscle Contraction</a>
                    </div>
                    <div class="module-progress">
                        <div class="progress-bar">
                            <div class="progress-fill progress-0"></div>
                        </div>
                        <span class="progress-text">0% Complete</span>
                    </div>
                </div>

                <!-- Skeletal System Module -->
                <div class="module-card" data-category="structure">
                    <div class="module-icon">
                        <i class="fas fa-bone"></i>
                    </div>
                    <h3>Skeletal System</h3>
                    <p>Study bone structure, development, and function</p>
                    <div class="module-links">
                        <a href="Bone System.html" class="module-link">Bone System Overview</a>
                    </div>
                    <div class="module-progress">
                        <div class="progress-bar">
                            <div class="progress-fill progress-0"></div>
                        </div>
                        <span class="progress-text">0% Complete</span>
                    </div>
                </div>

                <!-- Directional Terms Module -->
                <div class="module-card" data-category="terminology">
                    <div class="module-icon">
                        <i class="fas fa-compass"></i>
                    </div>
                    <h3>Anatomical Terminology</h3>
                    <p>Master anatomical position and directional terms</p>
                    <div class="module-links">
                        <a href="Anatomical Directional .html" class="module-link">Directional Terms</a>
                        <a href="Anatomical Directional Terms.html" class="module-link">Anatomical Directions</a>
                        <a href="Directional Terms in Anatomy.html" class="module-link">Anatomy Directions</a>
                    </div>
                    <div class="module-progress">
                        <div class="progress-bar">
                            <div class="progress-fill progress-0"></div>
                        </div>
                        <span class="progress-text">0% Complete</span>
                    </div>
                </div>

                <!-- Electrophysiology Module -->
                <div class="module-card" data-category="electrophysiology">
                    <div class="module-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3>Electrophysiology & Signal Generation</h3>
                    <p>Explore ion transport across cell membranes and bioelectrical signal generation</p>
                    <div class="module-links">
                        <a href="Electrophysiology Signal Generation.html" class="module-link">Ion Transport Simulation</a>
                        <a href="Electrophysiology Signal Generation.html" class="module-link">Action Potential Generation</a>
                        <a href="Electrophysiology Signal Generation.html" class="module-link">Membrane Potential Dynamics</a>
                    </div>
                    <div class="module-progress">
                        <div class="progress-bar">
                            <div class="progress-fill progress-0"></div>
                        </div>
                        <span class="progress-text">0% Complete</span>
                    </div>
                </div>
            </div>

            <!-- Module Filter -->
            <div class="module-filter">
                <h3>Filter by Category</h3>
                <div class="filter-buttons">
                    <button type="button" class="filter-btn active" data-filter="all">All Modules</button>
                    <button type="button" class="filter-btn" data-filter="foundation">Foundation</button>
                    <button type="button" class="filter-btn" data-filter="systems">Body Systems</button>
                    <button type="button" class="filter-btn" data-filter="physiology">Physiology</button>
                    <button type="button" class="filter-btn" data-filter="movement">Movement</button>
                    <button type="button" class="filter-btn" data-filter="structure">Structure</button>
                    <button type="button" class="filter-btn" data-filter="terminology">Terminology</button>
                    <button type="button" class="filter-btn" data-filter="electrophysiology">Electrophysiology</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Simulations Section -->
    <section id="simulations" class="simulations-section">
        <div class="container">
            <h2 class="section-title">Interactive Simulations</h2>
            <p class="section-subtitle">Hands-on learning through dynamic simulations and virtual experiments</p>

            <div class="simulations-grid">
                <div class="simulation-card featured">
                    <div class="simulation-preview">
                        <i class="fas fa-heartbeat simulation-icon"></i>
                        <div class="play-overlay">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="simulation-content">
                        <h3>Homeostasis Temperature Regulation</h3>
                        <p>Interactive simulation showing how the body maintains optimal temperature through feedback mechanisms.</p>
                        <div class="simulation-tags">
                            <span class="tag">Physiology</span>
                            <span class="tag">Homeostasis</span>
                        </div>
                        <a href="Homeostasis Simulator .html" class="simulation-link">Launch Simulation</a>
                    </div>
                </div>

                <div class="simulation-card">
                    <div class="simulation-preview">
                        <i class="fas fa-dna simulation-icon"></i>
                        <div class="play-overlay">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="simulation-content">
                        <h3>Sliding Filament Model</h3>
                        <p>Explore muscle contraction at the molecular level with this interactive sliding filament simulation.</p>
                        <div class="simulation-tags">
                            <span class="tag">Muscle</span>
                            <span class="tag">Molecular</span>
                        </div>
                        <a href="Interactive Sliding Filament Model.html" class="simulation-link">Launch Simulation</a>
                    </div>
                </div>

                <div class="simulation-card">
                    <div class="simulation-preview">
                        <i class="fas fa-user-md simulation-icon"></i>
                        <div class="play-overlay">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="simulation-content">
                        <h3>Human Anatomy Explorer</h3>
                        <p>Interactive 3D exploration of human organs and their functions with detailed information panels.</p>
                        <div class="simulation-tags">
                            <span class="tag">Anatomy</span>
                            <span class="tag">3D Model</span>
                        </div>
                        <a href="Human Anatomy Explorer.html" class="simulation-link">Launch Simulation</a>
                    </div>
                </div>

                <div class="simulation-card">
                    <div class="simulation-preview">
                        <i class="fas fa-brain simulation-icon"></i>
                        <div class="play-overlay">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="simulation-content">
                        <h3>Organ Systems Integration</h3>
                        <p>Understand how different organ systems work together to maintain life and health.</p>
                        <div class="simulation-tags">
                            <span class="tag">Systems</span>
                            <span class="tag">Integration</span>
                        </div>
                        <a href="Organ Systems and Homeostasis Explorer.html" class="simulation-link">Launch Simulation</a>
                    </div>
                </div>

                <div class="simulation-card">
                    <div class="simulation-preview">
                        <i class="fas fa-bolt simulation-icon"></i>
                        <div class="play-overlay">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="simulation-content">
                        <h3>Electrophysiology & Ion Transport</h3>
                        <p>Precise simulation of ion movement across cell membranes and bioelectrical signal generation.</p>
                        <div class="simulation-tags">
                            <span class="tag">Electrophysiology</span>
                            <span class="tag">Ion Transport</span>
                            <span class="tag">Action Potential</span>
                        </div>
                        <a href="Electrophysiology Signal Generation.html" class="simulation-link">Launch Simulation</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Video Library Section -->
    <section id="videos" class="videos-section">
        <div class="container">
            <h2 class="section-title">Video Library</h2>
            <p class="section-subtitle">Engaging animated videos to enhance your understanding</p>

            <div class="videos-grid">
                <div class="video-card">
                    <div class="video-thumbnail">
                        <img src="https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg" alt="Cell Structure and Function">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">8:45</div>
                    </div>
                    <div class="video-info">
                        <h3>Cell Structure and Function</h3>
                        <p>Explore the fundamental unit of life and understand cellular organelles and their functions.</p>
                        <div class="video-meta">
                            <span class="video-category">Cell Biology</span>
                            <span class="video-views">1.2K views</span>
                        </div>
                    </div>
                </div>

                <div class="video-card">
                    <div class="video-thumbnail">
                        <img src="https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg" alt="Homeostasis Mechanisms">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">12:30</div>
                    </div>
                    <div class="video-info">
                        <h3>Homeostasis Mechanisms</h3>
                        <p>Learn how the body maintains internal balance through negative and positive feedback loops.</p>
                        <div class="video-meta">
                            <span class="video-category">Physiology</span>
                            <span class="video-views">856 views</span>
                        </div>
                    </div>
                </div>

                <div class="video-card">
                    <div class="video-thumbnail">
                        <img src="https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg" alt="Muscle Contraction">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">15:20</div>
                    </div>
                    <div class="video-info">
                        <h3>Muscle Contraction Animation</h3>
                        <p>Detailed animation of the sliding filament model and cross-bridge cycling in muscle fibers.</p>
                        <div class="video-meta">
                            <span class="video-category">Muscle System</span>
                            <span class="video-views">2.1K views</span>
                        </div>
                    </div>
                </div>

                <div class="video-card">
                    <div class="video-thumbnail">
                        <img src="https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg" alt="Nervous System">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">18:45</div>
                    </div>
                    <div class="video-info">
                        <h3>Nervous System Overview</h3>
                        <p>Comprehensive overview of the central and peripheral nervous systems and signal transmission.</p>
                        <div class="video-meta">
                            <span class="video-category">Nervous System</span>
                            <span class="video-views">1.8K views</span>
                        </div>
                    </div>
                </div>

                <div class="video-card">
                    <div class="video-thumbnail">
                        <img src="https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg" alt="Cardiovascular System">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">14:15</div>
                    </div>
                    <div class="video-info">
                        <h3>Cardiovascular System</h3>
                        <p>Journey through the heart and blood vessels, understanding circulation and cardiac function.</p>
                        <div class="video-meta">
                            <span class="video-category">Cardiovascular</span>
                            <span class="video-views">1.5K views</span>
                        </div>
                    </div>
                </div>

                <div class="video-card">
                    <div class="video-thumbnail">
                        <img src="https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg" alt="Respiratory System">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">11:30</div>
                    </div>
                    <div class="video-info">
                        <h3>Respiratory System Mechanics</h3>
                        <p>Understand breathing mechanics, gas exchange, and respiratory control mechanisms.</p>
                        <div class="video-meta">
                            <span class="video-category">Respiratory</span>
                            <span class="video-views">1.1K views</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section">
        <div class="container">
            <div class="about-content">
                <div class="about-text">
                    <h2 class="section-title">About This Platform</h2>
                    <p>This interactive learning platform is designed specifically for biomedical engineering students to master anatomy and physiology concepts through engaging, hands-on experiences.</p>

                    <div class="features-grid">
                        <div class="feature-item">
                            <i class="fas fa-microscope"></i>
                            <h3>Interactive Simulations</h3>
                            <p>Explore complex biological processes through dynamic, interactive simulations</p>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-video"></i>
                            <h3>Animated Videos</h3>
                            <p>Learn through high-quality animations that bring concepts to life</p>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-chart-line"></i>
                            <h3>Progress Tracking</h3>
                            <p>Monitor your learning progress across all modules and topics</p>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-mobile-alt"></i>
                            <h3>Mobile Friendly</h3>
                            <p>Access content anywhere, anytime on any device</p>
                        </div>
                    </div>
                </div>
                <div class="about-visual">
                    <div class="stats-container">
                        <div class="stat-item">
                            <div class="stat-number">15+</div>
                            <div class="stat-label">Interactive Modules</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">50+</div>
                            <div class="stat-label">Simulations</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">100+</div>
                            <div class="stat-label">Video Lessons</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">1000+</div>
                            <div class="stat-label">Students Helped</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-heartbeat"></i>
                        <span>A&P for BME</span>
                    </div>
                    <p>Interactive Anatomy & Physiology learning platform designed for biomedical engineering students.</p>
                    <div class="author-info">
                        <h4><i class="fas fa-user-graduate"></i> Author</h4>
                        <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                        <p>SUST BME @2025</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <div class="contact-phones">
                            <p><i class="fas fa-phone"></i> +************</p>
                            <p><i class="fas fa-phone"></i> +************</p>
                        </div>
                    </div>
                    <div class="social-links">
                        <a href="mailto:<EMAIL>" aria-label="Email"><i class="fas fa-envelope"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="#" aria-label="ResearchGate"><i class="fab fa-researchgate"></i></a>
                        <a href="#" aria-label="Academia"><i class="fas fa-graduation-cap"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="#modules">Learning Modules</a></li>
                        <li><a href="#simulations">Simulations</a></li>
                        <li><a href="#videos">Video Library</a></li>
                        <li><a href="#about">About</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Resources</h3>
                    <ul>
                        <li><a href="#">Study Guides</a></li>
                        <li><a href="#">Practice Quizzes</a></li>
                        <li><a href="#">Glossary</a></li>
                        <li><a href="#">Help Center</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Information</h3>
                    <ul>
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                        <li><i class="fas fa-phone"></i> +************</li>
                        <li><i class="fas fa-phone"></i> +************</li>
                        <li><i class="fas fa-university"></i> SUST BME Department</li>
                        <li><i class="fas fa-calendar"></i> 2025</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="copyright-info">
                    <p>&copy; 2025 Interactive Anatomy & Physiology for BME. All rights reserved.</p>
                    <p><strong>Author:</strong> Dr. Mohammed Yagoub Esmail | <strong>SUST BME @2025</strong></p>
                    <p><strong>Copyright:</strong> <EMAIL></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
</body>
</html>
