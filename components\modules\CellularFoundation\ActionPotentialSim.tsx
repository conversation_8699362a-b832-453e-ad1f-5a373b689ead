import React, { useState, useEffect, useCallback } from 'react';
import Button from '../../common/Button';
import { ActionPotentialPhase } from '../../../types';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';


const ActionPotentialSim: React.FC = () => {
  const [phase, setPhase] = useState<ActionPotentialPhase>(ActionPotentialPhase.RESTING);
  const [membranePotential, setMembranePotential] = useState(-70); // mV
  const [time, setTime] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [stimulusStrength, setStimulusStrength] = useState(15); // Arbitrary units, threshold around 20mV increase
  const thresholdPotential = -55; // mV

  const [chartData, setChartData] = useState<{time: number, potential: number}[]>([]);

  const resetSimulation = useCallback(() => {
    setPhase(ActionPotentialPhase.RESTING);
    setMembranePotential(-70);
    setTime(0);
    setIsRunning(false);
    setChartData([{time: 0, potential: -70}]);
  }, []);
  
  useEffect(() => {
    resetSimulation();
  }, [resetSimulation]);

  useEffect(() => {
    let intervalId: ReturnType<typeof setTimeout> | undefined;

    if (isRunning) {
      intervalId = setInterval(() => {
        setTime(prevTime => prevTime + 1);
        setChartData(prevData => [...prevData, { time: time + 1, potential: membranePotential}].slice(-100)); // Keep last 100 points

        switch (phase) {
          case ActionPotentialPhase.RESTING:
            // Apply stimulus
            if (time === 1) { // Apply stimulus at t=1
                const newPotential = -70 + stimulusStrength;
                setMembranePotential(newPotential);
                if (newPotential >= thresholdPotential) {
                    setPhase(ActionPotentialPhase.DEPOLARIZATION);
                } else {
                    // Failed to reach threshold, return to resting (simplified)
                    // In a real sim, it would decay back. For now, just stops if not strong enough.
                    setMembranePotential(-70); // Back to resting
                    setPhase(ActionPotentialPhase.RESTING); // Reset phase
                    setIsRunning(false); // Stop simulation
                }
            }
            break;
          case ActionPotentialPhase.DEPOLARIZATION:
            setMembranePotential(prev => {
              const next = prev + 20; // Rapid rise
              if (next >= 30) {
                setPhase(ActionPotentialPhase.REPOLARIZATION);
                return 30;
              }
              return next;
            });
            break;
          case ActionPotentialPhase.REPOLARIZATION:
            setMembranePotential(prev => {
              const next = prev - 25; // Rapid fall
              if (next <= -75) { // Overshoot for hyperpolarization
                setPhase(ActionPotentialPhase.HYPERPOLARIZATION);
                return -75;
              }
              return next;
            });
            break;
          case ActionPotentialPhase.HYPERPOLARIZATION:
            setMembranePotential(prev => {
              const next = prev + 5; // Slow return to resting
              if (next >= -70) {
                setPhase(ActionPotentialPhase.RESTING);
                setIsRunning(false); // End of one cycle
                return -70;
              }
              return next;
            });
            break;
          default:
            setIsRunning(false);
        }
      }, 200); // Simulation speed
    }
    return () => clearInterval(intervalId);
  }, [isRunning, phase, time, membranePotential, stimulusStrength, thresholdPotential]);

  const startSimulation = () => {
    resetSimulation(); // Reset state before starting
    // Slight delay to ensure reset completes before isRunning is set
    setTimeout(() => {
      setIsRunning(true);
    }, 50);
  };
  

  return (
    <div className="space-y-4 p-4 bg-gray-50 rounded-lg shadow-inner">
      <p className="text-sm text-textlight/80">Current Phase: <span className="font-semibold text-secondary">{phase}</span></p>
      <p className="text-sm text-textlight/80">Membrane Potential: <span className="font-semibold text-secondary">{membranePotential.toFixed(0)} mV</span></p>
      
      <div className="my-4">
        <label htmlFor="stimulusStrength" className="block text-sm font-medium text-gray-700">
          Stimulus Strength (causes ~{stimulusStrength}mV initial change):
        </label>
        <input
          type="range"
          id="stimulusStrength"
          min="5" // Min stimulus to make it interesting
          max="30" // Max stimulus
          value={stimulusStrength}
          onChange={(e) => setStimulusStrength(parseInt(e.target.value))}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-secondary"
          disabled={isRunning}
        />
         <p className="text-xs text-gray-500">Threshold for action potential is at -55mV (requires ~15mV increase from -70mV resting).</p>
      </div>

      <div className="flex space-x-2">
        <Button onClick={startSimulation} disabled={isRunning} variant="primary">Start/Apply Stimulus</Button>
        <Button onClick={resetSimulation} variant="outline">Reset</Button>
      </div>

      <div className="w-full h-64 mt-6">
        <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData} margin={{ top: 5, right: 20, left: -20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#ccc" />
                <XAxis dataKey="time" unit="ms" label={{ value: 'Time (ms)', position: 'insideBottomRight', offset: -2 }}/>
                <YAxis domain={[-90, 40]} unit="mV" label={{ value: 'Potential (mV)', angle: -90, position: 'insideLeft' }} />
                <Tooltip formatter={(value: number) => [`${value.toFixed(0)} mV`, "Potential"]}/>
                <Legend />
                <Line type="monotone" dataKey="potential" stroke="#0a9396" strokeWidth={2} dot={false} activeDot={{ r: 6 }} name="Membrane Potential" />
                <Line type="monotone" dataKey={() => thresholdPotential} stroke="#ee9b00" strokeDasharray="5 5" dot={false} name="Threshold" />
            </LineChart>
        </ResponsiveContainer>
      </div>
      <p className="text-xs text-textlight/60 mt-2">
        Simplified action potential generation. Controls like ion channel density would add more complexity.
        Propagation along an axon is not visually represented here.
      </p>
    </div>
  );
};

export default ActionPotentialSim;