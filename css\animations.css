/* ===== ANIMATIONS & KEYFRAMES ===== */

/* Fade In Up Animation */
@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Pulse Animation for Heart */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

/* Pulse Animation for Elements */
.pulse-animation {
    animation: pulse 2s ease-in-out infinite;
}

/* Float Animation for Floating Elements */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-10px) rotate(5deg);
    }
    50% {
        transform: translateY(-20px) rotate(0deg);
    }
    75% {
        transform: translateY(-10px) rotate(-5deg);
    }
}

/* Bounce Animation for Scroll Indicator */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) translateX(-50%);
    }
    40% {
        transform: translateY(-10px) translateX(-50%);
    }
    60% {
        transform: translateY(-5px) translateX(-50%);
    }
}

/* Slide In from Left */
@keyframes slideInLeft {
    0% {
        opacity: 0;
        transform: translateX(-50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Slide In from Right */
@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scale In Animation */
@keyframes scaleIn {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Rotate Animation */
@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Glow Animation */
@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(10, 147, 150, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(10, 147, 150, 0.8);
    }
}

/* Shimmer Animation */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* Typing Animation */
@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

/* Blink Animation for Cursor */
@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

/* ===== ANIMATION UTILITY CLASSES ===== */

/* Fade Animations */
.fade-in {
    animation: fadeInUp 0.8s ease-out;
}

.fade-in-delay-1 {
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.fade-in-delay-2 {
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.fade-in-delay-3 {
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

/* Slide Animations */
.slide-in-left {
    animation: slideInLeft 0.8s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.8s ease-out;
}

/* Scale Animations */
.scale-in {
    animation: scaleIn 0.6s ease-out;
}

/* Hover Animations */
.hover-float:hover {
    animation: float 2s ease-in-out infinite;
}

.hover-glow:hover {
    animation: glow 1.5s ease-in-out infinite;
}

.hover-rotate:hover {
    animation: rotate 0.5s ease-in-out;
}

/* Loading Animations */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

.loading-pulse {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Text Animations */
.typing-effect {
    overflow: hidden;
    border-right: 2px solid #005f73;
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink 0.75s step-end infinite;
}

/* Stagger Animation for Lists */
.stagger-animation > * {
    opacity: 0;
    animation: fadeInUp 0.6s ease-out forwards;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }

/* Intersection Observer Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.animate-on-scroll.in-view {
    opacity: 1;
    transform: translateY(0);
}

/* Button Hover Effects */
.btn-hover-effect {
    position: relative;
    overflow: hidden;
}

.btn-hover-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-hover-effect:hover::before {
    left: 100%;
}

/* Card Hover Effects */
.card-hover-effect {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover-effect:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Icon Spin Animation */
.icon-spin {
    animation: rotate 2s linear infinite;
}

/* Heartbeat Animation */
@keyframes heartbeat {
    0%, 100% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.3);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.3);
    }
    70% {
        transform: scale(1);
    }
}

.heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
}

/* Progress Bar Animation */
@keyframes progressFill {
    0% {
        width: 0%;
    }
    100% {
        width: var(--progress-width, 0%);
    }
}

.progress-animated {
    animation: progressFill 2s ease-out;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
