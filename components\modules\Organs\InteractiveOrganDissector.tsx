
import React, { useState }
from 'react';
import type { OrganData } from '../../../types';
import Button from '../../common/Button';
import Card from '../../common/Card';

interface InteractiveOrganDissectorProps {
    organs: OrganData[];
}

const InteractiveOrganDissector: React.FC<InteractiveOrganDissectorProps> = ({ organs }) => {
    const [selectedOrgan, setSelectedOrgan] = useState<OrganData | null>(organs.length > 0 ? organs[0] : null);
    const [revealedLayers, setRevealedLayers] = useState<string[]>([]);

    const handleSelectOrgan = (organ: OrganData) => {
        setSelectedOrgan(organ);
        setRevealedLayers([]); // Reset revealed layers when organ changes
    };

    const toggleLayer = (layerName: string) => {
        setRevealedLayers(prev => 
            prev.includes(layerName) ? prev.filter(l => l !== layerName) : [...prev, layerName]
        );
    };

    return (
        <div className="p-6 bg-white/70 backdrop-blur-sm shadow-xl rounded-xl">
            <h3 className="text-3xl font-semibold text-secondary mb-8 text-center">Interactive Organ Dissector</h3>
            <div className="flex flex-col lg:flex-row gap-8">
                {/* Organ Selection */}
                <div className="lg:w-1/4 space-y-3">
                    <h4 className="font-semibold text-primary mb-2">Select an Organ:</h4>
                    {organs.map(organ => (
                        <Button 
                            key={organ.name}
                            onClick={() => handleSelectOrgan(organ)}
                            variant={selectedOrgan?.name === organ.name ? 'primary' : 'outline'}
                            className="w-full"
                        >
                            {organ.name}
                        </Button>
                    ))}
                </div>

                {/* Organ Display & Dissection */}
                <div className="lg:w-3/4">
                    {selectedOrgan ? (
                        <Card className="bg-white">
                            <h4 className="text-2xl font-bold text-primary mb-3">{selectedOrgan.name}</h4>
                            <p className="text-textlight/80 mb-4">{selectedOrgan.description}</p>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-start">
                                <div className="relative">
                                    <img 
                                        src={selectedOrgan.imageUrl} 
                                        alt={selectedOrgan.name} 
                                        className="w-full h-auto object-contain rounded-lg shadow-md" 
                                    />
                                    {/* Placeholder for actual layer peeling visualization */}
                                    {revealedLayers.length > 0 && (
                                        <div className="absolute inset-0 bg-black/20 flex items-center justify-center text-white font-bold text-lg rounded-lg">
                                            {revealedLayers.length} Layer(s) Revealed
                                        </div>
                                    )}
                                </div>
                                <div>
                                    <h5 className="font-semibold text-lg text-primary mb-2">Tissue Layers (Click to reveal info):</h5>
                                    <ul className="space-y-2">
                                        {selectedOrgan.tissueLayers.map(layer => (
                                            <li key={layer.name}>
                                                <button 
                                                    onClick={() => toggleLayer(layer.name)}
                                                    className={`w-full text-left p-2 rounded border transition-colors ${revealedLayers.includes(layer.name) ? 'bg-accent/30 border-accent' : 'bg-gray-100 hover:bg-gray-200 border-gray-300'}`}
                                                >
                                                    {layer.name}
                                                </button>
                                                {revealedLayers.includes(layer.name) && (
                                                    <div className="p-2 mt-1 bg-gray-50 rounded text-sm text-textlight/70 border-l-4 border-accent">
                                                        <strong>Role:</strong> {layer.role}
                                                    </div>
                                                )}
                                            </li>
                                        ))}
                                    </ul>
                                    <p className="mt-4 text-xs text-gray-500">This is a simplified representation. Actual dissection involves complex 3D structures.</p>
                                </div>
                            </div>
                        </Card>
                    ) : (
                        <p className="text-center text-textlight/70 p-8">Select an organ to explore its structure.</p>
                    )}
                </div>
            </div>
        </div>
    );
};

export default InteractiveOrganDissector;
