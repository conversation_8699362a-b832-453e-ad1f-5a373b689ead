<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anatomical Terminology - Dynamic Animated Lecture | A&P for BME</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/animations.css">
    <link rel="stylesheet" href="../css/lecture.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation Header -->
    <header class="lecture-header">
        <nav class="lecture-nav">
            <div class="nav-container">
                <div class="nav-logo">
                    <i class="fas fa-heartbeat"></i>
                    <span>A&P for BME</span>
                </div>
                <div class="lecture-title">
                    <h1>Anatomical Terminology - Dynamic Lecture</h1>
                </div>
                <div class="nav-actions">
                    <a href="../index.html" class="btn-home">
                        <i class="fas fa-home"></i> Return Home
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Lecture Progress Bar -->
    <div class="lecture-progress">
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        <div class="progress-info">
            <span id="currentSlide">1</span> / <span id="totalSlides">10</span>
        </div>
    </div>

    <!-- Lecture Content -->
    <main class="lecture-main">
        <div class="lecture-container">
            
            <!-- Slide 1: Introduction -->
            <div class="lecture-slide active" data-slide="1">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2 class="slide-title">Anatomical Terminology</h2>
                        <p class="slide-subtitle">Universal Language of Anatomy</p>
                    </div>
                    <div class="slide-body">
                        <div class="content-grid">
                            <div class="content-text">
                                <h3>Learning Objectives</h3>
                                <ul class="animated-list">
                                    <li class="fade-in-up" style="animation-delay: 0.2s;">Master anatomical position</li>
                                    <li class="fade-in-up" style="animation-delay: 0.4s;">Understand directional terms</li>
                                    <li class="fade-in-up" style="animation-delay: 0.6s;">Learn body planes and sections</li>
                                    <li class="fade-in-up" style="animation-delay: 0.8s;">Explore body cavities and regions</li>
                                    <li class="fade-in-up" style="animation-delay: 1.0s;">Apply terminology in clinical context</li>
                                </ul>
                            </div>
                            <div class="content-visual">
                                <div class="anatomical-overview">
                                    <div class="human-figure">
                                        <div class="body-outline">
                                            <div class="anatomical-position">
                                                <div class="head-region">
                                                    <div class="region-label">Cephalic</div>
                                                </div>
                                                <div class="torso-region">
                                                    <div class="region-label">Thoracic</div>
                                                </div>
                                                <div class="abdomen-region">
                                                    <div class="region-label">Abdominal</div>
                                                </div>
                                                <div class="limbs-region">
                                                    <div class="arm-labels">
                                                        <span>Upper Extremity</span>
                                                    </div>
                                                    <div class="leg-labels">
                                                        <span>Lower Extremity</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="directional-arrows">
                                                <div class="arrow superior">↑ Superior</div>
                                                <div class="arrow inferior">↓ Inferior</div>
                                                <div class="arrow anterior">← Anterior</div>
                                                <div class="arrow posterior">→ Posterior</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2: Anatomical Position -->
            <div class="lecture-slide" data-slide="2">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2 class="slide-title">Anatomical Position</h2>
                        <p class="slide-subtitle">Standard Reference Position</p>
                    </div>
                    <div class="slide-body">
                        <div class="anatomical-position-demo">
                            <div class="position-figure">
                                <div class="human-model">
                                    <div class="body-parts">
                                        <div class="head upright">
                                            <div class="feature-label">Head erect</div>
                                            <div class="eyes forward">
                                                <span>Eyes forward</span>
                                            </div>
                                        </div>
                                        
                                        <div class="arms positioned">
                                            <div class="arm-position">
                                                <div class="feature-label">Arms at sides</div>
                                                <div class="palms forward">
                                                    <span>Palms forward</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="legs positioned">
                                            <div class="leg-position">
                                                <div class="feature-label">Feet parallel</div>
                                                <div class="toes forward">
                                                    <span>Toes forward</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="body-stance">
                                            <div class="feature-label">Standing erect</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="position-importance">
                                <h3>Importance of Anatomical Position</h3>
                                <div class="importance-points">
                                    <div class="point standardization">
                                        <div class="point-icon">📏</div>
                                        <h4>Standardization</h4>
                                        <p>Provides universal reference point for all anatomical descriptions</p>
                                    </div>
                                    
                                    <div class="point consistency">
                                        <div class="point-icon">🎯</div>
                                        <h4>Consistency</h4>
                                        <p>Ensures accurate communication between healthcare professionals</p>
                                    </div>
                                    
                                    <div class="point precision">
                                        <div class="point-icon">🔬</div>
                                        <h4>Precision</h4>
                                        <p>Eliminates ambiguity in anatomical and clinical descriptions</p>
                                    </div>
                                    
                                    <div class="point education">
                                        <div class="point-icon">📚</div>
                                        <h4>Education</h4>
                                        <p>Foundation for learning all anatomical relationships</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="position-variations">
                                <h3>Common Variations</h3>
                                <div class="variation-examples">
                                    <div class="variation supine">
                                        <h4>Supine Position</h4>
                                        <div class="variation-description">
                                            <p>Lying face up, maintaining anatomical relationships</p>
                                        </div>
                                    </div>
                                    
                                    <div class="variation prone">
                                        <h4>Prone Position</h4>
                                        <div class="variation-description">
                                            <p>Lying face down, directional terms remain consistent</p>
                                        </div>
                                    </div>
                                    
                                    <div class="variation lateral">
                                        <h4>Lateral Position</h4>
                                        <div class="variation-description">
                                            <p>Lying on side, anatomical references unchanged</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3: Directional Terms -->
            <div class="lecture-slide" data-slide="3">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2 class="slide-title">Directional Terms</h2>
                        <p class="slide-subtitle">Spatial Relationships in the Body</p>
                    </div>
                    <div class="slide-body">
                        <div class="directional-terms">
                            <div class="terms-grid">
                                <div class="term-pair superior-inferior">
                                    <h3>Superior ↔ Inferior</h3>
                                    <div class="term-demo">
                                        <div class="body-reference">
                                            <div class="superior-example">
                                                <div class="example-label">Superior</div>
                                                <p>Toward the head</p>
                                                <div class="example-text">
                                                    "The heart is superior to the stomach"
                                                </div>
                                            </div>
                                            <div class="inferior-example">
                                                <div class="example-label">Inferior</div>
                                                <p>Toward the feet</p>
                                                <div class="example-text">
                                                    "The stomach is inferior to the heart"
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="term-pair anterior-posterior">
                                    <h3>Anterior ↔ Posterior</h3>
                                    <div class="term-demo">
                                        <div class="body-reference">
                                            <div class="anterior-example">
                                                <div class="example-label">Anterior (Ventral)</div>
                                                <p>Toward the front</p>
                                                <div class="example-text">
                                                    "The sternum is anterior to the spine"
                                                </div>
                                            </div>
                                            <div class="posterior-example">
                                                <div class="example-label">Posterior (Dorsal)</div>
                                                <p>Toward the back</p>
                                                <div class="example-text">
                                                    "The spine is posterior to the sternum"
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="term-pair medial-lateral">
                                    <h3>Medial ↔ Lateral</h3>
                                    <div class="term-demo">
                                        <div class="body-reference">
                                            <div class="medial-example">
                                                <div class="example-label">Medial</div>
                                                <p>Toward the midline</p>
                                                <div class="example-text">
                                                    "The nose is medial to the eyes"
                                                </div>
                                            </div>
                                            <div class="lateral-example">
                                                <div class="example-label">Lateral</div>
                                                <p>Away from midline</p>
                                                <div class="example-text">
                                                    "The ears are lateral to the nose"
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="term-pair proximal-distal">
                                    <h3>Proximal ↔ Distal</h3>
                                    <div class="term-demo">
                                        <div class="body-reference limbs">
                                            <div class="proximal-example">
                                                <div class="example-label">Proximal</div>
                                                <p>Closer to attachment point</p>
                                                <div class="example-text">
                                                    "The elbow is proximal to the wrist"
                                                </div>
                                            </div>
                                            <div class="distal-example">
                                                <div class="example-label">Distal</div>
                                                <p>Farther from attachment</p>
                                                <div class="example-text">
                                                    "The fingers are distal to the elbow"
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="term-pair superficial-deep">
                                    <h3>Superficial ↔ Deep</h3>
                                    <div class="term-demo">
                                        <div class="body-reference layers">
                                            <div class="superficial-example">
                                                <div class="example-label">Superficial</div>
                                                <p>Closer to surface</p>
                                                <div class="example-text">
                                                    "Skin is superficial to muscle"
                                                </div>
                                            </div>
                                            <div class="deep-example">
                                                <div class="example-label">Deep</div>
                                                <p>Farther from surface</p>
                                                <div class="example-text">
                                                    "Bones are deep to muscles"
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="interactive-examples">
                                <h3>Interactive Examples</h3>
                                <div class="example-scenarios">
                                    <div class="scenario clinical">
                                        <h4>Clinical Application</h4>
                                        <div class="scenario-text">
                                            <p>"The patient has a laceration on the <strong>lateral</strong> aspect of the <strong>distal</strong> forearm, <strong>superficial</strong> to the muscle layer."</p>
                                        </div>
                                        <div class="scenario-breakdown">
                                            <ul>
                                                <li><strong>Lateral:</strong> On the outer side</li>
                                                <li><strong>Distal:</strong> Away from the shoulder</li>
                                                <li><strong>Superficial:</strong> Close to the skin surface</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 4: Body Planes -->
            <div class="lecture-slide" data-slide="4">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2 class="slide-title">Body Planes and Sections</h2>
                        <p class="slide-subtitle">Imaginary Cutting Planes</p>
                    </div>
                    <div class="slide-body">
                        <div class="body-planes">
                            <div class="planes-demonstration">
                                <div class="plane sagittal">
                                    <h3>Sagittal Plane</h3>
                                    <div class="plane-visual">
                                        <div class="cutting-plane vertical-longitudinal">
                                            <div class="plane-line sagittal-line"></div>
                                            <div class="plane-description">
                                                <p>Divides body into left and right parts</p>
                                            </div>
                                        </div>
                                        <div class="resulting-sections">
                                            <div class="section left-section">
                                                <span>Left</span>
                                            </div>
                                            <div class="section right-section">
                                                <span>Right</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="plane-types">
                                        <div class="type midsagittal">
                                            <h4>Midsagittal (Median)</h4>
                                            <p>Divides into equal left and right halves</p>
                                        </div>
                                        <div class="type parasagittal">
                                            <h4>Parasagittal</h4>
                                            <p>Divides into unequal left and right parts</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="plane frontal">
                                    <h3>Frontal (Coronal) Plane</h3>
                                    <div class="plane-visual">
                                        <div class="cutting-plane vertical-transverse">
                                            <div class="plane-line frontal-line"></div>
                                            <div class="plane-description">
                                                <p>Divides body into anterior and posterior parts</p>
                                            </div>
                                        </div>
                                        <div class="resulting-sections">
                                            <div class="section anterior-section">
                                                <span>Anterior (Front)</span>
                                            </div>
                                            <div class="section posterior-section">
                                                <span>Posterior (Back)</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="plane-applications">
                                        <h4>Clinical Applications</h4>
                                        <ul>
                                            <li>Chest X-rays (AP view)</li>
                                            <li>Frontal brain sections</li>
                                            <li>Cardiac imaging</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="plane transverse">
                                    <h3>Transverse (Horizontal) Plane</h3>
                                    <div class="plane-visual">
                                        <div class="cutting-plane horizontal">
                                            <div class="plane-line transverse-line"></div>
                                            <div class="plane-description">
                                                <p>Divides body into superior and inferior parts</p>
                                            </div>
                                        </div>
                                        <div class="resulting-sections">
                                            <div class="section superior-section">
                                                <span>Superior (Upper)</span>
                                            </div>
                                            <div class="section inferior-section">
                                                <span>Inferior (Lower)</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="plane-applications">
                                        <h4>Clinical Applications</h4>
                                        <ul>
                                            <li>CT scans (axial slices)</li>
                                            <li>MRI cross-sections</li>
                                            <li>Anatomical cross-sections</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="oblique-planes">
                                <h3>Oblique Planes</h3>
                                <div class="oblique-description">
                                    <p>Planes that cut through the body at angles other than the three standard planes</p>
                                    <div class="oblique-examples">
                                        <div class="example medical-imaging">
                                            <h4>Medical Imaging</h4>
                                            <p>Used in specialized imaging techniques for better visualization of specific structures</p>
                                        </div>
                                        <div class="example surgical-approaches">
                                            <h4>Surgical Approaches</h4>
                                            <p>Surgical incisions may follow oblique planes for optimal access</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 5: Body Cavities -->
            <div class="lecture-slide" data-slide="5">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2 class="slide-title">Body Cavities</h2>
                        <p class="slide-subtitle">Internal Spaces and Organ Organization</p>
                    </div>
                    <div class="slide-body">
                        <div class="body-cavities">
                            <div class="cavity-system dorsal">
                                <h3>Dorsal Body Cavity</h3>
                                <div class="cavity-structure">
                                    <div class="cavity cranial">
                                        <h4>Cranial Cavity</h4>
                                        <div class="cavity-contents">
                                            <div class="organ brain">
                                                <span>Brain</span>
                                            </div>
                                            <div class="protective-layers">
                                                <div class="layer skull">Skull</div>
                                                <div class="layer meninges">Meninges</div>
                                            </div>
                                        </div>
                                        <div class="cavity-function">
                                            <p>Houses and protects the brain</p>
                                        </div>
                                    </div>
                                    
                                    <div class="cavity spinal">
                                        <h4>Spinal (Vertebral) Cavity</h4>
                                        <div class="cavity-contents">
                                            <div class="organ spinal-cord">
                                                <span>Spinal Cord</span>
                                            </div>
                                            <div class="protective-layers">
                                                <div class="layer vertebrae">Vertebrae</div>
                                                <div class="layer meninges">Meninges</div>
                                            </div>
                                        </div>
                                        <div class="cavity-function">
                                            <p>Houses and protects the spinal cord</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="cavity-system ventral">
                                <h3>Ventral Body Cavity</h3>
                                <div class="cavity-structure">
                                    <div class="cavity thoracic">
                                        <h4>Thoracic Cavity</h4>
                                        <div class="cavity-subdivisions">
                                            <div class="subdivision pleural">
                                                <h5>Pleural Cavities (2)</h5>
                                                <div class="cavity-contents">
                                                    <div class="organ lungs">
                                                        <span>Lungs</span>
                                                    </div>
                                                    <div class="membrane pleura">
                                                        <span>Pleural membranes</span>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="subdivision mediastinum">
                                                <h5>Mediastinum</h5>
                                                <div class="cavity-contents">
                                                    <div class="organ heart">Heart</div>
                                                    <div class="organ great-vessels">Great vessels</div>
                                                    <div class="organ esophagus">Esophagus</div>
                                                    <div class="organ trachea">Trachea</div>
                                                    <div class="organ thymus">Thymus</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="cavity-boundaries">
                                            <p><strong>Boundaries:</strong> Ribs, sternum, vertebrae, diaphragm</p>
                                        </div>
                                    </div>
                                    
                                    <div class="cavity abdominopelvic">
                                        <h4>Abdominopelvic Cavity</h4>
                                        <div class="cavity-subdivisions">
                                            <div class="subdivision abdominal">
                                                <h5>Abdominal Cavity</h5>
                                                <div class="cavity-contents">
                                                    <div class="organ stomach">Stomach</div>
                                                    <div class="organ liver">Liver</div>
                                                    <div class="organ intestines">Intestines</div>
                                                    <div class="organ kidneys">Kidneys</div>
                                                    <div class="organ spleen">Spleen</div>
                                                    <div class="organ pancreas">Pancreas</div>
                                                </div>
                                            </div>
                                            
                                            <div class="subdivision pelvic">
                                                <h5>Pelvic Cavity</h5>
                                                <div class="cavity-contents">
                                                    <div class="organ bladder">Bladder</div>
                                                    <div class="organ reproductive">Reproductive organs</div>
                                                    <div class="organ rectum">Rectum</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="cavity-membranes">
                                            <h5>Serous Membranes</h5>
                                            <div class="membrane peritoneum">
                                                <span>Peritoneum</span>
                                                <p>Lines abdominopelvic cavity and covers organs</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="serous-membranes">
                                <h3>Serous Membranes</h3>
                                <div class="membrane-structure">
                                    <div class="membrane-layer parietal">
                                        <h4>Parietal Layer</h4>
                                        <p>Lines the cavity wall</p>
                                    </div>
                                    <div class="membrane-layer visceral">
                                        <h4>Visceral Layer</h4>
                                        <p>Covers the organs</p>
                                    </div>
                                    <div class="serous-fluid">
                                        <h4>Serous Fluid</h4>
                                        <p>Lubricates between layers, reduces friction</p>
                                    </div>
                                </div>
                                
                                <div class="membrane-examples">
                                    <div class="example pleura">
                                        <h4>Pleura</h4>
                                        <p>Surrounds lungs in thoracic cavity</p>
                                    </div>
                                    <div class="example pericardium">
                                        <h4>Pericardium</h4>
                                        <p>Surrounds heart in mediastinum</p>
                                    </div>
                                    <div class="example peritoneum">
                                        <h4>Peritoneum</h4>
                                        <p>Lines abdominopelvic cavity</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </main>

    <!-- Lecture Controls -->
    <div class="lecture-controls">
        <button class="control-btn" id="prevBtn">
            <i class="fas fa-chevron-left"></i> Previous
        </button>
        <div class="slide-indicators" id="slideIndicators">
            <!-- Dynamically generated -->
        </div>
        <button class="control-btn" id="nextBtn">
            Next <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <!-- Lecture Sidebar -->
    <div class="lecture-sidebar" id="lectureSidebar">
        <div class="sidebar-header">
            <h3>Lecture Outline</h3>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        <div class="sidebar-content">
            <ul class="lecture-outline">
                <li class="outline-item active" data-slide="1">
                    <span class="outline-number">1</span>
                    <span class="outline-title">Introduction</span>
                </li>
                <li class="outline-item" data-slide="2">
                    <span class="outline-number">2</span>
                    <span class="outline-title">Anatomical Position</span>
                </li>
                <li class="outline-item" data-slide="3">
                    <span class="outline-number">3</span>
                    <span class="outline-title">Directional Terms</span>
                </li>
                <li class="outline-item" data-slide="4">
                    <span class="outline-number">4</span>
                    <span class="outline-title">Body Planes</span>
                </li>
                <li class="outline-item" data-slide="5">
                    <span class="outline-number">5</span>
                    <span class="outline-title">Body Cavities</span>
                </li>
            </ul>
        </div>
    </div>

    <!-- Author Information -->
    <div class="lecture-footer">
        <div class="author-info">
            <p><strong>Dr. Mohammed Yagoub Esmail</strong> | SUST BME @2025</p>
            <p><EMAIL> | +249912867327 | +966538076790</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../js/main.js"></script>
    <script src="../js/lecture.js"></script>
</body>
</html>
