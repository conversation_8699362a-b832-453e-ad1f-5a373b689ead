
import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  imageUrl?: string;
  imageAlt?: string;
}

const Card: React.FC<CardProps> = ({ children, className = '', title, imageUrl, imageAlt = 'Card image' }) => {
  return (
    <div className={`bg-white shadow-xl rounded-xl overflow-hidden transform hover:scale-105 transition-transform duration-300 ${className}`}>
      {imageUrl && (
        <img src={imageUrl} alt={imageAlt} className="w-full h-48 object-cover" />
      )}
      <div className="p-6">
        {title && (
          <h3 className="text-2xl font-semibold text-primary mb-3">{title}</h3>
        )}
        <div className="text-textlight/90 leading-relaxed">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Card;
