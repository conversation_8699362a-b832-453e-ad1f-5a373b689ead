<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anatomical Directional Terms</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
        }

        .container {
            max-width: 1000px;
            margin: auto;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        h1, h2, h3 {
            text-align: center;
            color: #333;
        }

        .main-content {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }

        .illustration-column {
            flex: 1;
            min-width: 250px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        #figure-container {
            width: 100%;
            max-width: 250px; /* Max width for the SVG container */
            margin: 0 auto;
            position: relative;
        }

        #anatomicalFigure {
            width: 100%;
            height: auto;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }

        .figure-caption {
            text-align: center;
            font-style: italic;
            color: #555;
            min-height: 20px; /* Reserve space for caption */
            margin-top: 10px;
        }

        .controls-column {
            flex: 1;
            min-width: 250px;
        }

        #term-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .term-btn {
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            font-size: 0.9em;
        }

        .term-btn:hover {
            background-color: #0056b3;
        }

        .term-btn.active {
            background-color: #004085;
            box-shadow: 0 0 5px rgba(0,0,0,0.3) inset;
        }

        .term-description {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            min-height: 40px; /* Reserve space */
            font-size: 0.9em;
        }

        .quiz-section {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        #question {
            font-weight: bold;
            margin-bottom: 15px;
        }

        #options-container button {
            display: block;
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            border-radius: 5px;
            cursor: pointer;
            text-align: left;
        }

        #options-container button:hover:not(:disabled) {
            background-color: #e0e0e0;
        }
        #options-container button:disabled {
            cursor: not-allowed;
        }

        #feedback {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
        }

        .feedback-correct {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .feedback-incorrect {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        #next-question-btn, #restart-quiz-btn {
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 15px;
            display: block; /* Make it block to center with margin auto if needed */
            margin-left: auto;
            margin-right: auto;
        }
        #next-question-btn:hover, #restart-quiz-btn:hover {
            background-color: #218838;
        }

        #score-container {
            text-align: center;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            #term-buttons {
                grid-template-columns: 1fr; /* Stack buttons on smaller screens */
            }
        }

        /* SVG specific styles */
        .highlight-overlay {
            pointer-events: none; /* So they don't interfere with base element clicks if any */
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>Directional Terms in Anatomy</h1>
        <p style="text-align: center;">Learn how to describe relative locations of body parts using anatomical directional terms.</p>

        <div class="main-content">
            <div class="illustration-column">
                <h2>Anatomical Position</h2>
                <div id="figure-container">
                    <svg id="anatomicalFigure" viewBox="0 0 100 200">
                        <!-- Base Body -->
                        <rect x="40" y="30" width="20" height="50" fill="#ddd" stroke="black" id="torso"/>
                        <circle cx="50" cy="20" r="10" fill="#ddd" stroke="black" id="head"/>
                        <!-- Arms -->
                        <line x1="40" y1="40" x2="25" y2="60" stroke="black" stroke-width="5" id="leftUpperArm"/>
                        <line x1="25" y1="60" x2="15" y2="90" stroke="black" stroke-width="5" id="leftLowerArm"/>
                        <circle cx="15" cy="90" r="4" fill="#ddd" stroke="black"/> <!-- Left hand -->
                        <line x1="60" y1="40" x2="75" y2="60" stroke="black" stroke-width="5" id="rightUpperArm"/>
                        <line x1="75" y1="60" x2="85" y2="90" stroke="black" stroke-width="5" id="rightLowerArm"/>
                        <circle cx="85" cy="90" r="4" fill="#ddd" stroke="black"/> <!-- Right hand -->
                        <!-- Legs -->
                        <line x1="45" y1="80" x2="35" y2="120" stroke="black" stroke-width="6" id="leftUpperLeg"/>
                        <line x1="35" y1="120" x2="30" y2="160" stroke="black" stroke-width="6" id="leftLowerLeg"/>
                        <ellipse cx="30" cy="160" rx="5" ry="3" fill="#ddd" stroke="black"/> <!-- Left foot -->
                        <line x1="55" y1="80" x2="65" y2="120" stroke="black" stroke-width="6" id="rightUpperLeg"/>
                        <line x1="65" y1="120" x2="70" y2="160" stroke="black" stroke-width="6" id="rightLowerLeg"/>
                        <ellipse cx="70" cy="160" rx="5" ry="3" fill="#ddd" stroke="black"/> <!-- Right foot -->

                        <!-- Highlight Overlays (initially hidden) -->
                        <g id="highlight-superior" class="highlight-overlay" style="display:none;">
                            <circle cx="50" cy="20" r="12" fill="rgba(255,0,0,0.5)"/>
                        </g>
                        <g id="highlight-inferior" class="highlight-overlay" style="display:none;">
                            <rect x="25" y="118" width="10" height="45" fill="rgba(255,255,0,0.5)"/> <!-- Left lower leg + foot -->
                            <rect x="65" y="118" width="10" height="45" fill="rgba(255,255,0,0.5)"/> <!-- Right lower leg + foot -->
                        </g>
                         <g id="highlight-anterior" class="highlight-overlay" style="display:none;">
                            <rect x="10" y="8" width="80" height="160" fill="rgba(0,0,255,0.3)"/>
                        </g>
                        <g id="highlight-posterior" class="highlight-overlay" style="display:none;">
                            <rect x="10" y="8" width="80" height="160" fill="rgba(0,128,0,0.3)"/>
                        </g>
                        <g id="highlight-medial" class="highlight-overlay" style="display:none;">
                            <line x1="50" y1="10" x2="50" y2="80" stroke="rgba(0,255,255,0.7)" stroke-width="4"/>
                        </g>
                        <g id="highlight-lateral" class="highlight-overlay" style="display:none;">
                            <rect x="10" y="35" width="15" height="60" fill="rgba(255,0,255,0.4)"/> <!-- Left arm area -->
                            <rect x="75" y="35" width="15" height="60" fill="rgba(255,0,255,0.4)"/> <!-- Right arm area -->
                            <rect x="35" y="30" width="5" height="50" fill="rgba(255,0,255,0.4)"/> <!-- Left torso side -->
                            <rect x="60" y="30" width="5" height="50" fill="rgba(255,0,255,0.4)"/> <!-- Right torso side -->
                            <rect x="28" y="78" width="7" height="85" fill="rgba(255,0,255,0.4)"/> <!-- Left leg outer area -->
                            <rect x="65" y="78" width="7" height="85" fill="rgba(255,0,255,0.4)"/> <!-- Right leg outer area -->
                        </g>
                        <g id="highlight-proximal" class="highlight-overlay" style="display:none;">
                            <!-- Adjusted rects to better cover line segments for arms/legs -->
                            <rect x="24" y="37" width="17" height="25" fill="rgba(128,0,128,0.5)"/> <!-- Left upper arm -->
                            <rect x="59" y="37" width="17" height="25" fill="rgba(128,0,128,0.5)"/> <!-- Right upper arm -->
                            <rect x="34" y="77" width="12" height="44" fill="rgba(128,0,128,0.5)"/> <!-- Left upper leg -->
                            <rect x="54" y="77" width="12" height="44" fill="rgba(128,0,128,0.5)"/> <!-- Right upper leg -->
                        </g>
                        <g id="highlight-distal" class="highlight-overlay" style="display:none;">
                            <rect x="12" y="57" width="14" height="37" fill="rgba(255,165,0,0.5)"/> <!-- Left lower arm/hand -->
                            <rect x="74" y="57" width="14" height="37" fill="rgba(255,165,0,0.5)"/> <!-- Right lower arm/hand -->
                            <rect x="27" y="117" width="10" height="47" fill="rgba(255,165,0,0.5)"/> <!-- Left lower leg/foot -->
                            <rect x="63" y="117" width M="10" height="47" fill="rgba(255,165,0,0.5)"/> <!-- Right lower leg/foot -->
                        </g>
                    </svg>
                    <p id="figure-caption" class="figure-caption">Body in anatomical position (erect, facing forward, arms at sides, palms forward).</p>
                </div>
            </div>

            <div class="controls-column">
                <h2>Select a Term</h2>
                <p>Click a term to see it highlighted on the figure and read its definition.</p>
                <div id="term-buttons">
                    <button class="term-btn" data-term="superior">Superior / Cranial</button>
                    <button class="term-btn" data-term="inferior">Inferior / Caudal</button>
                    <button class="term-btn" data-term="anterior">Anterior / Ventral</button>
                    <button class="term-btn" data-term="posterior">Posterior / Dorsal</button>
                    <button class="term-btn" data-term="medial">Medial</button>
                    <button class="term-btn" data-term="lateral">Lateral</button>
                    <button class="term-btn" data-term="proximal">Proximal</button>
                    <button class="term-btn" data-term="distal">Distal</button>
                </div>
                <div id="term-description" class="term-description">Select a term above.</div>
            </div>
        </div>

        <div class="quiz-section">
            <h2>Test Your Knowledge!</h2>
            <div id="quiz-container">
                <p id="question"></p>
                <div id="options-container"></div>
                <p id="feedback"></p>
                <button id="next-question-btn" style="display:none;">Next Question</button>
            </div>
            <div id="score-container" style="display:none;">
                <h3>Quiz Finished!</h3>
                <p id="final-score"></p>
                <button id="restart-quiz-btn">Restart Quiz</button>
            </div>
        </div>
    </div>

    <script>
        const termButtons = document.querySelectorAll('.term-btn');
        const termDescription = document.getElementById('term-description');
        const figureCaption = document.getElementById('figure-caption');
        const highlightOverlays = document.querySelectorAll('.highlight-overlay');

        const termDefinitions = {
            superior: "<strong>Superior (Cranial):</strong> Toward the head end or upper part of a structure or the body; above.",
            inferior: "<strong>Inferior (Caudal):</strong> Away from the head end or toward the lower part of a structure or the body; below.",
            anterior: "<strong>Anterior (Ventral):</strong> Toward or at the front of the body; in front of.",
            posterior: "<strong>Posterior (Dorsal):</strong> Toward or at the back of the body; behind.",
            medial: "<strong>Medial:</strong> Toward or at the midline of the body; on the inner side of.",
            lateral: "<strong>Lateral:</strong> Away from the midline of the body; on the outer side of.",
            proximal: "<strong>Proximal:</strong> Closer to the origin of the body part or the point of attachment of a limb to the body trunk.",
            distal: "<strong>Distal:</strong> Farther from the origin of a body part or the point of attachment of a limb to the body trunk."
        };

        termButtons.forEach(button => {
            button.addEventListener('click', () => {
                const term = button.dataset.term;

                // Update button active state
                termButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                // Update term description
                termDescription.innerHTML = termDefinitions[term];

                // Hide all overlays
                highlightOverlays.forEach(overlay => overlay.style.display = 'none');

                // Show selected overlay
                const selectedOverlay = document.getElementById(`highlight-${term}`);
                if (selectedOverlay) {
                    selectedOverlay.style.display = 'block';
                }

                // Update figure caption for specific cases
                if (term === 'anterior') {
                    figureCaption.textContent = "Anterior view. Highlight shows the front surface.";
                } else if (term === 'posterior') {
                    figureCaption.textContent = "Anterior view. Highlight represents the back surface (Posterior/Dorsal).";
                } else {
                    figureCaption.textContent = "Body in anatomical position.";
                }
            });
        });

        // Quiz Logic
        const quizData = [
            {
                question: "The eyes are ______ to the brain.",
                options: ["Anterior", "Posterior", "Medial", "Lateral"],
                correctAnswer: "Anterior",
                explanation: "Correct! The eyes are located towards the front (anterior) of the head relative to the brain."
            },
            {
                question: "The shoulders are ______ to the heart.",
                options: ["Superior", "Inferior", "Distal", "Proximal"],
                correctAnswer: "Superior",
                explanation: "Correct! The shoulders are located above (superior) the heart. They are also lateral to the heart."
            },
            {
                question: "The toes are ______ to the knees.",
                options: ["Anterior", "Posterior", "Distal", "Proximal"],
                correctAnswer: "Distal",
                explanation: "Correct! The toes are further away from the point of attachment of the leg (distal) compared to the knees."
            },
            {
                question: "The sternum (breastbone) is ______ to the vertebral column (spine).",
                options: ["Anterior", "Posterior", "Superior", "Inferior"],
                correctAnswer: "Anterior",
                explanation: "Correct! The sternum is on the front side (anterior) of the body, while the spine is on the back side (posterior)."
            },
            {
                question: "The elbow is ______ to the wrist.",
                options: ["Medial", "Lateral", "Distal", "Proximal"],
                correctAnswer: "Proximal",
                explanation: "Correct! The elbow is closer to the point of attachment of the arm (proximal) compared to the wrist."
            }
        ];

        const questionEl = document.getElementById('question');
        const optionsContainer = document.getElementById('options-container');
        const feedbackEl = document.getElementById('feedback');
        const nextQuestionBtn = document.getElementById('next-question-btn');
        const quizContainer = document.getElementById('quiz-container');
        const scoreContainer = document.getElementById('score-container');
        const finalScoreEl = document.getElementById('final-score');
        const restartQuizBtn = document.getElementById('restart-quiz-btn');

        let currentQuestionIndex = 0;
        let score = 0;

        function loadQuestion() {
            feedbackEl.textContent = '';
            feedbackEl.className = '';
            nextQuestionBtn.style.display = 'none';

            const currentQuestion = quizData[currentQuestionIndex];
            questionEl.textContent = currentQuestion.question;
            optionsContainer.innerHTML = '';

            currentQuestion.options.forEach(option => {
                const button = document.createElement('button');
                button.textContent = option;
                button.onclick = () => selectAnswer(option, currentQuestion.correctAnswer, currentQuestion.explanation);
                optionsContainer.appendChild(button);
            });
        }

        function selectAnswer(selectedOption, correctAnswer, explanation) {
            // Disable all option buttons
            const optionButtons = optionsContainer.querySelectorAll('button');
            optionButtons.forEach(btn => btn.disabled = true);

            if (selectedOption === correctAnswer) {
                score++;
                feedbackEl.textContent = explanation;
                feedbackEl.className = 'feedback-correct';
            } else {
                feedbackEl.textContent = `Incorrect. The correct answer is ${correctAnswer}. Why? ${explanation.replace("Correct! ","")}`;
                feedbackEl.className = 'feedback-incorrect';
            }
            
            if (currentQuestionIndex < quizData.length - 1) {
                nextQuestionBtn.textContent = 'Next Question';
            } else {
                nextQuestionBtn.textContent = 'Finish Quiz';
            }
            nextQuestionBtn.style.display = 'block';
        }

        nextQuestionBtn.addEventListener('click', () => {
            currentQuestionIndex++;
            if (currentQuestionIndex < quizData.length) {
                loadQuestion();
            } else {
                showScore();
            }
        });

        function showScore() {
            quizContainer.style.display = 'none';
            scoreContainer.style.display = 'block';
            finalScoreEl.textContent = `Your score: ${score} out of ${quizData.length}`;
        }

        restartQuizBtn.addEventListener('click', () => {
            currentQuestionIndex = 0;
            score = 0;
            scoreContainer.style.display = 'none';
            quizContainer.style.display = 'block';
            loadQuestion();
        });

        // Initial load
        loadQuestion();

    </script>
</body>
</html>
