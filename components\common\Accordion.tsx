
import React, { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon } from '../icons/ChevronIcons'; // Assume these exist

interface AccordionItemProps {
  title: string;
  children: React.ReactNode;
  isOpenInitially?: boolean;
}

const AccordionItem: React.FC<AccordionItemProps> = ({ title, children, isOpenInitially = false }) => {
  const [isOpen, setIsOpen] = useState(isOpenInitially);

  return (
    <div className="border-b border-gray-200 last:border-b-0">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex justify-between items-center w-full py-4 px-2 text-left text-lg font-medium text-primary hover:bg-accent/20 transition-colors duration-200"
        aria-expanded={isOpen}
      >
        <span>{title}</span>
        {isOpen ? <ChevronUpIcon className="w-5 h-5 text-secondary" /> : <ChevronDownIcon className="w-5 h-5 text-secondary" />}
      </button>
      {isOpen && (
        <div className="p-4 bg-white text-textlight/90 animate-fadeInUp">
          {children}
        </div>
      )}
    </div>
  );
};


interface AccordionProps {
    items: Array<{ title: string; content: React.ReactNode }>;
}
  
const Accordion: React.FC<AccordionProps> = ({ items }) => {
    return (
        <div className="bg-white/50 backdrop-blur-sm shadow-lg rounded-lg">
        {items.map((item, index) => (
            <AccordionItem key={index} title={item.title} isOpenInitially={index === 0}>
            {item.content}
            </AccordionItem>
        ))}
        </div>
    );
};

export default Accordion;
