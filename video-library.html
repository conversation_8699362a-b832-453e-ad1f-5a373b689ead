<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Library - Interactive A&P for BME</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/animations.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation Header -->
    <header class="main-header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <i class="fas fa-heartbeat"></i>
                    <span>A&P for BME</span>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="index.html#modules" class="nav-link">Learning Modules</a></li>
                    <li><a href="index.html#simulations" class="nav-link">Simulations</a></li>
                    <li><a href="#videos" class="nav-link active">Video Library</a></li>
                    <li><a href="index.html#about" class="nav-link">About</a></li>
                </ul>
                <div class="nav-actions">
                    <button class="nav-sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="hamburger">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Navigation Sidebar -->
    <div class="nav-sidebar" id="navSidebar">
        <div class="sidebar-header">
            <h3>Quick Navigation</h3>
            <button class="sidebar-close" id="sidebarClose">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="sidebar-content">
            <div class="sidebar-section">
                <h4>Learning Modules</h4>
                <ul class="sidebar-links">
                    <li><a href="Interactive Anatomical Terms.html"><i class="fas fa-microscope"></i> Anatomical Terms</a></li>
                    <li><a href="Human Anatomy Explorer.html"><i class="fas fa-user-md"></i> Anatomy Explorer</a></li>
                    <li><a href="Homeostasis Simulator .html"><i class="fas fa-balance-scale"></i> Homeostasis Simulator</a></li>
                    <li><a href="Interactive Sliding Filament Model.html"><i class="fas fa-dumbbell"></i> Muscle Contraction</a></li>
                    <li><a href="Bone System.html"><i class="fas fa-bone"></i> Skeletal System</a></li>
                    <li><a href="Electrophysiology Signal Generation.html"><i class="fas fa-bolt"></i> Electrophysiology</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Hero Section -->
    <section class="hero-section" style="min-height: 60vh; padding-top: 120px;">
        <div class="hero-content" style="text-align: center;">
            <div class="hero-text">
                <h1 class="hero-title">Video Library</h1>
                <h2 class="hero-subtitle">Educational Videos for Biomedical Engineers</h2>
                <p class="hero-description">
                    Comprehensive collection of animated videos covering anatomy, physiology, and biomedical engineering concepts.
                </p>
            </div>
        </div>
    </section>

    <!-- Video Filter Section -->
    <section class="video-filter-section" style="padding: 2rem 0; background: var(--surface-color);">
        <div class="container">
            <div class="filter-controls">
                <h3>Filter Videos by Category</h3>
                <div class="video-filter-buttons">
                    <button type="button" class="filter-btn active" data-filter="all">All Videos</button>
                    <button type="button" class="filter-btn" data-filter="cell-biology">Cell Biology</button>
                    <button type="button" class="filter-btn" data-filter="physiology">Physiology</button>
                    <button type="button" class="filter-btn" data-filter="muscle-system">Muscle System</button>
                    <button type="button" class="filter-btn" data-filter="nervous-system">Nervous System</button>
                    <button type="button" class="filter-btn" data-filter="cardiovascular">Cardiovascular</button>
                    <button type="button" class="filter-btn" data-filter="respiratory">Respiratory</button>
                    <button type="button" class="filter-btn" data-filter="electrophysiology">Electrophysiology</button>
                    <button type="button" class="filter-btn" data-filter="anatomy">Anatomy</button>
                    <button type="button" class="filter-btn" data-filter="bme-applications">BME Applications</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Complete Video Library -->
    <section id="videos" class="videos-section">
        <div class="container">
            <h2 class="section-title">Complete Video Collection</h2>
            <p class="section-subtitle">Interactive learning through high-quality educational videos</p>
            
            <div class="videos-grid" id="videoGrid">
                <!-- Cell Biology Videos -->
                <div class="video-card" data-category="cell-biology" data-video-id="cell-structure" data-video-url="https://www.youtube.com/embed/8IlzKri08kk">
                    <div class="video-thumbnail">
                        <img src="https://img.youtube.com/vi/8IlzKri08kk/maxresdefault.jpg" alt="Cell Structure and Function">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">8:45</div>
                    </div>
                    <div class="video-info">
                        <h3>Cell Structure and Function</h3>
                        <p>Explore the fundamental unit of life and understand cellular organelles and their functions.</p>
                        <div class="video-meta">
                            <span class="video-category">Cell Biology</span>
                            <span class="video-views">1.2K views</span>
                        </div>
                    </div>
                </div>

                <!-- Physiology Videos -->
                <div class="video-card" data-category="physiology" data-video-id="homeostasis" data-video-url="https://www.youtube.com/embed/O_DW7IfKjdE">
                    <div class="video-thumbnail">
                        <img src="https://img.youtube.com/vi/O_DW7IfKjdE/maxresdefault.jpg" alt="Homeostasis Mechanisms">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">12:30</div>
                    </div>
                    <div class="video-info">
                        <h3>Homeostasis Mechanisms</h3>
                        <p>Learn how the body maintains internal balance through negative and positive feedback loops.</p>
                        <div class="video-meta">
                            <span class="video-category">Physiology</span>
                            <span class="video-views">856 views</span>
                        </div>
                    </div>
                </div>

                <!-- Muscle System Videos -->
                <div class="video-card" data-category="muscle-system" data-video-id="muscle-contraction" data-video-url="https://www.youtube.com/embed/zopoN3T3SJM">
                    <div class="video-thumbnail">
                        <img src="https://img.youtube.com/vi/zopoN3T3SJM/maxresdefault.jpg" alt="Muscle Contraction">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">15:20</div>
                    </div>
                    <div class="video-info">
                        <h3>Muscle Contraction Animation</h3>
                        <p>Detailed animation of the sliding filament model and cross-bridge cycling in muscle fibers.</p>
                        <div class="video-meta">
                            <span class="video-category">Muscle System</span>
                            <span class="video-views">2.1K views</span>
                        </div>
                    </div>
                </div>

                <!-- Nervous System Videos -->
                <div class="video-card" data-category="nervous-system" data-video-id="nervous-system" data-video-url="https://www.youtube.com/embed/44B0ms3XPVE">
                    <div class="video-thumbnail">
                        <img src="https://img.youtube.com/vi/44B0ms3XPVE/maxresdefault.jpg" alt="Nervous System">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">18:45</div>
                    </div>
                    <div class="video-info">
                        <h3>Nervous System Overview</h3>
                        <p>Comprehensive overview of the central and peripheral nervous systems and signal transmission.</p>
                        <div class="video-meta">
                            <span class="video-category">Nervous System</span>
                            <span class="video-views">1.8K views</span>
                        </div>
                    </div>
                </div>

                <!-- Cardiovascular Videos -->
                <div class="video-card" data-category="cardiovascular" data-video-id="cardiovascular" data-video-url="https://www.youtube.com/embed/H04d3rJCLCE">
                    <div class="video-thumbnail">
                        <img src="https://img.youtube.com/vi/H04d3rJCLCE/maxresdefault.jpg" alt="Cardiovascular System">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">14:15</div>
                    </div>
                    <div class="video-info">
                        <h3>Cardiovascular System</h3>
                        <p>Journey through the heart and blood vessels, understanding circulation and cardiac function.</p>
                        <div class="video-meta">
                            <span class="video-category">Cardiovascular</span>
                            <span class="video-views">1.5K views</span>
                        </div>
                    </div>
                </div>

                <!-- Respiratory Videos -->
                <div class="video-card" data-category="respiratory" data-video-id="respiratory" data-video-url="https://www.youtube.com/embed/SPGRkexI_cs">
                    <div class="video-thumbnail">
                        <img src="https://img.youtube.com/vi/SPGRkexI_cs/maxresdefault.jpg" alt="Respiratory System">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">11:30</div>
                    </div>
                    <div class="video-info">
                        <h3>Respiratory System Mechanics</h3>
                        <p>Understand breathing mechanics, gas exchange, and respiratory control mechanisms.</p>
                        <div class="video-meta">
                            <span class="video-category">Respiratory</span>
                            <span class="video-views">1.1K views</span>
                        </div>
                    </div>
                </div>

                <!-- Electrophysiology Videos -->
                <div class="video-card" data-category="electrophysiology" data-video-id="electrophysiology" data-video-url="https://www.youtube.com/embed/oa6rvUJlg7o">
                    <div class="video-thumbnail">
                        <img src="https://img.youtube.com/vi/oa6rvUJlg7o/maxresdefault.jpg" alt="Action Potential">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">16:22</div>
                    </div>
                    <div class="video-info">
                        <h3>Action Potential & Electrophysiology</h3>
                        <p>Deep dive into membrane potential, ion channels, and electrical signal generation in neurons.</p>
                        <div class="video-meta">
                            <span class="video-category">Electrophysiology</span>
                            <span class="video-views">3.2K views</span>
                        </div>
                    </div>
                </div>

                <!-- Anatomy Videos -->
                <div class="video-card" data-category="anatomy" data-video-id="anatomy-overview" data-video-url="https://www.youtube.com/embed/z9HIYjRRaDE">
                    <div class="video-thumbnail">
                        <img src="https://img.youtube.com/vi/z9HIYjRRaDE/maxresdefault.jpg" alt="Human Anatomy Overview">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">22:15</div>
                    </div>
                    <div class="video-info">
                        <h3>Human Anatomy Overview</h3>
                        <p>Comprehensive introduction to human body systems and anatomical organization.</p>
                        <div class="video-meta">
                            <span class="video-category">Anatomy</span>
                            <span class="video-views">4.8K views</span>
                        </div>
                    </div>
                </div>

                <!-- BME Applications Videos -->
                <div class="video-card" data-category="bme-applications" data-video-id="biomedical-engineering" data-video-url="https://www.youtube.com/embed/lGYJyur8nxo">
                    <div class="video-thumbnail">
                        <img src="https://img.youtube.com/vi/lGYJyur8nxo/maxresdefault.jpg" alt="Biomedical Engineering">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">19:45</div>
                    </div>
                    <div class="video-info">
                        <h3>Biomedical Engineering Applications</h3>
                        <p>Explore how engineering principles apply to biological systems and medical devices.</p>
                        <div class="video-meta">
                            <span class="video-category">BME Applications</span>
                            <span class="video-views">2.7K views</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-heartbeat"></i>
                        <span>A&P for BME</span>
                    </div>
                    <p>Interactive Anatomy & Physiology learning platform designed for biomedical engineering students.</p>
                    <div class="author-info">
                        <h4><i class="fas fa-user-graduate"></i> Author</h4>
                        <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                        <p>SUST BME @2025</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <div class="contact-phones">
                            <p><i class="fas fa-phone"></i> +249912867327</p>
                            <p><i class="fas fa-phone"></i> +966538076790</p>
                        </div>
                    </div>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html#modules">Learning Modules</a></li>
                        <li><a href="index.html#simulations">Simulations</a></li>
                        <li><a href="video-library.html">Video Library</a></li>
                        <li><a href="index.html#about">About</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Video Categories</h3>
                    <ul>
                        <li><a href="#" data-filter="cell-biology">Cell Biology</a></li>
                        <li><a href="#" data-filter="physiology">Physiology</a></li>
                        <li><a href="#" data-filter="electrophysiology">Electrophysiology</a></li>
                        <li><a href="#" data-filter="anatomy">Anatomy</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Information</h3>
                    <ul>
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                        <li><i class="fas fa-phone"></i> +249912867327</li>
                        <li><i class="fas fa-phone"></i> +966538076790</li>
                        <li><i class="fas fa-university"></i> SUST BME Department</li>
                        <li><i class="fas fa-calendar"></i> 2025</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="copyright-info">
                    <p>&copy; 2025 Interactive Anatomy & Physiology for BME. All rights reserved.</p>
                    <p><strong>Author:</strong> Dr. Mohammed Yagoub Esmail | <strong>SUST BME @2025</strong></p>
                    <p><strong>Copyright:</strong> <EMAIL></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script>
        // Video Library Specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize video filtering
            initVideoFiltering();
        });

        function initVideoFiltering() {
            const filterButtons = document.querySelectorAll('.video-filter-buttons .filter-btn');
            const videoCards = document.querySelectorAll('.video-card');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');

                    // Update active button
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // Filter videos
                    videoCards.forEach(card => {
                        const category = card.getAttribute('data-category');
                        if (filter === 'all' || category === filter) {
                            card.style.display = 'block';
                            card.style.animation = 'fadeInUp 0.5s ease-out';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });

            // Footer filter links
            const footerFilterLinks = document.querySelectorAll('.footer-section a[data-filter]');
            footerFilterLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const filter = this.getAttribute('data-filter');

                    // Scroll to videos section
                    document.getElementById('videos').scrollIntoView({ behavior: 'smooth' });

                    // Apply filter
                    filterButtons.forEach(btn => {
                        btn.classList.remove('active');
                        if (btn.getAttribute('data-filter') === filter) {
                            btn.classList.add('active');
                        }
                    });

                    // Filter videos
                    videoCards.forEach(card => {
                        const category = card.getAttribute('data-category');
                        if (category === filter) {
                            card.style.display = 'block';
                            card.style.animation = 'fadeInUp 0.5s ease-out';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });
        }
    </script>
</body>
</html>
